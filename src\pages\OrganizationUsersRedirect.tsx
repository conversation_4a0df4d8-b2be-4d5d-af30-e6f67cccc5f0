import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

// This component redirects from the old /organization/users path to the new /organization/members path
const OrganizationUsersRedirect = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Redirect to the new path
    navigate('/organization/members', { replace: true });
  }, [navigate]);

  return null; // This component doesn't render anything
};

export default OrganizationUsersRedirect;
