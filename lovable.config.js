module.exports = {
  // Project configuration
  project: {
    name: "ClassTasker",
    description: "A task management system for schools and multi-academy trusts",
  },

  // Environment variables for the application
  // SECURITY: Only include public environment variables here
  env: {
    // Supabase configuration (anon key is safe for client-side)
    VITE_SUPABASE_URL: process.env.VITE_SUPABASE_URL,
    VITE_SUPABASE_ANON_KEY: process.env.VITE_SUPABASE_ANON_KEY,
  },

  // Build configuration
  build: {
    // Command to build the application
    command: "npm run build",
    // Output directory
    outputDir: "dist",
  },

  // Deployment configuration
  deploy: {
    // Static site settings
    static: {
      // Directory to deploy
      dir: "dist",
    },
  },
};
