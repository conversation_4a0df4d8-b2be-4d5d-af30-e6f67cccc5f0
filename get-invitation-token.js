import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase URL or service role key. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function getInvitationTokens() {
  try {
    // Get all pending invitations
    const { data: invitations, error } = await supabase
      .from('user_invitations')
      .select('id, token, email, organization_id, role, status, created_at')
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (error) {
      console.error('Error fetching invitations:', error);
      return;
    }
    
    if (!invitations || invitations.length === 0) {
      console.log('No invitations found. Creating a test invitation...');
      return await createTestInvitation();
    }
    
    console.log('Recent invitations:');
    invitations.forEach((invitation, index) => {
      console.log(`\n[${index + 1}] Invitation Details:`);
      console.log(`Token: ${invitation.token}`);
      console.log(`Email: ${invitation.email}`);
      console.log(`Status: ${invitation.status}`);
      console.log(`Created: ${new Date(invitation.created_at).toLocaleString()}`);
    });
    
    return invitations;
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

async function createTestInvitation() {
  try {
    const testEmail = '<EMAIL>';
    
    // Get the first organization
    const { data: orgs, error: orgError } = await supabase
      .from('organizations')
      .select('id')
      .limit(1);
    
    if (orgError || !orgs || orgs.length === 0) {
      console.error('Error fetching organization or no organizations found:', orgError);
      return;
    }
    
    const organizationId = orgs[0].id;
    
    // Generate a random token
    const token = Math.random().toString(36).substring(2, 15) + 
                 Math.random().toString(36).substring(2, 15);
    
    // Create the invitation
    const { data: invitation, error } = await supabase
      .from('user_invitations')
      .insert([
        {
          email: testEmail,
          token: token,
          organization_id: organizationId,
          role: 'teacher',
          status: 'pending',
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days from now
        }
      ])
      .select()
      .single();
    
    if (error) {
      console.error('Error creating invitation:', error);
      return;
    }
    
    console.log('\nCreated new test invitation:');
    console.log(`Token: ${invitation.token}`);
    console.log(`Email: ${invitation.email}`);
    console.log(`Status: ${invitation.status}`);
    console.log(`Created: ${new Date(invitation.created_at).toLocaleString()}`);
    
    return invitation;
  } catch (error) {
    console.error('Unexpected error creating invitation:', error);
  }
}

getInvitationTokens().finally(() => process.exit(0));
