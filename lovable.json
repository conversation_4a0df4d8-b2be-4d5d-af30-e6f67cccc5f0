{"project": {"name": "ClassTasker", "description": "A task management system for schools and multi-academy trusts"}, "env": {"VITE_SUPABASE_URL": "https://qcnotlojmyvpqbbgoxbc.supabase.co", "VITE_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFjbm90bG9qbXl2cHFiYmdveGJjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwODgwMjgsImV4cCI6MjA2MzY2NDAyOH0.9lQq6ekhMnxolFlS4D7Ul4IkG0h0OsoO8MJDznwH-9U"}, "build": {"command": "npm run build", "outputDir": "dist"}, "deploy": {"static": {"dir": "dist"}}}