import { EmailConfig } from '@/types/email';
import nodemailer from 'nodemailer';

/**
 * Test SMTP connection and send a test email
 */
export async function testSmtpConnection(config: EmailConfig, testEmail: string): Promise<{ success: boolean; message: string }> {
  try {
    if (!config.smtpHost || !config.smtpPort || !config.smtpUsername || !config.smtpPassword) {
      return { 
        success: false, 
        message: 'Missing SMTP configuration. Please provide host, port, username, and password.' 
      };
    }

    if (!config.fromEmail) {
      return { 
        success: false, 
        message: 'Missing sender email address. Please provide a from email.' 
      };
    }

    // Create a test transporter
    const transporter = nodemailer.createTransport({
      host: config.smtpHost,
      port: config.smtpPort,
      secure: config.smtpSecure || false,
      auth: {
        user: config.smtpUsername,
        pass: config.smtpPassword,
      },
      debug: true, // Enable debug output
    });

    // Verify connection configuration
    try {
      await transporter.verify();
      console.log('SMTP connection verified successfully');
    } catch (verifyError: any) {
      console.error('SMTP connection verification failed:', verifyError);
      return { 
        success: false, 
        message: `SMTP connection failed: ${verifyError.message}` 
      };
    }

    // Send test email
    const info = await transporter.sendMail({
      from: `"${config.fromName || 'Classtasker'}" <${config.fromEmail}>`,
      to: testEmail,
      subject: 'Classtasker Email Test',
      text: 'This is a test email from Classtasker to verify your SMTP configuration.',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <h1 style="color: #4f46e5;">Classtasker Email Test</h1>
          <p>This is a test email from Classtasker to verify your SMTP configuration.</p>
          <p>If you're seeing this, your email configuration is working correctly!</p>
          <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
            <p style="color: #666; font-size: 12px;">This is an automated message, please do not reply.</p>
          </div>
        </div>
      `,
    });

    console.log('Test email sent:', info);
    return { 
      success: true, 
      message: `Test email sent successfully to ${testEmail}. Message ID: ${info.messageId}` 
    };
  } catch (error: any) {
    console.error('Error sending test email:', error);
    return { 
      success: false, 
      message: `Failed to send test email: ${error.message}` 
    };
  }
}
