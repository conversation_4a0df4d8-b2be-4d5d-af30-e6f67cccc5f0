// <PERSON>ript to delete all invitations from Supabase
import { createClient } from '@supabase/supabase-js';

// Create Supabase client with service role key
const supabase = createClient(
  'https://qcnotlojmyvpqbbgoxbc.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFjbm90bG9qbXl2cHFiYmdveGJjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0Mzk4NzgzMywiZXhwIjoyMDU5NTYzODMzfQ.dRifBDSPK6GNYPialLfSeIQPu88lOSIsUVkynp2Be-U'
);

async function listAndDeleteInvitations() {
  try {
    console.log('Fetching all invitations...');
    const { data, error } = await supabase
      .from('user_invitations')
      .select('*');

    if (error) {
      console.error('Error fetching invitations:', error);
      return;
    }

    console.log(`Found ${data.length} invitations:`);
    console.log(JSON.stringify(data, null, 2));

    if (data.length > 0) {
      console.log('\nDeleting all invitations...');
      const { error: deleteError } = await supabase
        .from('user_invitations')
        .delete()
        .in('id', data.map(inv => inv.id));

      if (deleteError) {
        console.error('Error deleting invitations:', deleteError);
      } else {
        console.log('Successfully deleted all invitations');
      }

      // Verify deletion
      console.log('\nVerifying deletion...');
      const { data: verifyData, error: verifyError } = await supabase
        .from('user_invitations')
        .select('*');

      if (verifyError) {
        console.error('Error verifying deletion:', verifyError);
      } else if (verifyData.length === 0) {
        console.log('Verification successful: All invitations were deleted.');
      } else {
        console.log('Verification failed: Some invitations still exist:', verifyData);
      }
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the function
listAndDeleteInvitations();
