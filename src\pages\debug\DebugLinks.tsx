import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Bug, Shield, User, Database, FileText, Smartphone } from 'lucide-react';

/**
 * Debug links component to provide quick access to debug pages
 */
const DebugLinks = () => {
  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center mb-6">
        <Bug className="h-8 w-8 text-red-500 mr-2" />
        <h1 className="text-3xl font-bold">Debug Tools</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="h-5 w-5 mr-2 text-red-500" />
              Role Debugging
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-sm text-gray-600">
              Debug user roles, permissions, and site admin status
            </p>
            <Button asChild>
              <Link to="/debug/role">Role Debug</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2 text-blue-500" />
              User Debugging
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-sm text-gray-600">
              Debug user authentication and profile data
            </p>
            <Button asChild>
              <Link to="/debug">User Debug</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2 text-green-500" />
              System Debugging
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-sm text-gray-600">
              Debug system-level functionality
            </p>
            <Button asChild>
              <Link to="/system-debug">System Debug</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2 text-purple-500" />
              Task Type Debugging
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-sm text-gray-600">
              Debug task types and the new architecture
            </p>
            <Button asChild>
              <Link to="/debug/task-types">Task Type Debug</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Smartphone className="h-5 w-5 mr-2 text-orange-500" />
              PWA Debugging
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-sm text-gray-600">
              Debug Progressive Web App functionality
            </p>
            <Button asChild>
              <Link to="/debug/pwa">PWA Debug</Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="mt-8">
        <h2 className="text-xl font-bold mb-4">Direct Access URLs</h2>
        <ul className="list-disc pl-6 space-y-2">
          <li>
            <Link to="/debug/role" className="text-blue-600 hover:underline">
              Role Debug: /debug/role
            </Link>
          </li>
          <li>
            <Link to="/admin/site" className="text-blue-600 hover:underline">
              Site Admin Dashboard: /admin/site
            </Link>
          </li>
          <li>
            <Link to="/admin/roles" className="text-blue-600 hover:underline">
              Role Management: /admin/roles
            </Link>
          </li>
          <li>
            <Link to="/access-denied" className="text-blue-600 hover:underline">
              Access Denied Page: /access-denied
            </Link>
          </li>
          <li>
            <Link to="/debug/task-types" className="text-blue-600 hover:underline">
              Task Type Debug: /debug/task-types
            </Link>
          </li>
          <li>
            <Link to="/debug/pwa" className="text-blue-600 hover:underline">
              PWA Debug: /debug/pwa
            </Link>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default DebugLinks;
