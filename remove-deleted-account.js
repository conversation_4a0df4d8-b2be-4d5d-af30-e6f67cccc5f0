// <PERSON><PERSON><PERSON> to completely remove the deleted account from the database
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

// Initialize Supabase client with service role key
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or service role key. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// The account ID to remove
const accountId = 'acct_1RG0QGB7EfXoNdhk';
const userId = '4288cd97-e3ed-4e1d-8d22-abdc0d3f28bd';

async function removeDeletedAccount() {
  try {
    console.log(`Completely removing deleted account ${accountId} from the database...`);
    
    // First, check if the account exists and is marked as deleted
    const { data: account, error: fetchError } = await supabase
      .from('stripe_accounts')
      .select('*')
      .eq('account_id', accountId)
      .single();
    
    if (fetchError) {
      console.error('Error fetching account:', fetchError);
      process.exit(1);
    }
    
    if (!account) {
      console.log(`Account ${accountId} not found in the database.`);
      process.exit(0);
    }
    
    console.log('Found account:', account);
    
    if (account.account_status !== 'deleted') {
      console.error(`Account ${accountId} is not marked as deleted. Status: ${account.account_status}`);
      process.exit(1);
    }
    
    // Delete the account from the database
    const { error: deleteError } = await supabase
      .from('stripe_accounts')
      .delete()
      .eq('account_id', accountId);
    
    if (deleteError) {
      console.error('Error deleting account from database:', deleteError);
      process.exit(1);
    }
    
    console.log(`Successfully removed account ${accountId} from the database.`);
    
    // Make sure the user's profile doesn't reference this account
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('stripe_account_id')
      .eq('id', userId)
      .single();
    
    if (profileError) {
      console.error('Error fetching user profile:', profileError);
    } else if (profile.stripe_account_id === accountId) {
      // Update the user's profile to remove the Stripe account ID
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ stripe_account_id: null })
        .eq('id', userId);
      
      if (updateError) {
        console.error('Error updating user profile:', updateError);
      } else {
        console.log(`Removed Stripe account ID from user profile ${userId}`);
      }
    } else {
      console.log(`User profile ${userId} does not reference this account.`);
    }
    
    console.log('Account removal complete.');
  } catch (error) {
    console.error('Error removing deleted account:', error);
    process.exit(1);
  }
}

// Execute the function
removeDeletedAccount();