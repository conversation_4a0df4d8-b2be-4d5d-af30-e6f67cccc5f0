{"allTasks": [{"id": "193b0803-4119-4253-83aa-258778307d0d", "title": "Public Test Task Created by <PERSON><PERSON><PERSON>", "description": "This is a public test task created by the script", "location": "Test Location", "category": "Test Category", "budget": 100, "due_date": "2025-04-13T00:22:08.763+00:00", "status": "open", "created_at": "2025-04-13T00:22:08.819929+00:00", "updated_at": "2025-04-13T00:22:08.819929+00:00", "user_id": "2818832a-c6a5-4d4f-becf-6fcb75b88764", "offers_count": 0, "assigned_to": null, "visibility": "public"}, {"id": "f242ca7f-fc24-4bcf-8fa2-1afd7eaddc5c", "title": "Test Task Created by <PERSON><PERSON><PERSON>", "description": "This is a test task created by the script", "location": "Test Location", "category": "Test Category", "budget": 100, "due_date": "2025-04-13T00:21:31.13+00:00", "status": "open", "created_at": "2025-04-13T00:21:31.18354+00:00", "updated_at": "2025-04-13T00:21:31.18354+00:00", "user_id": "2818832a-c6a5-4d4f-becf-6fcb75b88764", "offers_count": 0, "assigned_to": null, "visibility": "admin"}, {"id": "7b18a30d-7307-41cd-b48e-2a62309ef3d0", "title": "Policy Test Task", "description": "This is a test task to check if policies are working", "location": "Test Location", "category": "Test Category", "budget": 100, "due_date": "2025-04-13T00:13:37.007+00:00", "status": "open", "created_at": "2025-04-13T00:13:37.075938+00:00", "updated_at": "2025-04-13T00:13:37.075938+00:00", "user_id": "2818832a-c6a5-4d4f-becf-6fcb75b88764", "offers_count": 0, "assigned_to": null, "visibility": "admin"}, {"id": "65d7037b-7f71-4562-ab4d-141e2f99a3d7", "title": "Test Task Created by <PERSON><PERSON><PERSON>", "description": "This is a test task created by the script", "location": "Test Location", "category": "Test Category", "budget": 100, "due_date": "2025-04-13T00:06:22.707+00:00", "status": "open", "created_at": "2025-04-13T00:06:22.769806+00:00", "updated_at": "2025-04-13T00:06:22.769806+00:00", "user_id": "2818832a-c6a5-4d4f-becf-6fcb75b88764", "offers_count": 0, "assigned_to": null, "visibility": "admin"}, {"id": "0d84834f-27bf-4bf9-89fe-9e160d1bef26", "title": "external test ", "description": "external test ", "location": "school", "category": "Painting", "budget": 1.06, "due_date": "2025-04-16T23:00:00+00:00", "status": "open", "created_at": "2025-04-12T23:18:35.526577+00:00", "updated_at": "2025-04-12T23:18:35.526577+00:00", "user_id": "dfaf494b-a559-4191-872b-5b0ec8b9d613", "offers_count": 0, "assigned_to": null, "visibility": "public"}, {"id": "9417a63c-6f4b-42ea-b7fa-25cebc055ac9", "title": "test assignment internal ", "description": "assign internally ", "location": "school", "category": "Painting", "budget": 1.06, "due_date": "2025-04-16T23:00:00+00:00", "status": "open", "created_at": "2025-04-12T23:18:04.66253+00:00", "updated_at": "2025-04-12T23:18:04.66253+00:00", "user_id": "dfaf494b-a559-4191-872b-5b0ec8b9d613", "offers_count": 0, "assigned_to": null, "visibility": "public"}, {"id": "87cf8056-9c9d-4ee0-bd87-2936bf9e3a5d", "title": "fix window", "description": "fix broken window", "location": "school", "category": "Electrical", "budget": 1.06, "due_date": "2025-04-17T23:00:00+00:00", "status": "assigned", "created_at": "2025-04-12T23:13:41.430924+00:00", "updated_at": "2025-04-12T23:13:41.430924+00:00", "user_id": "dfaf494b-a559-4191-872b-5b0ec8b9d613", "offers_count": 1, "assigned_to": null, "visibility": "public"}, {"id": "f944c84d-e500-435a-8321-89d3f2df8bab", "title": "Broken window", "description": "The glass is cracked on one of the windows with my classroom ", "location": "English Department", "category": "Other", "budget": 1, "due_date": "2025-04-13T23:00:00+00:00", "status": "open", "created_at": "2025-04-12T14:05:34.471562+00:00", "updated_at": "2025-04-12T14:05:34.471562+00:00", "user_id": "db0ebbeb-e43d-4d14-8a1b-8238911ac48f", "offers_count": 0, "assigned_to": null, "visibility": "public"}, {"id": "66a2b13b-0508-458c-af1e-b7d4b85f49a3", "title": "Faulty light switch ", "description": "Light is flickering in classroom 5b ", "location": "Maths Department - Classroom 5B", "category": "Electrical", "budget": 1, "due_date": "2025-04-13T23:00:00+00:00", "status": "open", "created_at": "2025-04-12T11:48:15.416274+00:00", "updated_at": "2025-04-12T11:48:15.416274+00:00", "user_id": "db0ebbeb-e43d-4d14-8a1b-8238911ac48f", "offers_count": 0, "assigned_to": null, "visibility": "public"}], "openTasks": [{"id": "193b0803-4119-4253-83aa-258778307d0d", "title": "Public Test Task Created by <PERSON><PERSON><PERSON>", "description": "This is a public test task created by the script", "location": "Test Location", "category": "Test Category", "budget": 100, "due_date": "2025-04-13T00:22:08.763+00:00", "status": "open", "created_at": "2025-04-13T00:22:08.819929+00:00", "updated_at": "2025-04-13T00:22:08.819929+00:00", "user_id": "2818832a-c6a5-4d4f-becf-6fcb75b88764", "offers_count": 0, "assigned_to": null, "visibility": "public"}, {"id": "f242ca7f-fc24-4bcf-8fa2-1afd7eaddc5c", "title": "Test Task Created by <PERSON><PERSON><PERSON>", "description": "This is a test task created by the script", "location": "Test Location", "category": "Test Category", "budget": 100, "due_date": "2025-04-13T00:21:31.13+00:00", "status": "open", "created_at": "2025-04-13T00:21:31.18354+00:00", "updated_at": "2025-04-13T00:21:31.18354+00:00", "user_id": "2818832a-c6a5-4d4f-becf-6fcb75b88764", "offers_count": 0, "assigned_to": null, "visibility": "admin"}, {"id": "7b18a30d-7307-41cd-b48e-2a62309ef3d0", "title": "Policy Test Task", "description": "This is a test task to check if policies are working", "location": "Test Location", "category": "Test Category", "budget": 100, "due_date": "2025-04-13T00:13:37.007+00:00", "status": "open", "created_at": "2025-04-13T00:13:37.075938+00:00", "updated_at": "2025-04-13T00:13:37.075938+00:00", "user_id": "2818832a-c6a5-4d4f-becf-6fcb75b88764", "offers_count": 0, "assigned_to": null, "visibility": "admin"}, {"id": "65d7037b-7f71-4562-ab4d-141e2f99a3d7", "title": "Test Task Created by <PERSON><PERSON><PERSON>", "description": "This is a test task created by the script", "location": "Test Location", "category": "Test Category", "budget": 100, "due_date": "2025-04-13T00:06:22.707+00:00", "status": "open", "created_at": "2025-04-13T00:06:22.769806+00:00", "updated_at": "2025-04-13T00:06:22.769806+00:00", "user_id": "2818832a-c6a5-4d4f-becf-6fcb75b88764", "offers_count": 0, "assigned_to": null, "visibility": "admin"}, {"id": "0d84834f-27bf-4bf9-89fe-9e160d1bef26", "title": "external test ", "description": "external test ", "location": "school", "category": "Painting", "budget": 1.06, "due_date": "2025-04-16T23:00:00+00:00", "status": "open", "created_at": "2025-04-12T23:18:35.526577+00:00", "updated_at": "2025-04-12T23:18:35.526577+00:00", "user_id": "dfaf494b-a559-4191-872b-5b0ec8b9d613", "offers_count": 0, "assigned_to": null, "visibility": "public"}, {"id": "9417a63c-6f4b-42ea-b7fa-25cebc055ac9", "title": "test assignment internal ", "description": "assign internally ", "location": "school", "category": "Painting", "budget": 1.06, "due_date": "2025-04-16T23:00:00+00:00", "status": "open", "created_at": "2025-04-12T23:18:04.66253+00:00", "updated_at": "2025-04-12T23:18:04.66253+00:00", "user_id": "dfaf494b-a559-4191-872b-5b0ec8b9d613", "offers_count": 0, "assigned_to": null, "visibility": "public"}, {"id": "f944c84d-e500-435a-8321-89d3f2df8bab", "title": "Broken window", "description": "The glass is cracked on one of the windows with my classroom ", "location": "English Department", "category": "Other", "budget": 1, "due_date": "2025-04-13T23:00:00+00:00", "status": "open", "created_at": "2025-04-12T14:05:34.471562+00:00", "updated_at": "2025-04-12T14:05:34.471562+00:00", "user_id": "db0ebbeb-e43d-4d14-8a1b-8238911ac48f", "offers_count": 0, "assigned_to": null, "visibility": "public"}, {"id": "66a2b13b-0508-458c-af1e-b7d4b85f49a3", "title": "Faulty light switch ", "description": "Light is flickering in classroom 5b ", "location": "Maths Department - Classroom 5B", "category": "Electrical", "budget": 1, "due_date": "2025-04-13T23:00:00+00:00", "status": "open", "created_at": "2025-04-12T11:48:15.416274+00:00", "updated_at": "2025-04-12T11:48:15.416274+00:00", "user_id": "db0ebbeb-e43d-4d14-8a1b-8238911ac48f", "offers_count": 0, "assigned_to": null, "visibility": "public"}]}