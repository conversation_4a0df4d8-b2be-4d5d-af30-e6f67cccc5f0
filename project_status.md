# ClassTasker Project Status

## Current Status and Issues

We've been implementing a role-based access control system for the ClassTasker application. The system includes:

1. **Database Schema and RLS Updates**:
   - Added `is_site_admin` column to the profiles table
   - Created SQL functions for role checking (`is_site_admin()`, `has_role()`, etc.)
   - Implemented RLS policies for different roles
   - Created test data and verification scripts

2. **Frontend Implementation**:
   - Created the `useRolePermissions` hook for permission checking
   - Updated the AuthContext to support site admin status
   - Created the RoleBasedUI component for conditional rendering
   - Created the RoleProtectedRoute component for route protection
   - Created the AccessDenied page for unauthorized access
   - Created the SiteAdminDashboard and RoleManagement components
   - Created the RoleBasedNavigation component for role-specific navigation
   - Updated the main router to include the new role-based routes

3. **Current Issue**:
   - There's an infinite recursion in the database policies for the "profiles" relation
   - This is causing a 500 Internal Server Error when the application tries to fetch data
   - The error occurs when querying offers: `infinite recursion detected in policy for relation "profiles"`
   - This is preventing the site admin functionality from working correctly
   - We've created SQL scripts to fix the issue, but they need to be run in the Supabase SQL Editor

## SQL Scripts Created

1. `sql/fix_all_policies_safe.sql`: Fixes all policies on both the profiles and offers tables
2. `sql/fix_offers_policies_safe.sql`: Specifically fixes the offers table policies
3. `sql/check_and_fix_admin.sql`: Ensures <EMAIL> is correctly set as a site admin

## Next Steps

1. **Run the SQL Fix Scripts in Order**:
   - First, run `sql/fix_all_policies_safe.sql` to fix all the policies
   - Then run `sql/fix_offers_policies_safe.sql` to fix the offers table policies
   - Finally, run `sql/check_and_fix_admin.sql` <NAME_EMAIL> is a site admin

2. **Test the Implementation**:
   - Clear browser cache and cookies
   - Log out and log back <NAME_EMAIL>
   - Navigate to `/debug/links` to access debug tools
   - Check the Role Debug page to verify site admin status
   - Try accessing the site admin dashboard at `/admin/site`

3. **Further Enhancements**:
   - Implement role-specific dashboards for each role
   - Add more granular permissions as needed
   - Enhance the UI with role-specific indicators

## Technical Details

### Database Structure

- **profiles table**: Contains user profiles with role and is_site_admin fields
- **tasks table**: Contains tasks that can be assigned to users
- **offers table**: Contains offers made by suppliers for tasks

### RLS Policies

The issue is with the RLS policies that are causing infinite recursion:

1. The policy calls the `is_site_admin()` function
2. The function queries the `profiles` table
3. This query triggers the RLS policies again
4. The policies call the `is_site_admin()` function again
5. And so on, creating an infinite loop

Our fix:
- Simplifies all policies to avoid complex function calls
- Creates SECURITY DEFINER functions that bypass RLS for critical checks
- <NAME_EMAIL> is correctly set as a site admin
- Removes any circular dependencies between policies

### Test Accounts

- Site Administrator: <EMAIL> / Test123!
- Organization Administrator: <EMAIL> / Test123!
- Teacher: <EMAIL> / Test123!
- Maintenance Staff: <EMAIL> / Test123!
- Support Staff: <EMAIL> / Test123!
- Supplier: <EMAIL> / Test123!

### Development Environment

- Frontend: Vite on port 8082 (npm run dev)
- Stripe API Server: Node.js on port 3001 (npm run stripe-server)
- Combined: npm run dev:all

### Key Files

- `src/hooks/useRolePermissions.ts`: Hook for checking user permissions
- `src/contexts/AuthContext.tsx`: Context for user authentication and role information
- `src/components/auth/RoleProtectedRoute.tsx`: Component for protecting routes based on roles
- `src/pages/admin/SiteAdminDashboard.tsx`: Dashboard for site administrators
- `src/pages/admin/RoleManagement.tsx`: Page for managing user roles
- `src/pages/debug/RoleDebug.tsx`: Debug page for role information
- `sql/fix_all_policies_safe.sql`: Script to fix all policies
- `sql/fix_offers_policies_safe.sql`: Script to fix offers table policies
- `sql/check_and_fix_admin.sql`: Script <NAME_EMAIL> is a site admin

## Current Browser Errors

```
Error fetching user offers: {code: '42P17', details: null, hint: null, message: 'infinite recursion detected in policy for relation "profiles"'}
```

This error occurs when the application tries to fetch offers, which triggers the RLS policies that cause infinite recursion.
