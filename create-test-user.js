// <PERSON>ript to create a test user
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create Supabase client with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createTestUser() {
  try {
    const email = '<EMAIL>';
    const password = 'password123';

    console.log(`Creating user with email: ${email}`);

    // Check if user already exists
    const { data: { users }, error: usersError } = await supabase.auth.admin.listUsers();

    if (usersError) {
      console.error('Error listing users:', usersError);
      return;
    }

    const existingUser = users.find(u => u.email === email);

    if (existingUser) {
      console.log(`User with email ${email} already exists. Deleting...`);

      // Delete the user
      const { error: deleteError } = await supabase.auth.admin.deleteUser(existingUser.id);

      if (deleteError) {
        console.error('Error deleting existing user:', deleteError);
        return;
      }

      console.log('Existing user deleted');
    }

    // Create the user
    const { data: { user }, error: createError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true
    });

    if (createError) {
      console.error('Error creating user:', createError);
      return;
    }

    console.log('User created successfully:');
    console.log(`ID: ${user.id}`);
    console.log(`Email: ${user.email}`);

    // Create a profile for the user
    const { error: profileError } = await supabase
      .from('profiles')
      .insert({
        id: user.id,
        first_name: 'Test',
        last_name: 'User',
        account_type: 'school'
      });

    if (profileError) {
      console.error('Error creating profile:', profileError);
    } else {
      console.log('Profile created successfully');
    }

    console.log('\nTest user credentials:');
    console.log(`Email: ${email}`);
    console.log(`Password: ${password}`);

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

createTestUser();
