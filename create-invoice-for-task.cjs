// Script to create an invoice for a specific task
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createInvoiceForTask() {
  try {
    const taskId = 'fd946027-b773-43b4-b267-90a049b9458d';
    
    console.log(`Creating invoice for task: ${taskId}`);
    
    // Get task details
    const { data: task, error: taskError } = await supabase
      .from('tasks')
      .select('*, offers(*), payments(*)')
      .eq('id', taskId)
      .single();
    
    if (taskError) {
      console.error('Error fetching task:', taskError);
      return;
    }
    
    console.log('Task Details:');
    console.log(`ID: ${task.id}`);
    console.log(`Title: ${task.title}`);
    console.log(`Status: ${task.status}`);
    console.log(`Payment Status: ${task.payment_status}`);
    
    // Get the payer details
    const { data: payer, error: payerError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', task.user_id)
      .single();
    
    if (payerError) {
      console.error('Error fetching payer:', payerError);
      return;
    }
    
    console.log('\nPayer Details:');
    console.log(`ID: ${payer.id}`);
    console.log(`Email: ${payer.email}`);
    
    // Get the accepted offer
    const acceptedOffer = task.offers.find(offer => offer.status === 'accepted');
    
    if (!acceptedOffer) {
      console.error('No accepted offer found for this task.');
      return;
    }
    
    console.log('\nAccepted Offer:');
    console.log(`ID: ${acceptedOffer.id}`);
    console.log(`Amount: £${acceptedOffer.amount}`);
    
    // Get the supplier details
    const { data: supplier, error: supplierError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', acceptedOffer.user_id)
      .single();
    
    if (supplierError) {
      console.error('Error fetching supplier:', supplierError);
      return;
    }
    
    console.log('\nSupplier Details:');
    console.log(`ID: ${supplier.id}`);
    console.log(`Email: ${supplier.email}`);
    
    // Create or get a Stripe customer for the payer
    let customer;
    
    try {
      // Check if customer already exists
      const customers = await stripe.customers.list({
        email: payer.email,
        limit: 1
      });
      
      if (customers.data.length > 0) {
        customer = customers.data[0];
        console.log(`Using existing customer: ${customer.id}`);
      } else {
        // Create a new customer
        customer = await stripe.customers.create({
          email: payer.email,
          name: payer.full_name || payer.email,
          metadata: {
            user_id: payer.id
          }
        });
        console.log(`Created new customer: ${customer.id}`);
      }
    } catch (error) {
      console.error('Error creating/getting Stripe customer:', error);
      return;
    }
    
    // Create an invoice item
    const invoiceItem = await stripe.invoiceItems.create({
      customer: customer.id,
      amount: Math.round(acceptedOffer.amount * 100), // Convert to cents
      currency: 'gbp',
      description: `Payment for: ${task.title}`,
    });
    
    console.log(`\nCreated invoice item: ${invoiceItem.id}`);
    
    // Create an invoice
    const invoice = await stripe.invoices.create({
      customer: customer.id,
      collection_method: 'send_invoice',
      days_until_due: 30,
      metadata: {
        task_id: task.id,
        offer_id: acceptedOffer.id
      }
    });
    
    console.log(`Created invoice: ${invoice.id}`);
    
    // Finalize the invoice
    const finalizedInvoice = await stripe.invoices.finalizeInvoice(invoice.id);
    
    console.log(`Finalized invoice: ${finalizedInvoice.id}`);
    console.log(`Invoice number: ${finalizedInvoice.number}`);
    
    // Send the invoice
    const sentInvoice = await stripe.invoices.sendInvoice(finalizedInvoice.id);
    
    console.log(`Sent invoice: ${sentInvoice.id}`);
    console.log(`Invoice URL: ${sentInvoice.hosted_invoice_url}`);
    
    // Create a payment record if one doesn't exist
    let payment;
    
    if (task.payments && task.payments.length > 0) {
      payment = task.payments[0];
      console.log(`\nUsing existing payment: ${payment.id}`);
    } else {
      const { data: newPayment, error: paymentError } = await supabase
        .from('payments')
        .insert({
          task_id: task.id,
          offer_id: acceptedOffer.id,
          payer_id: payer.id,
          payee_id: supplier.id,
          amount: acceptedOffer.amount,
          platform_fee: acceptedOffer.amount * 0.2, // 20% platform fee
          supplier_amount: acceptedOffer.amount * 0.8, // 80% to supplier
          status: 'pending',
          currency: 'gbp'
        })
        .select()
        .single();
      
      if (paymentError) {
        console.error('Error creating payment record:', paymentError);
        return;
      }
      
      payment = newPayment;
      console.log(`\nCreated new payment: ${payment.id}`);
    }
    
    // Store the invoice in our database
    const { data: dbInvoice, error: invoiceError } = await supabase
      .from('invoices')
      .insert({
        payment_id: payment.id,
        task_id: task.id,
        invoice_number: finalizedInvoice.number,
        invoice_url: finalizedInvoice.hosted_invoice_url,
        stripe_invoice_id: finalizedInvoice.id,
        status: finalizedInvoice.status,
        due_date: finalizedInvoice.due_date ? new Date(finalizedInvoice.due_date * 1000).toISOString() : null
      })
      .select()
      .single();
    
    if (invoiceError) {
      console.error('Error creating invoice record:', invoiceError);
      return;
    }
    
    console.log('\nInvoice record created in database:');
    console.log(`ID: ${dbInvoice.id}`);
    console.log(`Invoice Number: ${dbInvoice.invoice_number}`);
    console.log(`Status: ${dbInvoice.status}`);
    
    console.log('\nTest completed successfully.');
  } catch (error) {
    console.error('Error creating invoice for task:', error);
  }
}

createInvoiceForTask();
