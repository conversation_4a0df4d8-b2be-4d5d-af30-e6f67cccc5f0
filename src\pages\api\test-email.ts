import { testSmtpConnection } from '@/services/emailTester';
import { EmailConfig } from '@/types/email';

export default async function handler(req: any, res: any) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { config, testEmail } = req.body;
    
    if (!config || !testEmail) {
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    const result = await testSmtpConnection(config as EmailConfig, testEmail);
    
    return res.status(result.success ? 200 : 400).json(result);
  } catch (error: any) {
    console.error('Error testing email:', error);
    return res.status(500).json({ 
      success: false, 
      message: `Server error: ${error.message}` 
    });
  }
}
