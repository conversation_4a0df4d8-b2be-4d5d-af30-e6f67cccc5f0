@echo off
echo Starting Stripe CLI in test mode...
echo This will allow you to test Stripe Connect without HTTPS.
echo.
echo First, let's switch to test mode by updating the environment variables...

REM Update .env file to use test mode
echo Updating .env file...
powershell -Command "(Get-Content .env) -replace 'STRIPE_SECRET_KEY=.*', 'STRIPE_SECRET_KEY=sk_test_51RFz5yPWBcixtWOc6SFaXTQ4NxDXTFNmCf7rQqD2ZUhNA4lKjjS6EB1IP16iHl0ArCtN0XTQrY87XVO5wfGn7uRU00fGCizEnS' | Set-Content .env"

REM Update .env.local file to use test mode
echo Updating .env.local file...
powershell -Command "(Get-Content .env.local) -replace 'VITE_STRIPE_PUBLIC_KEY=.*', 'VITE_STRIPE_PUBLIC_KEY=pk_test_51RFz5yPWBcixtWOchgiZ3Zj7qsG8pV9W9IGbjwl2yim9iIQSDyR1hJhJpWGv8HFJGsswwfaEm0J8sjTdMilbUfLM00Sv1YQD4B' | Set-Content .env.local"

echo Environment variables updated to use test mode.
echo.
echo Now, let's start the Stripe CLI to forward events to our local server...
echo.
echo Please run the following command in a new terminal:
echo stripe listen --forward-to http://localhost:3000/api/stripe-webhook
echo.
echo Then, in another terminal, run:
echo npm run stripe-server
echo.
echo Press any key to continue...
pause > nul