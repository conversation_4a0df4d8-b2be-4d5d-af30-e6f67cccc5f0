@echo off
echo Starting Stripe CLI in test mode...
echo This will allow you to test Stripe Connect without HTTPS.
echo.
echo First, let's switch to test mode by updating the environment variables...

REM SECURITY: Do not hardcode API keys in scripts
echo SECURITY WARNING: This script previously contained hardcoded Stripe keys.
echo Please manually update your .env files with your current Stripe keys.
echo.
echo Update .env file with:
echo STRIPE_SECRET_KEY=your_stripe_secret_key_here
echo.
echo Update .env.local file with:
echo VITE_STRIPE_PUBLIC_KEY=your_stripe_public_key_here

echo Environment variables updated to use test mode.
echo.
echo Now, let's start the Stripe CLI to forward events to our local server...
echo.
echo Please run the following command in a new terminal:
echo stripe listen --forward-to http://localhost:3000/api/stripe-webhook
echo.
echo Then, in another terminal, run:
echo npm run stripe-server
echo.
echo Press any key to continue...
pause > nul