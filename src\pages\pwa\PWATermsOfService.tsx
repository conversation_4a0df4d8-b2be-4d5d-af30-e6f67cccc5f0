import React from 'react';
import PWAMobileLayout from '@/components/pwa/PWAMobileLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, FileText } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const PWATermsOfService = () => {
  const navigate = useNavigate();

  return (
    <PWAMobileLayout>
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center mb-4">
          <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center ml-2">
            <FileText className="h-5 w-5 text-classtasker-blue mr-2" />
            <h1 className="text-lg font-semibold">Terms of Service</h1>
          </div>
        </div>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-classtasker-blue">Classtasker UK Ltd Terms & Conditions</CardTitle>
            <p className="text-sm text-gray-600">Last updated: 12th May, 2025</p>
          </CardHeader>
          <CardContent className="space-y-4 text-sm">
            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-3 mb-4">
              <p className="text-gray-800 font-medium text-xs">
                BY USING THE PLATFORM, YOU AGREE TO BE BOUND BY THESE TERMS.
                IF YOU DO NOT AGREE, YOU MAY NOT ACCESS OR USE THE PLATFORM.
              </p>
            </div>

            <section>
              <h3 className="font-semibold mb-2">1. The Platform</h3>
              <p className="text-gray-700 leading-relaxed mb-2">
                The Platform is an online marketplace connecting Clients (Schools, Colleges, MATs)
                with Taskers (businesses) for short-term services.
              </p>
              <p className="text-gray-700 leading-relaxed">
                Taskers are independent business owners providing services under their own name,
                using their own tools and supplies.
              </p>
            </section>

            <section>
              <h3 className="font-semibold mb-2">2. Registration & Account Security</h3>
              <p className="text-gray-700 leading-relaxed mb-2">
                You must provide accurate information and maintain account security:
              </p>
              <ul className="list-disc pl-4 space-y-1 text-gray-700">
                <li>Provide correct contact and business information</li>
                <li>Keep login credentials confidential</li>
                <li>Notify us of any account changes</li>
                <li>Accept responsibility for all account activities</li>
              </ul>
            </section>

            <section>
              <h3 className="font-semibold mb-2">3. Fees & Payment</h3>
              <p className="text-gray-700 leading-relaxed mb-2">
                Payment terms are set out in our Supplemental Terms:
              </p>
              <ul className="list-disc pl-4 space-y-1 text-gray-700">
                <li>All fees are non-refundable unless stated</li>
                <li>Fees charged in British Pounds (£)</li>
                <li>Payment processing via third-party providers</li>
              </ul>
            </section>

            <section>
              <h3 className="font-semibold mb-2">4. Intellectual Property</h3>
              <p className="text-gray-700 leading-relaxed">
                The Platform and all content are owned by Classtasker UK Ltd.
                Service marks and trademarks are protected and not available for use by Taskers.
              </p>
            </section>

            <section>
              <h3 className="font-semibold mb-2">5. Disclaimer of Warranties</h3>
              <div className="bg-red-50 border-l-4 border-red-400 p-3">
                <p className="text-gray-700 text-xs leading-relaxed">
                  THE PLATFORM IS PROVIDED "AS IS" AND "AS AVAILABLE" WITHOUT WARRANTIES
                  OF ANY KIND. WE DISCLAIM ALL WARRANTIES INCLUDING MERCHANTABILITY,
                  FITNESS FOR PURPOSE, AND NON-INFRINGEMENT.
                </p>
              </div>
            </section>

            <section>
              <h3 className="font-semibold mb-2">6. Limitation of Liability</h3>
              <div className="bg-red-50 border-l-4 border-red-400 p-3">
                <p className="text-gray-700 text-xs leading-relaxed">
                  CLASSTASKER UK LTD SHALL NOT BE LIABLE FOR ANY INDIRECT, INCIDENTAL,
                  SPECIAL, OR CONSEQUENTIAL DAMAGES ARISING FROM YOUR USE OF THE PLATFORM.
                </p>
              </div>
            </section>

            <section>
              <h3 className="font-semibold mb-2">7. Governing Law</h3>
              <p className="text-gray-700 leading-relaxed">
                For Users within the United Kingdom: English law applies and disputes
                will be dealt with by English courts.
              </p>
            </section>

            <section>
              <h3 className="font-semibold mb-2">8. Contact Information</h3>
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-gray-700 text-xs">
                  <strong>Company:</strong> Classtasker UK Ltd, Inc.<br />
                  <strong>Registration:</strong> 16386525<br />
                  <strong>UTR:</strong> 93521 20881<br />
                  <strong>Email:</strong> <EMAIL>
                </p>
              </div>
            </section>

            <div className="pt-4 border-t">
              <Button
                variant="outline"
                className="w-full"
                onClick={() => navigate('/privacy')}
              >
                View Privacy Policy
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </PWAMobileLayout>
  );
};

export default PWATermsOfService;
