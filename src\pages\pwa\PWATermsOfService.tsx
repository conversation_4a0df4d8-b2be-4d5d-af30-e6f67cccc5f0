import React from 'react';
import PWAMobileLayout from '@/components/pwa/PWAMobileLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, FileText } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const PWATermsOfService = () => {
  const navigate = useNavigate();

  return (
    <PWAMobileLayout>
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center mb-4">
          <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center ml-2">
            <FileText className="h-5 w-5 text-classtasker-blue mr-2" />
            <h1 className="text-lg font-semibold">Terms of Service</h1>
          </div>
        </div>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-classtasker-blue">ClassTasker Terms of Service</CardTitle>
            <p className="text-sm text-gray-600">Last updated: {new Date().toLocaleDateString('en-GB', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
          </CardHeader>
          <CardContent className="space-y-4 text-sm">
            <section>
              <h3 className="font-semibold mb-2">1. Acceptance of Terms</h3>
              <p className="text-gray-700 leading-relaxed">
                By accessing and using ClassTasker, you accept and agree to be bound by these terms. 
                If you do not agree, please do not use this service.
              </p>
            </section>

            <section>
              <h3 className="font-semibold mb-2">2. Description of Service</h3>
              <p className="text-gray-700 leading-relaxed mb-2">
                ClassTasker connects educational institutions with qualified maintenance and service providers:
              </p>
              <ul className="list-disc pl-4 space-y-1 text-gray-700">
                <li>Task posting and management for schools</li>
                <li>Marketplace for suppliers to find tasks</li>
                <li>Communication tools</li>
                <li>Payment processing</li>
                <li>Compliance tracking</li>
              </ul>
            </section>

            <section>
              <h3 className="font-semibold mb-2">3. User Accounts</h3>
              <p className="text-gray-700 leading-relaxed mb-2">
                To use our services, you must register an account and agree to:
              </p>
              <ul className="list-disc pl-4 space-y-1 text-gray-700">
                <li>Provide accurate information</li>
                <li>Keep your password secure</li>
                <li>Accept responsibility for account activities</li>
              </ul>
            </section>

            <section>
              <h3 className="font-semibold mb-2">4. Acceptable Use</h3>
              <p className="text-gray-700 leading-relaxed mb-2">
                You agree not to:
              </p>
              <ul className="list-disc pl-4 space-y-1 text-gray-700">
                <li>Violate any laws or regulations</li>
                <li>Post false or misleading content</li>
                <li>Harass or harm other users</li>
                <li>Interfere with service functionality</li>
              </ul>
            </section>

            <section>
              <h3 className="font-semibold mb-2">5. Payment Terms</h3>
              <ul className="list-disc pl-4 space-y-1 text-gray-700">
                <li>Fees are charged in British Pounds (£)</li>
                <li>Payment due according to billing cycle</li>
                <li>All fees are non-refundable unless stated</li>
                <li>Pricing may change with 30 days notice</li>
              </ul>
            </section>

            <section>
              <h3 className="font-semibold mb-2">6. Privacy</h3>
              <p className="text-gray-700 leading-relaxed">
                Your privacy is important to us. Please review our Privacy Policy to understand 
                how we collect and use your personal information.
              </p>
            </section>

            <section>
              <h3 className="font-semibold mb-2">7. Intellectual Property</h3>
              <p className="text-gray-700 leading-relaxed">
                The service and its content are the exclusive property of ClassTasker and protected 
                by copyright, trademark, and other laws.
              </p>
            </section>

            <section>
              <h3 className="font-semibold mb-2">8. Limitation of Liability</h3>
              <p className="text-gray-700 leading-relaxed">
                ClassTasker shall not be liable for any indirect, incidental, special, or consequential damages 
                resulting from your use of the service.
              </p>
            </section>

            <section>
              <h3 className="font-semibold mb-2">9. Termination</h3>
              <p className="text-gray-700 leading-relaxed">
                We may terminate or suspend your account immediately for any reason, 
                including breach of these terms.
              </p>
            </section>

            <section>
              <h3 className="font-semibold mb-2">10. Governing Law</h3>
              <p className="text-gray-700 leading-relaxed">
                These terms are governed by the laws of England and Wales. 
                Disputes will be subject to the jurisdiction of English courts.
              </p>
            </section>

            <section>
              <h3 className="font-semibold mb-2">11. Changes to Terms</h3>
              <p className="text-gray-700 leading-relaxed">
                We may modify these terms at any time. Material changes will be notified 
                with at least 30 days notice.
              </p>
            </section>

            <section>
              <h3 className="font-semibold mb-2">12. Contact Information</h3>
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-gray-700">
                  <strong>Email:</strong> <EMAIL><br />
                  <strong>Address:</strong> ClassTasker Ltd, United Kingdom
                </p>
              </div>
            </section>

            <div className="pt-4 border-t">
              <Button 
                variant="outline" 
                className="w-full" 
                onClick={() => navigate('/privacy')}
              >
                View Privacy Policy
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </PWAMobileLayout>
  );
};

export default PWATermsOfService;
