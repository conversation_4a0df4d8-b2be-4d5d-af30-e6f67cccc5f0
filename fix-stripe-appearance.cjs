// <PERSON>ript to fix the Stripe appearance options in EnhancedPaymentProcessor.tsx
const fs = require('fs');
const path = require('path');

// Paths
const processorPath = path.join(__dirname, 'src', 'components', 'stripe', 'EnhancedPaymentProcessor.tsx');
const backupPath = path.join(__dirname, 'src', 'components', 'stripe', 'EnhancedPaymentProcessor.tsx.bak2');

// Create a backup of the original file
console.log('Creating backup of EnhancedPaymentProcessor.tsx...');
fs.copyFileSync(processorPath, backupPath);
console.log(`Backup created at ${backupPath}`);

// Read the file content
console.log('Reading EnhancedPaymentProcessor.tsx...');
let content = fs.readFileSync(processorPath, 'utf8');

// Fix the TabIcon marginRight property
if (content.includes('marginRight: \'8px\'')) {
  console.log('Fixing TabIcon marginRight property...');
  content = content.replace(/marginRight: ['"]8px['"]/g, 'margin: \'0 8px 0 0\'');
}

// Remove the FormRow class
if (content.includes('.FormRow')) {
  console.log('Removing FormRow class...');
  const formRowRegex = /\s*['"]\.FormRow['"]:\s*{\s*marginBottom:\s*['"]12px['"]\s*},/g;
  content = content.replace(formRowRegex, '');
}

// Write the modified content back to the file
fs.writeFileSync(processorPath, content, 'utf8');
console.log('Fix applied successfully!');

console.log('\nChanges made:');
console.log('1. Changed TabIcon marginRight to margin: \'0 8px 0 0\'');
console.log('2. Removed the FormRow class which is not supported');

console.log('\nPlease refresh the page and try the payment again.');
console.log('\nIf you need to restore the original file, run:');
console.log(`cp ${backupPath} ${processorPath}`);