// Simple script to restart the server by touching a file to trigger a reload
const fs = require('fs');
const path = require('path');

// Path to the server file
const serverPath = path.join(__dirname, 'server', 'index.js');

// Get the current timestamp
const timestamp = new Date().toISOString();

// Read the current content
console.log('Reading server index.js...');
let content = fs.readFileSync(serverPath, 'utf8');

// Add a harmless comment to trigger a reload
console.log('Adding a timestamp comment to force server restart...');
content = `// Last restarted: ${timestamp}\n${content}`;

// Write the modified content back to the file
fs.writeFileSync(serverPath, content, 'utf8');
console.log('Server restart triggered successfully!');
console.log('The server should restart automatically.');
console.log('Please try the payment process again in your browser.');