import express from 'express';
import Stripe from 'stripe';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import cors from 'cors';

dotenv.config();

const router = express.Router();
router.use(cors());
router.use(express.json());

// Initialize Stripe with the secret key
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
console.log('Raw Stripe secret key:', stripeSecretKey);
console.log('Stripe API key:', stripeSecretKey ? `${stripeSecretKey.substring(0, 7)}...${stripeSecretKey.substring(stripeSecretKey.length - 4)}` : 'undefined');
console.log('API mode:', stripeSecretKey?.startsWith('sk_test') ? 'TEST' : 'LIVE');
console.log('Key length:', stripeSecretKey?.length);

if (!stripeSecretKey) {
  console.error('Stripe secret key is not defined in environment variables');
  process.exit(1);
}

console.log('Using Stripe API key:', stripeSecretKey.startsWith('sk_test') ? 'TEST MODE' : 'LIVE MODE');

const stripe = new Stripe(stripeSecretKey);

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceRoleKey) {
  console.error('Supabase URL or service role key is not defined in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

// Create a Stripe Connect Express account link
router.post('/create-account-link', async (req, res) => {
  try {
    const { accountId, refreshUrl, returnUrl } = req.body;

    if (!accountId) {
      return res.status(400).json({ error: 'Account ID is required' });
    }

    const accountLink = await stripe.accountLinks.create({
      account: accountId,
      refresh_url: refreshUrl || process.env.STRIPE_CONNECT_EXPRESS_REFRESH_URL,
      return_url: returnUrl || process.env.STRIPE_CONNECT_EXPRESS_RETURN_URL,
      type: 'account_onboarding',
    });

    res.json({ url: accountLink.url });
  } catch (error) {
    console.error('Error creating account link:', error);
    res.status(500).json({ error: 'Failed to create account link' });
  }
});

// Create a Stripe Connect Express account
router.post('/create-account', async (req, res) => {
  try {
    const { userId, email, country = 'GB' } = req.body;

    if (!userId || !email) {
      return res.status(400).json({ error: 'User ID and email are required' });
    }

    // Check if the user already has a Stripe account
    const { data: existingAccounts } = await supabase
      .from('stripe_connect_accounts')
      .select('*')
      .eq('user_id', userId);

    if (existingAccounts && existingAccounts.length > 0) {
      return res.json({ accountId: existingAccounts[0].account_id });
    }

    // Create a new Stripe Connect Express account
    const account = await stripe.accounts.create({
      type: 'express',
      country,
      email,
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
      business_type: 'individual',
      business_profile: {
        mcc: '8299', // Educational Services
        url: 'https://lovable.dev',
      },
    });

    // Store the account in the database
    const { data, error } = await supabase
      .from('stripe_connect_accounts')
      .insert({
        user_id: userId,
        account_id: account.id,
        account_type: 'express',
        account_status: 'pending',
      });

    if (error) {
      console.error('Error storing Stripe account in database:', error);
      return res.status(500).json({ error: 'Failed to store Stripe account' });
    }

    res.json({ accountId: account.id });
  } catch (error) {
    console.error('Error creating Stripe account:', error);
    res.status(500).json({ error: 'Failed to create Stripe account' });
  }
});

// Get Stripe Connect Express account status
router.get('/account-status/:accountId', async (req, res) => {
  try {
    const { accountId } = req.params;
    console.log('Getting status for account', accountId);

    if (!accountId) {
      return res.status(400).json({ error: 'Account ID is required' });
    }

    console.log('Retrieving Stripe Connect Express account status...');
    const account = await stripe.accounts.retrieve(accountId);

    // Update the account status in the database
    const accountStatus = account.charges_enabled ? 'active' : 'pending';
    
    const { data, error } = await supabase
      .from('stripe_connect_accounts')
      .update({
        charges_enabled: account.charges_enabled,
        payouts_enabled: account.payouts_enabled,
        account_status: accountStatus,
      })
      .eq('account_id', accountId);

    if (error) {
      console.error('Error updating Stripe account status in database:', error);
    }

    res.json({
      id: account.id,
      charges_enabled: account.charges_enabled,
      payouts_enabled: account.payouts_enabled,
      requirements: account.requirements,
      status: accountStatus,
    });
  } catch (error) {
    console.error('Error getting Stripe account status:', error);
    res.status(500).json({ error: 'Failed to get Stripe account status' });
  }
});

// Create a login link for a Stripe Connect Express account
router.post('/create-login-link', async (req, res) => {
  try {
    const { accountId } = req.body;

    if (!accountId) {
      return res.status(400).json({ error: 'Account ID is required' });
    }

    const loginLink = await stripe.accounts.createLoginLink(accountId);

    res.json({ url: loginLink.url });
  } catch (error) {
    console.error('Error creating login link:', error);
    res.status(500).json({ error: 'Failed to create login link' });
  }
});

// Create a payment with direct transfer
router.post('/create-payment', async (req, res) => {
  try {
    const { taskId, offerId, amount } = req.body;
    const { user_id: payerId } = req.body.user;

    if (!taskId || !offerId || !amount || !payerId) {
      return res.status(400).json({ error: 'Task ID, offer ID, amount, and payer ID are required' });
    }

    // Get the offer details to find the supplier (payee)
    const { data: offer, error: offerError } = await supabase
      .from('offers')
      .select('user_id')
      .eq('id', offerId)
      .single();

    if (offerError || !offer) {
      console.error('Error getting offer details:', offerError);
      return res.status(500).json({ error: 'Failed to get offer details' });
    }

    const payeeId = offer.user_id;

    // Calculate platform fee (20% of the amount)
    const platformFeePercentage = process.env.PLATFORM_FEE_PERCENTAGE || 20;
    const platformFee = (amount * platformFeePercentage) / 100;
    const supplierAmount = amount - platformFee;

    // Create a payment record
    const { data: payment, error: paymentError } = await supabase
      .from('payments')
      .insert({
        task_id: taskId,
        offer_id: offerId,
        payer_id: payerId,
        payee_id: payeeId,
        amount,
        platform_fee: platformFee,
        supplier_amount: supplierAmount,
        status: 'pending',
        currency: 'gbp', // Default to GBP
      })
      .select()
      .single();

    if (paymentError || !payment) {
      console.error('Error creating payment record:', paymentError);
      return res.status(500).json({ error: 'Failed to create payment record' });
    }

    res.json(payment);
  } catch (error) {
    console.error('Error creating payment:', error);
    res.status(500).json({ error: 'Failed to create payment' });
  }
});

// Create a payment intent for a payment
router.post('/create-payment-intent', async (req, res) => {
  try {
    const { paymentId } = req.body;
    console.log('=== CREATE PAYMENT INTENT REQUEST ===');
    console.log('Request body:', req.body);
    console.log('Request headers:', req.headers);

    if (!paymentId) {
      return res.status(400).json({ error: 'Payment ID is required' });
    }

    console.log('Creating payment intent for payment', paymentId);

    // Get the payment details
    console.log('Fetching payment details from Supabase...');
    const { data: payment, error: paymentError } = await supabase
      .from('payments')
      .select(`
        *,
        tasks (*),
        offers (*)
      `)
      .eq('id', paymentId)
      .single();

    if (paymentError || !payment) {
      console.error('Error getting payment details:', paymentError);
      return res.status(500).json({ error: 'Failed to get payment details' });
    }

    console.log('Payment details:', payment);

    // Get the supplier's Stripe account
    console.log('Fetching Stripe account for supplier', payment.payee_id);
    const { data: supplierAccount, error: supplierAccountError } = await supabase
      .from('stripe_connect_accounts')
      .select('*')
      .eq('user_id', payment.payee_id)
      .single();

    if (supplierAccountError || !supplierAccount) {
      console.error('Error getting supplier Stripe account:', supplierAccountError);
      return res.status(500).json({ error: 'Failed to get supplier Stripe account' });
    }

    console.log('Supplier account details:', supplierAccount);

    // Convert amount to cents for Stripe
    const amountInCents = Math.round(payment.amount * 100);
    const platformFeeInCents = Math.round(payment.platform_fee * 100);

    // Define payment method types based on currency
    let paymentMethodTypes = ['card']; // Default to card only
    const currency = payment.currency.toLowerCase();

    // Add currency-specific payment methods
    // For GBP (British Pound) - UK-specific payment methods
    if (currency === 'gbp') {
      paymentMethodTypes = [
        'card',           // Credit/debit cards
        'paypal',         // PayPal
        'bacs_debit',     // Direct Debit (UK)
        'link',           // Stripe Link
        'revolut_pay',    // Revolut Pay
        'customer_balance' // Pay by Bank
      ];
      
      // Add Apple Pay and Google Pay if supported by the browser
      if (req.headers['user-agent']?.includes('iPhone') || 
          req.headers['user-agent']?.includes('iPad') || 
          req.headers['user-agent']?.includes('Mac')) {
        paymentMethodTypes.push('apple_pay');
      } else if (req.headers['user-agent']?.includes('Android')) {
        paymentMethodTypes.push('google_pay');
      } else {
        // For desktop browsers, include both as the Stripe Elements UI will only show supported methods
        paymentMethodTypes.push('apple_pay');
        paymentMethodTypes.push('google_pay');
      }
    }
    // For EUR (Euro)
    else if (currency === 'eur') {
      paymentMethodTypes = [
        'card',
        'paypal',
        'sepa_debit'
      ];
    }
    // For USD (US Dollar)
    else if (currency === 'usd') {
      paymentMethodTypes = [
        'card',
        'paypal',
        'us_bank_account'
      ];
    }

    console.log('Using payment method types:', paymentMethodTypes);

    // Configure payment method options
    const paymentMethodOptions = {};
    
    // Configure BACS Direct Debit options for GBP
    if (paymentMethodTypes.includes('bacs_debit')) {
      paymentMethodOptions.bacs_debit = {
        setup_future_usage: 'off_session'
      };
    }
    
    // Configure SEPA Direct Debit options for EUR
    if (paymentMethodTypes.includes('sepa_debit')) {
      paymentMethodOptions.sepa_debit = {
        setup_future_usage: 'off_session'
      };
    }
    
    // Configure ACH Direct Debit options for USD
    if (paymentMethodTypes.includes('us_bank_account')) {
      paymentMethodOptions.us_bank_account = {
        setup_future_usage: 'off_session'
      };
    }
    
    // Configure Pay by Bank (customer_balance) options
    if (paymentMethodTypes.includes('customer_balance')) {
      paymentMethodOptions.customer_balance = {
        funding_type: 'bank_transfer',
        bank_transfer: {
          type: 'gb_bank_transfer'
        }
      };
    }
    
    // Configure Link options
    if (paymentMethodTypes.includes('link')) {
      paymentMethodOptions.link = {
        persistent_token: true
      };
    }

    // Create the payment intent with payment method options
    const paymentIntentParams = {
      amount: amountInCents,
      currency: payment.currency,
      payment_method_types: paymentMethodTypes,
      transfer_data: {
        destination: supplierAccount.account_id,
      },
      application_fee_amount: platformFeeInCents,
      metadata: {
        payment_id: paymentId,
        task_id: payment.task_id,
        offer_id: payment.offer_id,
      }
    };

    // Only add payment_method_options if we have any
    if (Object.keys(paymentMethodOptions).length > 0) {
      paymentIntentParams.payment_method_options = paymentMethodOptions;
    }

    console.log('Creating payment intent with params:', JSON.stringify(paymentIntentParams, null, 2));
    const paymentIntent = await stripe.paymentIntents.create(paymentIntentParams);

    // Update the payment record with the payment intent ID
    await supabase
      .from('payments')
      .update({
        payment_intent_id: paymentIntent.id,
        status: 'processing',
      })
      .eq('id', paymentId);

    return res.json({ clientSecret: paymentIntent.client_secret });
  } catch (error) {
    console.error('Error creating payment intent:', error);
    return res.status(500).json({ error: 'Error creating payment intent' });
  }
});

export default router;
