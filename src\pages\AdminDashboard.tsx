import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/contexts/AuthContext';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/hooks/use-toast';
import { Users, BarChart3, Settings, Building, Shield, Mail, Database, Map, ArrowRight, ClipboardList, Info, MessageSquare } from 'lucide-react';
import AdminServerStatus from '@/components/admin/AdminServerStatus';

// Import admin components
import AdminUsersList from '@/components/admin/AdminUsersList';
import AdminOrganizations from '@/components/admin/AdminOrganizations';
import AdminSettings from '@/components/admin/AdminSettings';
import AdminStats from '@/components/admin/AdminStats';

const AdminDashboard = () => {
  const { user, isAdmin } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('users');
  const [isServerRunning, setIsServerRunning] = useState<boolean | null>(null);

  // Check admin server status
  useEffect(() => {
    const checkServerStatus = async () => {
      try {
        const response = await fetch('http://localhost:3001/', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        setIsServerRunning(response.ok);
      } catch (error) {
        console.error('Error checking admin server status:', error);
        setIsServerRunning(false);
      }
    };

    checkServerStatus();
  }, []);

  // Check if user has admin role
  React.useEffect(() => {
    if (user) {
      // Use the isAdmin flag from AuthContext instead of checking metadata directly
      if (!isAdmin) {
        toast({
          title: 'Access Denied',
          description: 'You do not have permission to access the admin dashboard.',
          variant: 'destructive',
        });
        navigate('/dashboard');
      }
    }
  }, [user, isAdmin, navigate]);

  if (!user || !isAdmin) {
    return null; // Don't render anything if not admin
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Shield className="h-6 w-6 text-amber-500 mr-2" />
            <h1 className="text-2xl font-bold">Admin Dashboard</h1>
          </div>
          <AdminServerStatus />
        </div>

        {/* Dashboard Purpose Information */}
        <Card className="mb-6">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Info className="mr-2 h-5 w-5 text-blue-500" />
              Dashboard Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">
              This dashboard shows your personal tasks and administrative tools. Use the <strong>Task Management</strong> link below to review and assign tasks created by teachers.
              The <strong>Organisation Dashboard</strong> link provides access to organization-wide settings and data.
            </p>
          </CardContent>
        </Card>

        {/* Quick Links Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <ClipboardList className="mr-2 h-5 w-5 text-amber-500" />
                Task Management
              </CardTitle>
            </CardHeader>
            <CardContent className="pb-2">
              <p className="text-sm text-gray-500">Review and assign tasks created by teachers</p>
            </CardContent>
            <CardFooter>
              <Button asChild variant="outline" size="sm" className="w-full">
                <Link to="/admin/tasks">
                  Manage Tasks <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <Users className="mr-2 h-5 w-5" />
                Organisation Members
              </CardTitle>
            </CardHeader>
            <CardContent className="pb-2">
              <p className="text-sm text-gray-500">Manage users and roles within your organisation</p>
            </CardContent>
            <CardFooter>
              <Button asChild variant="outline" size="sm" className="w-full">
                <Link to="/organization/members">
                  Manage Members <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <Settings className="mr-2 h-5 w-5" />
                Task Management
              </CardTitle>
            </CardHeader>
            <CardContent className="pb-2">
              <p className="text-sm text-gray-500">Manage tasks and assignments</p>
            </CardContent>
            <CardFooter>
              <Button asChild variant="outline" size="sm" className="w-full">
                <Link to="/admin/tasks">
                  Manage Tasks <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardFooter>
          </Card>

          <Card className={!isServerRunning ? "opacity-60" : ""}>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <Database className="mr-2 h-5 w-5" />
                System Users
              </CardTitle>
            </CardHeader>
            <CardContent className="pb-2">
              <p className="text-sm text-gray-500">Manage all users in the system</p>
              {!isServerRunning && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-600">
                  Admin server is offline
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button asChild variant="outline" size="sm" className="w-full" disabled={!isServerRunning}>
                <Link to="/admin/users">
                  System Users <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <MessageSquare className="mr-2 h-5 w-5 text-blue-500" />
                GetStream Debug
              </CardTitle>
            </CardHeader>
            <CardContent className="pb-2">
              <p className="text-sm text-gray-500">Debug and troubleshoot GetStream chat issues</p>
            </CardContent>
            <CardFooter>
              <Button asChild variant="outline" size="sm" className="w-full">
                <Link to="/admin/getstream-debug">
                  Debug Chat <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardFooter>
          </Card>

          <Card className={!isServerRunning ? "opacity-60" : ""}>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <Mail className="mr-2 h-5 w-5" />
                Email Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="pb-2">
              <p className="text-sm text-gray-500">Configure email settings and test email delivery</p>
              {!isServerRunning && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-600">
                  Admin server is offline
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button asChild variant="outline" size="sm" className="w-full" disabled={!isServerRunning}>
                <Link to="/admin/email">
                  Email Settings <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <Map className="mr-2 h-5 w-5" />
                Route Explorer
              </CardTitle>
            </CardHeader>
            <CardContent className="pb-2">
              <p className="text-sm text-gray-500">Explore available routes and navigation paths</p>
            </CardContent>
            <CardFooter>
              <Button asChild variant="outline" size="sm" className="w-full">
                <Link to="/route-explorer">
                  Explore Routes <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardFooter>
          </Card>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid grid-cols-4 md:w-[600px]">
            <TabsTrigger value="users" className="flex items-center">
              <Users className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Users</span>
            </TabsTrigger>
            <TabsTrigger value="organizations" className="flex items-center">
              <Building className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Organizations</span>
            </TabsTrigger>
            <TabsTrigger value="stats" className="flex items-center">
              <BarChart3 className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Statistics</span>
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center">
              <Settings className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Settings</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="users" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>User Management</CardTitle>
                <CardDescription>
                  View and manage all users registered on the platform
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AdminUsersList />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="organizations" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Organizations</CardTitle>
                <CardDescription>
                  Manage all organizations on the platform
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AdminOrganizations />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="stats" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Platform Statistics</CardTitle>
                <CardDescription>
                  View key metrics and statistics about your platform
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AdminStats />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Admin Settings</CardTitle>
                <CardDescription>
                  Configure global platform settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AdminSettings />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default AdminDashboard;
