@echo off
echo Creating a test Stripe Connect account using the Stripe CLI...
echo.
echo Step 1: Create a test account
echo Running: stripe connect accounts create --type=express
stripe connect accounts create --type=express
echo.
echo Step 2: Generate an onboarding link
echo Please enter the account ID from the output above:
set /p ACCOUNT_ID="Account ID: "
echo.
echo Running: stripe connect onboarding-links create --account=%ACCOUNT_ID% --refresh-url=http://localhost:8082/stripe-connect --return-url=http://localhost:8082/stripe-connect
stripe connect onboarding-links create --account=%ACCOUNT_ID% --refresh-url=http://localhost:8082/stripe-connect --return-url=http://localhost:8082/stripe-connect
echo.
echo Step 3: Open the onboarding link in your browser
echo Please enter the URL from the output above:
set /p ONBOARDING_URL="Onboarding URL: "
echo.
echo Opening the onboarding link in your browser...
start "" "%ONBOARDING_URL%"
echo.
echo Press any key to continue...
pause > nul