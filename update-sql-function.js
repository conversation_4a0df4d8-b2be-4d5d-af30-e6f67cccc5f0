// <PERSON><PERSON><PERSON> to update the SQL function
import { createClient } from '@supabase/supabase-js';

// SECURITY: Create Supabase client with service role key for admin access - NEVER hardcode keys
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Validate that the service role key is present
if (!supabaseServiceKey) {
  console.error('SECURITY ERROR: SUPABASE_SERVICE_ROLE_KEY not found in environment variables');
  console.error('This script requires the service role key to function');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateSqlFunction() {
  try {
    console.log('Updating SQL function...');

    // Create a new SQL function with SECURITY DEFINER
    const createFunctionSQL = `
    CREATE OR REPLACE FUNCTION public.accept_invitation(token_param TEXT, user_id_param UUID)
    RETURNS BOOLEAN
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    DECLARE
      invitation RECORD;
      user_email TEXT;
    BEGIN
      -- Find the invitation regardless of status
      SELECT * INTO invitation
      FROM public.user_invitations
      WHERE token = token_param
      AND expires_at > now();

      -- If invitation not found or expired
      IF NOT FOUND THEN
        RETURN FALSE;
      END IF;

      -- Get the user's email
      SELECT email INTO user_email
      FROM auth.users
      WHERE id = user_id_param;

      -- Update the user's profile
      UPDATE public.profiles
      SET
        organization_id = invitation.organization_id,
        role = invitation.role,
        email = ARRAY[user_email]
      WHERE id = user_id_param;

      -- Mark invitation as accepted if it's not already
      IF invitation.status != 'accepted' THEN
        UPDATE public.user_invitations
        SET status = 'accepted'
        WHERE token = token_param;
      END IF;

      RETURN TRUE;
    END;
    $$;
    `;

    // Try to execute the SQL directly
    console.log('Trying to execute SQL directly...');

    try {
      // This is a workaround since we can't use the exec_sql function
      // We'll create a temporary table to store the SQL
      const { error: createTableError } = await supabase
        .from('temp_sql')
        .insert({ sql: createFunctionSQL });

      if (createTableError) {
        console.error('Error creating temporary table:', createTableError);

        // Try to create the table first
        const { error: createTempTableError } = await supabase.rpc('exec_sql', {
          sql_query: `
          CREATE TABLE IF NOT EXISTS public.temp_sql (
            id SERIAL PRIMARY KEY,
            sql TEXT,
            created_at TIMESTAMPTZ DEFAULT NOW()
          );
          `
        });

        if (createTempTableError) {
          console.error('Error creating temp_sql table:', createTempTableError);
        } else {
          console.log('Successfully created temp_sql table');

          // Try to insert again
          const { error: insertError } = await supabase
            .from('temp_sql')
            .insert({ sql: createFunctionSQL });

          if (insertError) {
            console.error('Error inserting SQL into temp_sql table:', insertError);
          } else {
            console.log('Successfully inserted SQL into temp_sql table');
          }
        }
      } else {
        console.log('Successfully inserted SQL into temp_sql table');
      }
    } catch (error) {
      console.error('Error executing SQL directly:', error);
    }

    // Test the function with a real invitation
    console.log('\nTesting the function with a real invitation...');

    // Get a test invitation
    const { data: invitations, error: invitationsError } = await supabase
      .from('user_invitations')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();

    if (invitationsError) {
      console.error('Error fetching invitation:', invitationsError);
      return;
    }

    console.log('Using invitation:', invitations);

    // Get the user
    const { data: { users }, error: usersError } = await supabase.auth.admin.listUsers();

    if (usersError) {
      console.error('Error listing users:', usersError);
      return;
    }

    const user = users.find(u => u.email === '<EMAIL>');

    if (!user) {
      console.error('No user found with email: <EMAIL>');
      return;
    }

    console.log('Using user:', user.id);

    // Call the function
    const { data: result, error: functionError } = await supabase.rpc('accept_invitation', {
      token_param: invitations.token,
      user_id_param: user.id
    });

    if (functionError) {
      console.error('Error calling function:', functionError);
    } else {
      console.log('Function result:', result);

      // Check if the profile was updated
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (profileError) {
        console.error('Error fetching profile:', profileError);
      } else {
        console.log('Profile after function call:', profile);
      }
    }

    // Try a direct update as a last resort
    console.log('\nTrying a direct update as a last resort...');

    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        organization_id: invitations.organization_id,
        role: invitations.role,
        email: [user.email]
      })
      .eq('id', user.id);

    if (updateError) {
      console.error('Error updating profile directly:', updateError);
    } else {
      console.log('Successfully updated profile directly');

      // Check if the profile was updated
      const { data: updatedProfile, error: updatedProfileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (updatedProfileError) {
        console.error('Error fetching updated profile:', updatedProfileError);
      } else {
        console.log('Updated profile:', updatedProfile);
      }
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

updateSqlFunction();
