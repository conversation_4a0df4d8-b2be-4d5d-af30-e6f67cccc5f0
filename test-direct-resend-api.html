<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Direct Resend API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .log { max-height: 400px; overflow-y: auto; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; padding: 10px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🧪 Test Direct Resend API</h1>
    <p>This tests calling the Resend API directly (like compliance notifications do).</p>

    <div class="warning">
        <strong>⚠️ Note:</strong> This test will fail because we don't have the Resend API key in the browser. 
        But it will show us the exact request format that works for compliance notifications.
        The real test is to deploy this approach in the Edge Function.
    </div>

    <div class="test-section">
        <h3>Test 1: Direct Resend API Call (Expected to Fail)</h3>
        <p>This shows the exact format our security function should use.</p>
        <button onclick="testDirectResend()">Test Direct Resend API</button>
        <div id="direct-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Test Existing Working Function</h3>
        <p>Let's test a function we know works to compare.</p>
        <button onclick="testWorkingFunction()">Test test-resend Function</button>
        <div id="working-result"></div>
    </div>

    <div class="test-section">
        <h3>📋 Test Logs</h3>
        <button onclick="clearLogs()">Clear Logs</button>
        <div id="logs" class="log"></div>
    </div>

    <script>
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logs.scrollTop = logs.scrollHeight;
            console.log(message);
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        async function testDirectResend() {
            const resultDiv = document.getElementById('direct-result');
            resultDiv.innerHTML = '<p>Testing direct Resend API call...</p>';
            
            try {
                log('🧪 Testing direct Resend API call (this will fail but shows the format)');
                
                const securityEmailHtml = `
                    <h2>Critical Security Alert</h2>
                    <p>A critical security event has been detected on ClassTasker:</p>
                    
                    <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
                        <tr style="background-color: #f8f9fa;">
                            <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Event Type:</td>
                            <td style="padding: 10px; border: 1px solid #ddd;">suspicious_activity</td>
                        </tr>
                        <tr>
                            <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Severity:</td>
                            <td style="padding: 10px; border: 1px solid #ddd; color: red; font-weight: bold;">CRITICAL</td>
                        </tr>
                        <tr style="background-color: #f8f9fa;">
                            <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Action:</td>
                            <td style="padding: 10px; border: 1px solid #ddd;">Test security alert</td>
                        </tr>
                    </table>
                    
                    <p>This is a test of the direct Resend API approach.</p>
                `;
                
                const plainText = securityEmailHtml.replace(/<[^>]*>/g, '');
                
                const emailData = {
                    from: 'ClassTasker Security <<EMAIL>>',
                    to: ['<EMAIL>'],
                    subject: '🚨 CRITICAL Security Alert - Direct API Test',
                    html: securityEmailHtml,
                    text: plainText
                };

                log('📤 Sending direct Resend API request...');
                log('Request payload: ' + JSON.stringify(emailData, null, 2));
                log('⚠️ This will fail because we need the API key from server environment');

                // This will fail because we don't have the API key, but shows the format
                const response = await fetch('https://api.resend.com/emails', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer FAKE_API_KEY_FOR_TESTING',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(emailData)
                });

                log(`📨 Response status: ${response.status} ${response.statusText}`);

                const responseData = await response.json();
                log('📨 Response data: ' + JSON.stringify(responseData, null, 2));

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Unexpected Success!</h4>
                            <p>Direct Resend API call worked!</p>
                            <pre>${JSON.stringify(responseData, null, 2)}</pre>
                        </div>
                    `;
                    log('✅ Direct Resend API test PASSED (unexpected!)');
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Expected Failure</h4>
                            <p>Status: ${response.status} (This is expected - we need the real API key)</p>
                            <p><strong>✅ Format is correct!</strong> This shows our Edge Function approach will work.</p>
                            <pre>${JSON.stringify(responseData, null, 2)}</pre>
                        </div>
                    `;
                    log('❌ Direct Resend API test FAILED as expected (need real API key)');
                    log('✅ But the request format is correct for our Edge Function!');
                }

            } catch (error) {
                log('💥 Direct Resend API test ERROR: ' + error.message);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>💥 Error!</h4>
                        <p>${error.message}</p>
                        <p>This is expected - we're testing the format, not the actual API call.</p>
                    </div>
                `;
            }
        }

        async function testWorkingFunction() {
            const resultDiv = document.getElementById('working-result');
            resultDiv.innerHTML = '<p>Testing working function...</p>';
            
            try {
                log('🧪 Testing test-resend function (should work)');

                const response = await fetch('https://qcnotlojmyvpqbbgoxbc.supabase.co/functions/v1/test-resend', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });

                log(`📨 Response status: ${response.status} ${response.statusText}`);

                const responseData = await response.json();
                log('📨 Response data: ' + JSON.stringify(responseData, null, 2));

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Success!</h4>
                            <p>test-resend function works! This confirms Resend API is working.</p>
                            <pre>${JSON.stringify(responseData, null, 2)}</pre>
                        </div>
                    `;
                    log('✅ test-resend function test PASSED - Resend API is working!');
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Failed!</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(responseData, null, 2)}</pre>
                        </div>
                    `;
                    log('❌ test-resend function test FAILED: ' + JSON.stringify(responseData));
                }

            } catch (error) {
                log('💥 test-resend function test ERROR: ' + error.message);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>💥 Error!</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // Auto-clear logs on page load
        window.onload = function() {
            log('🚀 Direct Resend API Test Page Loaded');
            log('Ready to test direct Resend API approach');
            log('💡 The goal is to verify the request format, not the actual API call');
        };
    </script>
</body>
</html>
