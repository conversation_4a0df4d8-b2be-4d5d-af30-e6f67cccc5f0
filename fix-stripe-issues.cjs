// Comprehensive script to fix Stripe payment issues
const fs = require('fs');
const path = require('path');

// Paths
const processorPath = path.join(__dirname, 'src', 'components', 'stripe', 'EnhancedPaymentProcessor.tsx');
const backupPath = path.join(__dirname, 'src', 'components', 'stripe', 'EnhancedPaymentProcessor.tsx.backup');
const serverPath = path.join(__dirname, 'server', 'api', 'stripe-connect.js');
const serverBackupPath = path.join(__dirname, 'server', 'api', 'stripe-connect.js.backup');

// Create backups
console.log('Creating backups of files...');
fs.copyFileSync(processorPath, backupPath);
fs.copyFileSync(serverPath, serverBackupPath);
console.log(`Backups created at ${backupPath} and ${serverBackupPath}`);

// Fix client-side issues in EnhancedPaymentProcessor.tsx
console.log('\nFixing client-side issues...');
let clientContent = fs.readFileSync(processorPath, 'utf8');

// 1. Fix TabIcon marginRight property
if (clientContent.includes('marginRight: \'8px\'')) {
  console.log('- Fixing TabIcon marginRight property');
  clientContent = clientContent.replace(/marginRight: ['"]8px['"]/g, 'margin: \'0 8px 0 0\'');
}

// 2. Remove the FormRow class which is not supported
if (clientContent.includes('.FormRow')) {
  console.log('- Removing unsupported FormRow class');
  const formRowRegex = /\s*['"]\.FormRow['"]:\s*{\s*marginBottom:\s*['"]12px['"]\s*},/g;
  clientContent = clientContent.replace(formRowRegex, '');
}

// 3. Fix syntax error in comment
if (clientContent.includes('// Save payment method for future use},')) {
  console.log('- Fixing syntax error in comment');
  clientContent = clientContent.replace('// Save payment method for future use},', '// Save payment method for future use');
}

// 4. Remove any setup_future_usage parameter from client-side code
if (clientContent.includes('setup_future_usage: \'off_session\'')) {
  console.log('- Removing setup_future_usage parameter from client-side code');
  clientContent = clientContent.replace(/\s*setup_future_usage: ['"]off_session['"],?\s*/g, '');
}

// Write the modified client content back to the file
fs.writeFileSync(processorPath, clientContent, 'utf8');
console.log('Client-side fixes applied successfully!');

// Fix server-side issues in stripe-connect.js
console.log('\nFixing server-side issues...');
let serverContent = fs.readFileSync(serverPath, 'utf8');

// 1. Modify the payment method options to remove setup_future_usage
if (serverContent.includes('setup_future_usage: \'off_session\'')) {
  console.log('- Removing setup_future_usage parameter from server-side code');
  
  // Remove setup_future_usage from bacs_debit
  serverContent = serverContent.replace(
    /paymentMethodOptions\.bacs_debit = {\s*setup_future_usage: ['"]off_session['"]\s*};/g,
    'paymentMethodOptions.bacs_debit = {};'
  );
  
  // Remove setup_future_usage from sepa_debit
  serverContent = serverContent.replace(
    /paymentMethodOptions\.sepa_debit = {\s*setup_future_usage: ['"]off_session['"]\s*};/g,
    'paymentMethodOptions.sepa_debit = {};'
  );
  
  // Remove setup_future_usage from us_bank_account
  serverContent = serverContent.replace(
    /paymentMethodOptions\.us_bank_account = {\s*setup_future_usage: ['"]off_session['"]\s*};/g,
    'paymentMethodOptions.us_bank_account = {};'
  );
}

// Write the modified server content back to the file
fs.writeFileSync(serverPath, serverContent, 'utf8');
console.log('Server-side fixes applied successfully!');

console.log('\nAll fixes have been applied. Please restart your server and refresh the page.');
console.log('\nIf you need to restore the original files, run:');
console.log(`cp ${backupPath} ${processorPath}`);
console.log(`cp ${serverBackupPath} ${serverPath}`);