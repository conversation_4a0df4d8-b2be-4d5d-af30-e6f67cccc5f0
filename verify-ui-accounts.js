// Script to verify that the UI is no longer showing deleted accounts
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

// Initialize Supabase client with service role key
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or service role key. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// The user ID to check
const userId = '4288cd97-e3ed-4e1d-8d22-abdc0d3f28bd';

async function verifyUIAccounts() {
  try {
    console.log('Verifying that the UI is no longer showing deleted accounts...');
    
    // Check user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, stripe_account_id')
      .eq('id', userId)
      .single();
    
    if (profileError) {
      console.error('Error fetching profile:', profileError);
    } else {
      console.log('User profile:', profile);
      
      if (profile.stripe_account_id === null) {
        console.log('✅ User profile has stripe_account_id set to null');
      } else {
        console.error('❌ User profile still has a Stripe account ID:', profile.stripe_account_id);
      }
    }
    
    // Check all stripe_accounts for this user
    const { data: allAccounts, error: allAccountsError } = await supabase
      .from('stripe_accounts')
      .select('*')
      .eq('user_id', userId);
    
    if (allAccountsError) {
      console.error('Error fetching all accounts:', allAccountsError);
    } else {
      console.log('\nAll Stripe accounts for this user:');
      allAccounts.forEach(account => {
        console.log(`- Account ID: ${account.account_id}, Status: ${account.account_status}`);
      });
      
      // Check for non-deleted accounts
      const activeAccounts = allAccounts.filter(account => account.account_status !== 'deleted');
      if (activeAccounts.length === 0) {
        console.log('✅ No active accounts found for this user');
      } else {
        console.error('❌ User still has active accounts:', activeAccounts);
      }
    }
    
    // Check what the UI would see (excluding deleted accounts)
    const { data: uiAccounts, error: uiAccountsError } = await supabase
      .from('stripe_accounts')
      .select('*')
      .eq('user_id', userId)
      .neq('account_status', 'deleted')
      .maybeSingle();
    
    if (uiAccountsError) {
      console.error('Error fetching UI accounts:', uiAccountsError);
    } else {
      console.log('\nWhat the UI would see (excluding deleted accounts):');
      console.log(uiAccounts || 'No accounts');
      
      if (!uiAccounts) {
        console.log('✅ UI would not show any accounts for this user');
      } else {
        console.error('❌ UI would still show an account for this user:', uiAccounts);
      }
    }
    
    console.log('\nVerification complete.');
    console.log('If you are still seeing an account in the UI, try refreshing the page or restarting the application.');
  } catch (error) {
    console.error('Error verifying UI accounts:', error);
  }
}

// Execute the function
verifyUIAccounts();