/**
 * Support Form Test Script
 * 
 * This script tests the functionality of the support request form.
 * It simulates user interactions and verifies that the form works correctly.
 * 
 * Usage:
 * 1. Open the browser console on the Help page
 * 2. Copy and paste this script into the console
 * 3. The script will run automatically and log the results
 */

(function() {
  console.log('=== SUPPORT FORM TEST SCRIPT ===');
  console.log('Starting test sequence...');

  // Helper function to wait for a specified time
  const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

  // Helper function to log test results
  const logResult = (testName, success, message) => {
    console.log(
      `${success ? '✅' : '❌'} ${testName}: ${message}`
    );
  };

  // Helper function to get form elements
  const getFormElements = () => {
    const nameInput = document.querySelector('input[name="name"]');
    const emailInput = document.querySelector('input[name="email"]');
    const organizationInput = document.querySelector('input[name="organization"]');
    const supportTypeSelect = document.querySelector('button[aria-haspopup="listbox"]');
    const messageTextarea = document.querySelector('textarea[name="message"]');
    const submitButton = Array.from(document.querySelectorAll('button')).find(btn => 
      btn.textContent.includes('Submit Support Request')
    );
    
    return { nameInput, emailInput, organizationInput, supportTypeSelect, messageTextarea, submitButton };
  };

  // Test if the form exists
  const testFormExists = () => {
    const form = document.querySelector('form');
    if (!form) {
      logResult('Form Exists', false, 'Support form not found on the page');
      return false;
    }
    logResult('Form Exists', true, 'Support form found on the page');
    return true;
  };

  // Test if the form fields are populated for logged-in users
  const testFormPopulation = async () => {
    const { nameInput, emailInput, organizationInput } = getFormElements();
    
    // Check if name is populated
    if (nameInput && nameInput.value.trim() !== '') {
      logResult('Name Population', true, `Name field is populated with: ${nameInput.value}`);
    } else {
      logResult('Name Population', false, 'Name field is not populated');
    }
    
    // Check if email is populated
    if (emailInput && emailInput.value.trim() !== '') {
      logResult('Email Population', true, `Email field is populated with: ${emailInput.value}`);
      
      // Check if email field is read-only for authenticated users
      if (emailInput.readOnly) {
        logResult('Email Read-Only', true, 'Email field is correctly set to read-only for authenticated users');
      } else {
        logResult('Email Read-Only', false, 'Email field should be read-only for authenticated users');
      }
    } else {
      logResult('Email Population', false, 'Email field is not populated');
    }
    
    // Check if organization is populated (optional)
    if (organizationInput && organizationInput.value.trim() !== '') {
      logResult('Organization Population', true, `Organization field is populated with: ${organizationInput.value}`);
    } else {
      logResult('Organization Population', true, 'Organization field is not populated (this might be expected)');
    }
  };

  // Test form submission
  const testFormSubmission = async () => {
    const { nameInput, emailInput, supportTypeSelect, messageTextarea, submitButton } = getFormElements();
    
    // Fill in required fields if they're empty
    if (nameInput && nameInput.value.trim() === '') {
      nameInput.value = 'Test User';
      nameInput.dispatchEvent(new Event('input', { bubbles: true }));
      logResult('Form Input', true, 'Set name to "Test User"');
    }
    
    if (emailInput && emailInput.value.trim() === '') {
      emailInput.value = '<EMAIL>';
      emailInput.dispatchEvent(new Event('input', { bubbles: true }));
      logResult('Form Input', true, 'Set email to "<EMAIL>"');
    }
    
    // Select a support type
    if (supportTypeSelect) {
      supportTypeSelect.click();
      await wait(500);
      
      // Find and click the "Technical Support" option
      const options = Array.from(document.querySelectorAll('[role="option"]'));
      const technicalOption = options.find(option => option.textContent.includes('Technical'));
      
      if (technicalOption) {
        technicalOption.click();
        logResult('Form Input', true, 'Selected "Technical Support" from dropdown');
      } else {
        logResult('Form Input', false, 'Could not find "Technical Support" option');
      }
      
      await wait(500);
    }
    
    // Fill in message
    if (messageTextarea) {
      messageTextarea.value = 'This is a test message from the automated test script. Please ignore.';
      messageTextarea.dispatchEvent(new Event('input', { bubbles: true }));
      logResult('Form Input', true, 'Set test message');
    }
    
    // Submit the form
    if (submitButton) {
      console.log('Submitting form...');
      submitButton.click();
      
      // Wait for submission to complete
      await wait(2000);
      
      // Check for success message
      const successAlert = document.querySelector('.bg-green-50');
      if (successAlert) {
        logResult('Form Submission', true, 'Form submitted successfully and success message displayed');
      } else {
        logResult('Form Submission', false, 'Form submitted but no success message displayed');
      }
    } else {
      logResult('Form Submission', false, 'Submit button not found');
    }
  };

  // Main test function
  const runTests = async () => {
    try {
      console.log('Testing if form exists...');
      const formExists = testFormExists();
      if (!formExists) return;
      
      console.log('Testing form field population...');
      await testFormPopulation();
      
      console.log('Testing form submission...');
      await testFormSubmission();
      
      console.log('All tests completed!');
    } catch (error) {
      console.error('Test failed with error:', error);
    }
  };

  // Run the tests
  runTests();
})();
