// Script to verify that the live Stripe API is working correctly
import dotenv from 'dotenv';
import <PERSON>e from 'stripe';

// Load environment variables
dotenv.config();

// Initialize Stripe with the secret key
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;

if (!stripeSecretKey) {
  console.error('Missing Stripe secret key. Check your environment variables.');
  process.exit(1);
}

// Initialize Stripe with the secret key
const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2025-03-31.basil',
});

async function verifyStripeConnection() {
  try {
    console.log('Verifying Stripe API connection...');
    console.log(`Using API key: ${stripeSecretKey.substring(0, 8)}...${stripeSecretKey.substring(stripeSecretKey.length - 4)}`);
    
    // Check if we're in live mode
    const isLiveMode = stripeSecretKey.startsWith('sk_live_');
    console.log(`API mode: ${isLiveMode ? 'LIVE' : 'TEST'}`);
    
    // Get the account details
    const account = await stripe.account.retrieve();
    console.log('Successfully connected to Stripe API!');
    console.log('Account details:');
    console.log(`- Account ID: ${account.id}`);
    console.log(`- Business name: ${account.business_profile?.name || 'Not set'}`);
    console.log(`- Email: ${account.email}`);
    console.log(`- Country: ${account.country}`);
    console.log(`- Created: ${new Date(account.created * 1000).toLocaleString()}`);
    
    // Check the balance
    const balance = await stripe.balance.retrieve();
    console.log('\nAccount balance:');
    for (const balanceItem of balance.available) {
      console.log(`- ${balanceItem.currency.toUpperCase()}: ${(balanceItem.amount / 100).toFixed(2)}`);
    }
    
    console.log('\nVerification complete. The Stripe API is working correctly in live mode.');
  } catch (error) {
    console.error('Error verifying Stripe connection:', error);
    if (error.type === 'StripeAuthenticationError') {
      console.error('Authentication error: The API key may be invalid or revoked.');
    }
    process.exit(1);
  }
}

// Execute the function
verifyStripeConnection();