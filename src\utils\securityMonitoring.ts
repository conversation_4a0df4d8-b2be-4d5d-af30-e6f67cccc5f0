/**
 * Security monitoring and logging utilities
 */

import { supabase } from '@/integrations/supabase/client';

export interface SecurityEvent {
  type: 'auth_failure' | 'permission_denied' | 'suspicious_activity' | 'data_access' | 'admin_action';
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  userEmail?: string;
  action: string;
  resource?: string;
  details?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: string;
}

/**
 * Log security events
 */
export const logSecurityEvent = async (event: Omit<SecurityEvent, 'timestamp'>) => {
  const securityEvent: SecurityEvent = {
    ...event,
    timestamp: new Date().toISOString(),
    ipAddress: await getClientIP(),
    userAgent: navigator.userAgent
  };

  // Log to console in development
  if (import.meta.env.DEV) {
    console.warn('🔒 Security Event:', securityEvent);
  }

  // Store in local storage for debugging (non-sensitive data only)
  try {
    const existingLogs = JSON.parse(localStorage.getItem('security_logs') || '[]');
    const sanitizedEvent = {
      ...securityEvent,
      details: event.severity === 'critical' ? '[REDACTED]' : securityEvent.details
    };
    existingLogs.push(sanitizedEvent);
    
    // Keep only last 50 events
    if (existingLogs.length > 50) {
      existingLogs.splice(0, existingLogs.length - 50);
    }
    
    localStorage.setItem('security_logs', JSON.stringify(existingLogs));
  } catch (error) {
    console.error('Failed to store security log:', error);
  }

  // Send to server for critical events
  if (event.severity === 'critical' || event.severity === 'high') {
    try {
      await sendSecurityAlert(securityEvent);
    } catch (error) {
      console.error('Failed to send security alert:', error);
    }
  }
};

/**
 * Get client IP address (best effort)
 */
const getClientIP = async (): Promise<string> => {
  try {
    // This is a best-effort approach for client-side IP detection
    // In production, you'd want to get this from your server
    const response = await fetch('https://api.ipify.org?format=json');
    const data = await response.json();
    return data.ip || 'unknown';
  } catch {
    return 'unknown';
  }
};

/**
 * Send security alerts for critical events
 */
const sendSecurityAlert = async (event: SecurityEvent) => {
  // In a real application, you'd send this to your security monitoring service
  // For now, we'll log it and potentially send an email notification
  
  try {
    // Call Supabase Edge Function for security alerts
    const { error } = await supabase.functions.invoke('security-alert', {
      body: { event }
    });

    if (error) {
      console.error('Failed to send security alert:', error);
    }
  } catch (error) {
    console.error('Security alert service unavailable:', error);
  }
};

/**
 * Monitor authentication failures
 */
export const logAuthFailure = (email: string, reason: string) => {
  logSecurityEvent({
    type: 'auth_failure',
    severity: 'medium',
    userEmail: email,
    action: 'login_failed',
    details: { reason }
  });
};

/**
 * Monitor permission denials
 */
export const logPermissionDenied = (userId: string, action: string, resource: string) => {
  logSecurityEvent({
    type: 'permission_denied',
    severity: 'medium',
    userId,
    action,
    resource,
    details: { attempted_action: action, denied_resource: resource }
  });
};

/**
 * Monitor suspicious activities
 */
export const logSuspiciousActivity = (userId: string, activity: string, details?: Record<string, any>) => {
  logSecurityEvent({
    type: 'suspicious_activity',
    severity: 'high',
    userId,
    action: activity,
    details
  });
};

/**
 * Monitor admin actions
 */
export const logAdminAction = (userId: string, action: string, resource?: string, details?: Record<string, any>) => {
  logSecurityEvent({
    type: 'admin_action',
    severity: 'medium',
    userId,
    action,
    resource,
    details
  });
};

/**
 * Monitor data access patterns
 */
export const logDataAccess = (userId: string, resource: string, action: 'read' | 'write' | 'delete', details?: Record<string, any>) => {
  logSecurityEvent({
    type: 'data_access',
    severity: 'low',
    userId,
    action: `data_${action}`,
    resource,
    details
  });
};

/**
 * Detect and log potential security threats
 */
export const detectSecurityThreats = {
  /**
   * Detect rapid successive requests (potential DoS)
   */
  rapidRequests: (() => {
    const requestTimes: number[] = [];
    
    return (threshold: number = 10, windowMs: number = 1000) => {
      const now = Date.now();
      requestTimes.push(now);
      
      // Remove old requests outside the window
      while (requestTimes.length > 0 && requestTimes[0] < now - windowMs) {
        requestTimes.shift();
      }
      
      if (requestTimes.length > threshold) {
        logSuspiciousActivity('unknown', 'rapid_requests', {
          requestCount: requestTimes.length,
          timeWindow: windowMs
        });
        return true;
      }
      
      return false;
    };
  })(),

  /**
   * Detect unusual navigation patterns
   */
  unusualNavigation: (() => {
    const navigationHistory: string[] = [];
    
    return (path: string) => {
      navigationHistory.push(path);
      
      // Keep only last 20 navigations
      if (navigationHistory.length > 20) {
        navigationHistory.shift();
      }
      
      // Detect rapid navigation between admin pages
      const adminPaths = navigationHistory.filter(p => p.includes('/admin')).length;
      if (adminPaths > 5 && navigationHistory.length <= 10) {
        logSuspiciousActivity('unknown', 'rapid_admin_navigation', {
          navigationHistory: navigationHistory.slice(-10)
        });
        return true;
      }
      
      return false;
    };
  })(),

  /**
   * Detect potential XSS attempts
   */
  xssAttempt: (input: string) => {
    const xssPatterns = [
      /<script/i,
      /javascript:/i,
      /on\w+\s*=/i,
      /<iframe/i,
      /eval\(/i,
      /expression\(/i
    ];
    
    const hasXSS = xssPatterns.some(pattern => pattern.test(input));
    
    if (hasXSS) {
      logSuspiciousActivity('unknown', 'potential_xss', {
        input: input.substring(0, 100) // Log only first 100 chars
      });
    }
    
    return hasXSS;
  },

  /**
   * Detect potential SQL injection attempts
   */
  sqlInjection: (input: string) => {
    const sqlPatterns = [
      /union\s+select/i,
      /drop\s+table/i,
      /delete\s+from/i,
      /insert\s+into/i,
      /update\s+set/i,
      /exec\s*\(/i,
      /--/,
      /\/\*/
    ];
    
    const hasSQLI = sqlPatterns.some(pattern => pattern.test(input));
    
    if (hasSQLI) {
      logSuspiciousActivity('unknown', 'potential_sql_injection', {
        input: input.substring(0, 100) // Log only first 100 chars
      });
    }
    
    return hasSQLI;
  }
};

/**
 * Get security logs for debugging
 */
export const getSecurityLogs = (): SecurityEvent[] => {
  try {
    return JSON.parse(localStorage.getItem('security_logs') || '[]');
  } catch {
    return [];
  }
};

/**
 * Clear security logs
 */
export const clearSecurityLogs = () => {
  localStorage.removeItem('security_logs');
};

/**
 * Security monitoring hook for React components
 */
export const useSecurityMonitoring = () => {
  const monitorPageAccess = (pageName: string, requiredRole?: string) => {
    logDataAccess('current_user', pageName, 'read', { requiredRole });
  };

  const monitorFormSubmission = (formName: string, data: Record<string, any>) => {
    // Check for potential threats in form data
    Object.entries(data).forEach(([key, value]) => {
      if (typeof value === 'string') {
        detectSecurityThreats.xssAttempt(value);
        detectSecurityThreats.sqlInjection(value);
      }
    });

    logDataAccess('current_user', formName, 'write', { formData: Object.keys(data) });
  };

  const monitorAPICall = (endpoint: string, method: string, success: boolean) => {
    if (!success) {
      logSecurityEvent({
        type: 'suspicious_activity',
        severity: 'low',
        action: 'api_call_failed',
        resource: endpoint,
        details: { method, success }
      });
    }
  };

  return {
    monitorPageAccess,
    monitorFormSubmission,
    monitorAPICall,
    logAuthFailure,
    logPermissionDenied,
    logSuspiciousActivity,
    logAdminAction
  };
};
