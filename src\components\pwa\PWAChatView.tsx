import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import {
  ArrowLeft,
  Send,
  Info,
  WifiOff,
  RefreshCw,
  AlertCircle,
  MessageSquare,
  Image as ImageIcon
} from 'lucide-react';
import { useGetStreamChat } from '@/hooks/use-getstream-chat';
import {
  isOnline,
  isPWA,
  registerConnectivityListeners
} from '@/utils/pwa-utils';
import PWAMobileLayout from './PWAMobileLayout';
import { supabase } from '@/integrations/supabase/client';
import {
  Chat,
  MessageInput,
  MessageList,
  Window,
  Channel as StreamChannel
} from 'stream-chat-react';
import { Channel } from 'stream-chat';

// Import GetStream CSS - using only the official GetStream styles
import 'stream-chat-react/dist/css/v2/index.css';
import '@stream-io/stream-chat-css/dist/v2/css/index.css';

// Import custom CSS for GetStream chat
import '@/styles/getstream-chat.css';
import PWAImageUploader from './PWAImageUploader';

// Custom Input UI component for the PWA chat
const CustomInputUI = (props: any) => {
  const { taskId, channel } = props;
  const [showImageUploader, setShowImageUploader] = useState(false);

  if (showImageUploader) {
    return (
      <PWAImageUploader
        taskId={taskId}
        channel={channel}
        onClose={() => setShowImageUploader(false)}
      />
    );
  }

  return (
    <div className="str-chat__input-container">
      <div className="str-chat__input-flat str-chat__input-flat--send-button-active">
        <div className="str-chat__input-flat-wrapper">
          <div className="str-chat__input-flat--textarea-wrapper">
            {/* Use the default MessageInputFlat component */}
            <MessageInput {...props} focus />
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="str-chat__image-upload-button"
            onClick={() => setShowImageUploader(true)}
          >
            <ImageIcon className="h-5 w-5" />
          </Button>
        </div>
      </div>
    </div>
  );
};

const PWAChatView: React.FC = () => {
  // Get URL parameters
  const { threadId } = useParams<{ threadId: string }>();
  const [searchParams] = useSearchParams();
  const taskId = searchParams.get('task');
  const navigate = useNavigate();
  const { user } = useAuth();

  // Local state
  const [offlineMode, setOfflineMode] = useState(!isOnline());
  const [taskTitle, setTaskTitle] = useState<string>('Chat');
  const [taskStatus, setTaskStatus] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  // Use the GetStream chat hook
  const {
    client,
    channel,
    messages,
    isLoading,
    isSending,
    sendMessage,
    error: streamError
  } = useGetStreamChat({
    taskId: taskId || '',
    threadId
  });

  // Set error if there's a stream error
  useEffect(() => {
    if (streamError) {
      console.error('[PWAChatView] Stream error:', streamError);
      setError(streamError.message || 'Failed to load chat');
    }
  }, [streamError]);

  // Effect to handle online/offline status
  useEffect(() => {
    const cleanup = registerConnectivityListeners(
      // Online callback
      () => {
        setOfflineMode(false);
      },
      // Offline callback
      () => {
        setOfflineMode(true);
      }
    );

    return cleanup;
  }, []);

  // Fetch task details
  useEffect(() => {
    const fetchTaskDetails = async () => {
      if (!taskId) return;

      try {
        const { data, error } = await supabase
          .from('tasks')
          .select('title, status')
          .eq('id', taskId)
          .single();

        if (error) {
          console.error('[PWAChatView] Error fetching task details:', error);
          return;
        }

        if (data) {
          setTaskTitle(data.title || 'Chat');
          setTaskStatus(data.status || '');
        }
      } catch (error) {
        console.error('[PWAChatView] Error in fetchTaskDetails:', error);
      }
    };

    fetchTaskDetails();
  }, [taskId]);

  // Handle back button
  const handleBack = () => {
    navigate(-1);
  };

  // Handle refresh
  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <PWAMobileLayout>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center p-3 border-b bg-white">
          <Button variant="ghost" size="icon" onClick={handleBack} className="mr-2">
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex-1 min-w-0">
            <h1 className="text-lg font-semibold truncate">{taskTitle}</h1>
            {taskStatus && (
              <div className="text-xs text-gray-500">
                Status: <span className="capitalize">{taskStatus}</span>
              </div>
            )}
          </div>
          <Button variant="ghost" size="icon" onClick={() => taskId && navigate(`/tasks/${taskId}`)}>
            <Info className="h-5 w-5" />
          </Button>
        </div>

        {/* Offline warning */}
        {offlineMode && (
          <div className="bg-yellow-50 p-2 text-center text-sm text-yellow-700 flex items-center justify-center">
            <WifiOff className="h-4 w-4 mr-2" />
            You're offline. Messages will be sent when you're back online.
          </div>
        )}

        {/* Error message */}
        {error && (
          <div className="bg-red-50 border-b border-red-200 px-4 py-2 flex items-center justify-center">
            <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
            <span className="text-xs text-red-700">
              {error}
            </span>
          </div>
        )}

        {/* Chat Content */}
        <div className="flex-1 overflow-hidden">
          {isLoading || !client || !channel ? (
            // Loading skeletons
            <div className="p-4 space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className={`flex ${i % 2 === 0 ? '' : 'justify-end'}`}>
                  <div className={`flex ${i % 2 === 0 ? 'flex-row' : 'flex-row-reverse'} items-start max-w-[80%]`}>
                    {i % 2 === 0 && <Skeleton className="h-8 w-8 rounded-full mr-2" />}
                    <div>
                      <Skeleton className="h-4 w-32 mb-1" />
                      <Skeleton className="h-3 w-20" />
                    </div>
                    {i % 2 !== 0 && <Skeleton className="h-8 w-8 rounded-full ml-2" />}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            // GetStream Chat Component
            <Chat client={client} theme="messaging light">
              <StreamChannel channel={channel} Input={(props) => <CustomInputUI {...props} taskId={taskId || ''} channel={channel} />}>
                <Window>
                  <MessageList />
                </Window>
              </StreamChannel>
            </Chat>
          )}
        </div>
      </div>
    </PWAMobileLayout>
  );
};

export default PWAChatView;
