/**
 * This script generates the env-config.js file with environment variables
 * It's meant to be run during the build process
 * CommonJS version for compatibility
 */

const fs = require('fs');
const path = require('path');

try {
  // Read the template file
  const envConfigPath = path.resolve(__dirname, '../public/env-config.js');

  // Check if the file exists
  if (!fs.existsSync(envConfigPath)) {
    console.log('env-config.js does not exist, creating it...');
    const defaultContent = `// This file is dynamically generated during build time to inject environment variables
window.env = {
  VITE_GETSTREAM_API_KEY: "",
  VITE_SUPABASE_URL: "",
  VITE_SUPABASE_ANON_KEY: ""
};`;
    fs.writeFileSync(envConfigPath, defaultContent, 'utf8');
  }

  let envConfigContent = fs.readFileSync(envConfigPath, 'utf8');

  // Log environment variables (without sensitive values)
  console.log('Environment variables available:');
  console.log('VITE_GETSTREAM_API_KEY:', process.env.VITE_GETSTREAM_API_KEY ? '[SET]' : '[NOT SET]');
  console.log('VITE_SUPABASE_URL:', process.env.VITE_SUPABASE_URL ? '[SET]' : '[NOT SET]');
  console.log('VITE_SUPABASE_ANON_KEY:', process.env.VITE_SUPABASE_ANON_KEY ? '[SET]' : '[NOT SET]');

  // SECURITY: Replace environment variables with actual values
  // NEVER include hardcoded fallback keys - this is a security risk
  const envVars = {
    VITE_GETSTREAM_API_KEY: process.env.VITE_GETSTREAM_API_KEY || '',
    VITE_SUPABASE_URL: process.env.VITE_SUPABASE_URL || 'https://qcnotlojmyvpqbbgoxbc.supabase.co',
    VITE_SUPABASE_ANON_KEY: process.env.VITE_SUPABASE_ANON_KEY || ''
  };

  // Validate that required environment variables are present
  if (!envVars.VITE_SUPABASE_ANON_KEY) {
    console.error('SECURITY WARNING: VITE_SUPABASE_ANON_KEY not found in environment variables');
    console.error('The application may not function correctly without this key');
  }

  // Create new content with the actual values
  const newContent = `// This file is dynamically generated during build time to inject environment variables
window.env = {
  VITE_GETSTREAM_API_KEY: "${envVars.VITE_GETSTREAM_API_KEY}",
  VITE_SUPABASE_URL: "${envVars.VITE_SUPABASE_URL}",
  VITE_SUPABASE_ANON_KEY: "${envVars.VITE_SUPABASE_ANON_KEY}"
};`;

  // Replace the entire content
  envConfigContent = newContent;

  // Write the updated file
  fs.writeFileSync(envConfigPath, envConfigContent);

  console.log('Successfully injected environment variables into env-config.js');
} catch (error) {
  console.error('Error generating env-config.js:', error);
  // Create a fallback env-config.js with empty values
  try {
    const envConfigPath = path.resolve(__dirname, '../public/env-config.js');
    const fallbackContent = `// Fallback environment variables due to error in generation script
window.env = {
  VITE_GETSTREAM_API_KEY: "",
  VITE_SUPABASE_URL: "",
  VITE_SUPABASE_ANON_KEY: ""
};`;
    fs.writeFileSync(envConfigPath, fallbackContent, 'utf8');
    console.log('Created fallback env-config.js');
  } catch (fallbackError) {
    console.error('Failed to create fallback env-config.js:', fallbackError);
  }
}

console.log('Environment variables injected into env-config.js');
