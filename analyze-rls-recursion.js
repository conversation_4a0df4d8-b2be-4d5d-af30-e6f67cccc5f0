// <PERSON>ript to analyze RLS policies for potential recursion issues
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';

// Load environment variables
dotenv.config();

// Create Supabase client with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Function to analyze RLS policies for recursion
async function analyzeRlsPolicies() {
  try {
    console.log('=== Analyzing RLS Policies for Recursion Issues ===\n');
    
    // Read the SQL files that contain RLS policies
    const sqlFiles = [
      'sql/fix_rls_properly.sql',
      'sql/fix_profiles_recursion.sql',
      'sql/fix_rls_recursion.sql',
      'sql/emergency_fix_profiles.sql'
    ];
    
    console.log('Analyzing SQL files for RLS policies...\n');
    
    for (const file of sqlFiles) {
      try {
        const sql = fs.readFileSync(file, 'utf8');
        console.log(`Analyzing ${file}:`);
        
        // Look for policies that might cause recursion
        const policies = sql.match(/CREATE POLICY.*?;/gs) || [];
        
        for (const policy of policies) {
          if (policy.includes('profiles') && policy.includes('EXISTS') && policy.includes('FROM profiles')) {
            console.log('\n🔴 POTENTIAL RECURSION ISSUE DETECTED:');
            console.log(policy);
            console.log('\nThis policy queries the profiles table within a policy for the profiles table,');
            console.log('which can cause infinite recursion when the database tries to evaluate the policy.');
            
            // Check if there's a self-reference prevention
            if (!policy.includes('!=') && !policy.includes('<>')) {
              console.log('\n⚠️ No self-reference prevention detected!');
              console.log('The policy should include a condition like "admin_profile.id != profiles.id"');
              console.log('to prevent self-reference and break the recursion loop.');
            }
          }
        }
        
        // Look for SECURITY DEFINER functions that might help avoid recursion
        const functions = sql.match(/CREATE OR REPLACE FUNCTION.*?END;/gs) || [];
        
        for (const func of functions) {
          if (func.includes('SECURITY DEFINER') && func.includes('profiles')) {
            console.log('\n✅ SECURITY DEFINER function found that might help avoid recursion:');
            console.log(func.split('\n')[0]); // Just show the function signature
            console.log('This function can help avoid recursion by elevating privileges and');
            console.log('performing checks without triggering RLS policies.');
          }
        }
        
        console.log('\n---\n');
      } catch (error) {
        console.log(`Could not read ${file}: ${error.message}`);
      }
    }
    
    console.log('=== Analysis Summary ===');
    console.log('Potential causes of recursion in RLS policies:');
    console.log('1. Self-referential policies: Policies that query the profiles table within a policy for the profiles table');
    console.log('2. Missing self-reference prevention: No condition to prevent a row from checking itself');
    console.log('3. Complex nested subqueries: Multiple levels of subqueries that reference the same table');
    console.log('\nRecommended solutions:');
    console.log('1. Use SECURITY DEFINER functions for complex authorization checks');
    console.log('2. Add explicit conditions to prevent self-reference (e.g., admin_profile.id != profiles.id)');
    console.log('3. Simplify policies to avoid nested subqueries');
    console.log('4. Use JWT claims when possible instead of querying the database');
    
  } catch (error) {
    console.error('Error analyzing RLS policies:', error);
  }
}

// Run the analysis
analyzeRlsPolicies();