// <PERSON>ript to create a Stripe account and generate an onboarding link
import dotenv from 'dotenv';
import <PERSON><PERSON> from 'stripe';

// Load environment variables
dotenv.config();

// Initialize Stripe with the secret key
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;

if (!stripeSecretKey) {
  console.error('Missing Stripe secret key. Check your environment variables.');
  process.exit(1);
}

// Initialize Stripe with the secret key
const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2025-03-31.basil',
});

async function createAccountWithLink() {
  try {
    console.log('Creating a new Stripe account...');
    
    // Create a new account in Stripe
    const account = await stripe.accounts.create({
      type: 'express',
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
      business_type: 'individual',
    });
    
    console.log(`Created account: ${account.id}`);
    
    // Generate an onboarding link
    console.log('Generating an onboarding link...');
    
    const accountLink = await stripe.accountLinks.create({
      account: account.id,
      refresh_url: 'https://example.com/refresh',
      return_url: 'https://example.com/return',
      type: 'account_onboarding',
    });
    
    console.log(`Onboarding link: ${accountLink.url}`);
    
    // Clean up by deleting the account
    console.log('Cleaning up by deleting the account...');
    
    const deletedAccount = await stripe.accounts.del(account.id);
    
    console.log(`Deleted account: ${deletedAccount.id} (${deletedAccount.deleted ? 'success' : 'failed'})`);
  } catch (error) {
    console.error('Error:', error.message);
    
    if (error.message.includes('Livemode requests must always be redirected via HTTPS')) {
      console.error('\nERROR: The API key is in live mode, but the redirect URLs are not HTTPS.');
      console.error('This suggests that the API key being used is a live mode key, even though the environment variable is set to a test mode key.');
      console.error('Check if there are any other environment variables or system variables that might be overriding the one in the .env file.');
    }
  }
}

// Execute the function
createAccountWithLink();