@echo off
REM Simplified start script for running the application with Stripe Connect integration

echo Starting the application with Stripe Connect integration...
echo.
echo NOTE: You need to manually apply the SQL in sql/setup_stripe_connect.sql
echo using the Supabase SQL Editor in your Supabase dashboard.
echo.

REM Start the application with the server
call npm run dev
call npm run stripe-server

REM Exit with the status of the last command
exit /b %ERRORLEVEL%