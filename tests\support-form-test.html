<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Support Form Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1 {
      color: #333;
      border-bottom: 2px solid #eee;
      padding-bottom: 10px;
    }
    h2 {
      color: #555;
      margin-top: 30px;
    }
    pre {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      overflow-x: auto;
    }
    button {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 10px 15px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 16px;
      margin: 4px 2px;
      cursor: pointer;
      border-radius: 4px;
    }
    button:hover {
      background-color: #45a049;
    }
    .results {
      margin-top: 20px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
      background-color: #f9f9f9;
    }
    .success {
      color: #4CAF50;
    }
    .error {
      color: #f44336;
    }
    .instructions {
      background-color: #e7f3fe;
      border-left: 6px solid #2196F3;
      padding: 10px;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <h1>Support Form Test</h1>
  
  <div class="instructions">
    <p><strong>Instructions:</strong></p>
    <ol>
      <li>Make sure you're logged in to the ClassTasker application in another tab</li>
      <li>Open the Help page at <a href="http://localhost:8082/help" target="_blank">http://localhost:8082/help</a></li>
      <li>Navigate to the Contact Support section</li>
      <li>Come back to this page and click the "Run Test" button below</li>
    </ol>
  </div>
  
  <h2>Test Script</h2>
  <p>This script will test the support form functionality by:</p>
  <ol>
    <li>Checking if the form exists on the page</li>
    <li>Verifying if user information is pre-populated</li>
    <li>Filling in any missing required fields</li>
    <li>Submitting the form</li>
    <li>Checking if the success message appears</li>
  </ol>
  
  <button id="runTest">Run Test</button>
  <button id="clearResults">Clear Results</button>
  
  <div id="results" class="results">
    <p>Test results will appear here...</p>
  </div>
  
  <h2>Test Script Code</h2>
  <pre id="scriptCode"></pre>
  
  <script>
    // Load the test script
    fetch('../tests/support-form-test.js')
      .then(response => response.text())
      .then(code => {
        document.getElementById('scriptCode').textContent = code;
      })
      .catch(error => {
        document.getElementById('scriptCode').textContent = 'Error loading script: ' + error.message;
      });
    
    // Function to run the test in the Help page
    document.getElementById('runTest').addEventListener('click', function() {
      const helpWindow = window.open('http://localhost:8082/help', '_blank');
      
      if (!helpWindow) {
        document.getElementById('results').innerHTML = '<p class="error">Failed to open the Help page. Please allow popups and try again.</p>';
        return;
      }
      
      // Wait for the page to load
      setTimeout(() => {
        fetch('../tests/support-form-test.js')
          .then(response => response.text())
          .then(code => {
            // Create a function to handle messages from the Help page
            window.addEventListener('message', function(event) {
              if (event.data && event.data.type === 'TEST_RESULT') {
                document.getElementById('results').innerHTML += `<p class="${event.data.success ? 'success' : 'error'}">${event.data.message}</p>`;
              }
            });
            
            // Inject the test script into the Help page
            helpWindow.postMessage({
              type: 'RUN_TEST',
              code: code
            }, '*');
          })
          .catch(error => {
            document.getElementById('results').innerHTML = '<p class="error">Error loading test script: ' + error.message + '</p>';
          });
      }, 2000);
    });
    
    // Clear results
    document.getElementById('clearResults').addEventListener('click', function() {
      document.getElementById('results').innerHTML = '<p>Test results will appear here...</p>';
    });
  </script>
</body>
</html>
