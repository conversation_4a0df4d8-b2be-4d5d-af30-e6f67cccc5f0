// <PERSON><PERSON>t to update the accept_invitation function in the database
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create Supabase client with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateInvitationFunction() {
  try {
    console.log('Updating accept_invitation function...');

    // Read the SQL file
    const sql = fs.readFileSync('./sql/simplified_accept_invitation.sql', 'utf8');

    // Try to execute the SQL directly
    console.log('Executing SQL to update function...');

    // First try: Use the exec_sql RPC function if it exists
    try {
      const { error } = await supabase.rpc('exec_sql', {
        sql_query: sql
      });

      if (error) {
        throw error;
      }

      console.log('Successfully updated function using exec_sql RPC');
    } catch (rpcError) {
      console.error('Error using exec_sql RPC:', rpcError);

      // Second try: Use a direct query to execute the SQL
      console.log('Trying direct query...');

      // This is a workaround since we can't execute arbitrary SQL directly
      // We'll create a temporary table to store the SQL and then execute it
      try {
        // Create a temporary table if it doesn't exist
        const { error: createTableError } = await supabase.rpc('exec_sql', {
          sql_query: `
          CREATE TABLE IF NOT EXISTS public.temp_sql (
            id SERIAL PRIMARY KEY,
            sql TEXT,
            created_at TIMESTAMPTZ DEFAULT NOW()
          );
          `
        });

        if (createTableError) {
          console.error('Error creating temp_sql table:', createTableError);
          throw createTableError;
        }

        // Insert the SQL into the temporary table
        const { error: insertError } = await supabase
          .from('temp_sql')
          .insert({ sql });

        if (insertError) {
          console.error('Error inserting SQL into temp_sql table:', insertError);
          throw insertError;
        }

        console.log('Successfully inserted SQL into temp_sql table');
        console.log('Please execute the SQL manually in the Supabase SQL editor');
      } catch (directError) {
        console.error('Error with direct query:', directError);

        // Last resort: Just print the SQL for manual execution
        console.log('\nPlease execute the following SQL manually in the Supabase SQL editor:');
        console.log(sql);
      }
    }

    // Test the function with a real invitation
    console.log('\nTesting the function with a real invitation...');

    // Get a test invitation
    const { data: invitations, error: invitationsError } = await supabase
      .from('user_invitations')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();

    if (invitationsError) {
      console.error('Error fetching invitation:', invitationsError);
      return;
    }

    console.log('Using invitation:', invitations);

    // Get the user
    const { data: { users }, error: usersError } = await supabase.auth.admin.listUsers();

    if (usersError) {
      console.error('Error listing users:', usersError);
      return;
    }

    const user = users.find(u => u.email === '<EMAIL>');

    if (!user) {
      console.error('No user found with email: <EMAIL>');
      return;
    }

    console.log('Using user:', user.id);

    // Call the function
    const { data: result, error: functionError } = await supabase.rpc('accept_invitation', {
      token_param: invitations.token,
      user_id_param: user.id
    });

    if (functionError) {
      console.error('Error calling function:', functionError);
    } else {
      console.log('Function result:', result);

      // Check if the profile was updated
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (profileError) {
        console.error('Error fetching profile:', profileError);
      } else {
        console.log('Profile after function call:', profile);
      }

      // Check if the invitation status was updated
      const { data: updatedInvitation, error: updatedInvitationError } = await supabase
        .from('user_invitations')
        .select('*')
        .eq('token', invitations.token)
        .single();

      if (updatedInvitationError) {
        console.error('Error fetching updated invitation:', updatedInvitationError);
      } else {
        console.log('Invitation after function call:', updatedInvitation);
      }
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

updateInvitationFunction();
