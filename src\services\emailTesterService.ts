import { EmailConfig } from '@/types/email';

/**
 * Test SMTP connection and send a test email using a serverless function
 */
export async function testSmtpConnection(config: EmailConfig, testEmail: string): Promise<{ success: boolean; message: string }> {
  try {
    if (!config.smtpHost || !config.smtpPort || !config.smtpUsername || !config.smtpPassword) {
      return {
        success: false,
        message: 'Missing SMTP configuration. Please provide host, port, username, and password.'
      };
    }

    if (!config.fromEmail) {
      return {
        success: false,
        message: 'Missing sender email address. Please provide a from email.'
      };
    }

    // Call the Supabase Edge Function to test the email configuration
    console.log('Testing email configuration with:', {
      provider: config.provider,
      host: config.smtpHost,
      port: config.smtpPort,
      secure: config.smtpSecure,
      username: config.smtpUsername,
      // Password is masked for security
    });

    try {
      // Call the test-email Edge Function
      const apiUrl = 'https://qcnotlojmyvpqbbgoxbc.supabase.co/functions/v1/test-email';
      console.log('USING SUPABASE EDGE FUNCTION FOR EMAIL TESTING');
      console.log(`Calling Supabase Edge Function at ${apiUrl}`);

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          config: {
            provider: config.provider,
            fromEmail: config.fromEmail,
            fromName: config.fromName,
            apiKey: config.apiKey,

            smtpHost: config.smtpHost,
            smtpPort: config.smtpPort,
            smtpUsername: config.smtpUsername,
            smtpPassword: config.smtpPassword,
            smtpSecure: config.smtpSecure,
          },
          testEmail: testEmail
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          message: errorData.message || `Error: ${response.status} ${response.statusText}`
        };
      }

      const result = await response.json();
      return {
        success: result.success,
        message: result.message
      };
    } catch (error: any) {
      console.error('Error calling test-email Edge Function:', error);

      // Fallback to client-side validation if the Edge Function call fails
      // Check for common SMTP issues based on the configuration
      if (config.smtpHost?.includes('gmail') && !config.smtpUsername?.includes('@gmail.com')) {
        return {
          success: false,
          message: 'Gmail SMTP requires a full Gmail email address as the username.'
        };
      }

      if (config.smtpHost?.includes('gmail') && config.smtpPassword && config.smtpPassword.length < 16) {
        return {
          success: false,
          message: 'For Gmail, you likely need to use an App Password instead of your regular password. Go to your Google Account > Security > App Passwords to generate one.'
        };
      }

      if (config.smtpPort === 465 && !config.smtpSecure) {
        return {
          success: false,
          message: 'Port 465 typically requires SSL/TLS to be enabled. Try enabling the SSL/TLS option.'
        };
      }

      if (config.smtpPort === 587 && config.smtpSecure) {
        return {
          success: false,
          message: 'Port 587 typically uses STARTTLS, not direct SSL/TLS. Try disabling the SSL/TLS option.'
        };
      }

      return {
        success: false,
        message: `Error testing email: ${error.message}. Please check your network connection and try again.`
      };
    }
  } catch (error: any) {
    console.error('Error testing SMTP connection:', error);
    return {
      success: false,
      message: `Error testing SMTP connection: ${error.message}`
    };
  }
}
