// <PERSON>ript to create 4 tasks in different states: pending review, assigned to maintenance, assigned to support, and public
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Email addresses
const teacherEmail = '<EMAIL>';
const maintenanceEmail = '<EMAIL>';
const supportEmail = '<EMAIL>';

// Function to get user ID by email
async function getUserIdByEmail(email) {
  const { data: authData } = await supabase.auth.admin.listUsers();
  const user = authData.users.find(u => u.email === email);
  return user ? user.id : null;
}

async function createTasksInDifferentStates() {
  try {
    console.log('Creating 4 tasks in different states...');
    
    // Step 1: Get user IDs
    console.log('\nStep 1: Get user IDs');
    
    const teacherId = await getUserIdByEmail(teacherEmail);
    if (!teacherId) {
      console.error(`Teacher with email ${teacherEmail} not found.`);
      return;
    }
    
    const maintenanceId = await getUserIdByEmail(maintenanceEmail);
    if (!maintenanceId) {
      console.error(`Maintenance staff with email ${maintenanceEmail} not found.`);
      return;
    }
    
    const supportId = await getUserIdByEmail(supportEmail);
    if (!supportId) {
      console.error(`Support staff with email ${supportEmail} not found.`);
      return;
    }
    
    console.log(`Teacher ID: ${teacherId}`);
    console.log(`Maintenance Staff ID: ${maintenanceId}`);
    console.log(`Support Staff ID: ${supportId}`);
    
    // Step 2: Create Task 1 - Pending Review
    console.log('\nStep 2: Create Task 1 - Pending Review');
    
    // This simulates a teacher creating a task through the UI
    const task1Data = {
      title: 'Repair Library Shelving',
      description: 'Several shelves in the library are unstable and need to be repaired or replaced. This is becoming a safety concern as books have fallen off.',
      location: 'School Library',
      category: 'Carpentry',
      budget: 200.00,
      due_date: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days from now
      user_id: teacherId,
      status: 'open',
      visibility: 'admin', // This is the default for new tasks
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    const { data: task1, error: task1Error } = await supabase
      .from('tasks')
      .insert(task1Data)
      .select()
      .single();
    
    if (task1Error) {
      console.error('Error creating Task 1:', task1Error);
      return;
    }
    
    console.log(`Created Task 1 (Pending Review): ${task1.title} (${task1.id})`);
    console.log(`- Status: ${task1.status}`);
    console.log(`- Visibility: ${task1.visibility}`);
    
    // Step 3: Create Task 2 - Assigned to Maintenance
    console.log('\nStep 3: Create Task 2 - Assigned to Maintenance');
    
    // First create the task as if submitted by a teacher
    const task2InitialData = {
      title: 'Fix Classroom Door Handle',
      description: 'The door handle in Room 105 is loose and difficult to open. It needs to be tightened or replaced.',
      location: 'Room 105',
      category: 'Maintenance',
      budget: 50.00,
      due_date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days from now
      user_id: teacherId,
      status: 'open',
      visibility: 'admin', // Initial visibility
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    const { data: task2Initial, error: task2InitialError } = await supabase
      .from('tasks')
      .insert(task2InitialData)
      .select()
      .single();
    
    if (task2InitialError) {
      console.error('Error creating initial Task 2:', task2InitialError);
      return;
    }
    
    // Now update it as if an admin assigned it to maintenance
    const task2UpdateData = {
      status: 'assigned',
      visibility: 'internal',
      assigned_to: maintenanceId,
      payment_status: 'not_required',
      updated_at: new Date().toISOString(),
    };
    
    const { data: task2, error: task2Error } = await supabase
      .from('tasks')
      .update(task2UpdateData)
      .eq('id', task2Initial.id)
      .select()
      .single();
    
    if (task2Error) {
      console.error('Error updating Task 2:', task2Error);
      return;
    }
    
    console.log(`Created Task 2 (Assigned to Maintenance): ${task2.title} (${task2.id})`);
    console.log(`- Status: ${task2.status}`);
    console.log(`- Visibility: ${task2.visibility}`);
    console.log(`- Assigned to: ${task2.assigned_to}`);
    
    // Step 4: Create Task 3 - Assigned to Support
    console.log('\nStep 4: Create Task 3 - Assigned to Support');
    
    // First create the task as if submitted by a teacher
    const task3InitialData = {
      title: 'Setup New Classroom Computers',
      description: 'We have received 5 new computers for the computer lab that need to be set up with the school software and connected to the network.',
      location: 'Computer Lab',
      category: 'IT Support',
      budget: 0.00, // No budget for internal IT support
      due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
      user_id: teacherId,
      status: 'open',
      visibility: 'admin', // Initial visibility
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    const { data: task3Initial, error: task3InitialError } = await supabase
      .from('tasks')
      .insert(task3InitialData)
      .select()
      .single();
    
    if (task3InitialError) {
      console.error('Error creating initial Task 3:', task3InitialError);
      return;
    }
    
    // Now update it as if an admin assigned it to support
    const task3UpdateData = {
      status: 'assigned',
      visibility: 'internal',
      assigned_to: supportId,
      payment_status: 'not_required',
      updated_at: new Date().toISOString(),
    };
    
    const { data: task3, error: task3Error } = await supabase
      .from('tasks')
      .update(task3UpdateData)
      .eq('id', task3Initial.id)
      .select()
      .single();
    
    if (task3Error) {
      console.error('Error updating Task 3:', task3Error);
      return;
    }
    
    console.log(`Created Task 3 (Assigned to Support): ${task3.title} (${task3.id})`);
    console.log(`- Status: ${task3.status}`);
    console.log(`- Visibility: ${task3.visibility}`);
    console.log(`- Assigned to: ${task3.assigned_to}`);
    
    // Step 5: Create Task 4 - Public (for suppliers)
    console.log('\nStep 5: Create Task 4 - Public (for suppliers)');
    
    // First create the task as if submitted by a teacher
    const task4InitialData = {
      title: 'Playground Equipment Installation',
      description: 'We need a qualified contractor to install new playground equipment. The equipment has been purchased and is on-site, but needs professional installation.',
      location: 'School Playground',
      category: 'Equipment Installation',
      budget: 500.00,
      due_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days from now
      user_id: teacherId,
      status: 'open',
      visibility: 'admin', // Initial visibility
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    const { data: task4Initial, error: task4InitialError } = await supabase
      .from('tasks')
      .insert(task4InitialData)
      .select()
      .single();
    
    if (task4InitialError) {
      console.error('Error creating initial Task 4:', task4InitialError);
      return;
    }
    
    // Now update it as if an admin made it public
    const task4UpdateData = {
      status: 'open', // Remains open for suppliers to bid
      visibility: 'public',
      assigned_to: null, // No specific assignment for public tasks
      updated_at: new Date().toISOString(),
    };
    
    const { data: task4, error: task4Error } = await supabase
      .from('tasks')
      .update(task4UpdateData)
      .eq('id', task4Initial.id)
      .select()
      .single();
    
    if (task4Error) {
      console.error('Error updating Task 4:', task4Error);
      return;
    }
    
    console.log(`Created Task 4 (Public): ${task4.title} (${task4.id})`);
    console.log(`- Status: ${task4.status}`);
    console.log(`- Visibility: ${task4.visibility}`);
    
    // Step 6: Verify tasks appear in the correct sections
    console.log('\nStep 6: Verify tasks appear in the correct sections');
    
    // Check admin review section (should contain Task 1)
    const { data: adminReviewTasks, error: adminReviewTasksError } = await supabase
      .from('tasks')
      .select('*')
      .eq('status', 'open')
      .eq('visibility', 'admin')
      .order('created_at', { ascending: false });
    
    if (adminReviewTasksError) {
      console.error('Error fetching admin review tasks:', adminReviewTasksError);
      return;
    }
    
    const task1InReview = adminReviewTasks.some(task => task.id === task1.id);
    console.log(`Task 1 "${task1.title}" in admin review section: ${task1InReview ? 'Yes ✅' : 'No ❌'}`);
    
    // Check assigned tasks section (should contain Tasks 2, 3, and 4)
    const { data: assignedTasks, error: assignedTasksError } = await supabase
      .from('tasks')
      .select('*')
      .or('status.eq.assigned,visibility.eq.public,visibility.eq.internal')
      .order('created_at', { ascending: false });
    
    if (assignedTasksError) {
      console.error('Error fetching assigned tasks:', assignedTasksError);
      return;
    }
    
    const task2InAssigned = assignedTasks.some(task => task.id === task2.id);
    const task3InAssigned = assignedTasks.some(task => task.id === task3.id);
    const task4InAssigned = assignedTasks.some(task => task.id === task4.id);
    
    console.log(`Task 2 "${task2.title}" in assigned tasks section: ${task2InAssigned ? 'Yes ✅' : 'No ❌'}`);
    console.log(`Task 3 "${task3.title}" in assigned tasks section: ${task3InAssigned ? 'Yes ✅' : 'No ❌'}`);
    console.log(`Task 4 "${task4.title}" in assigned tasks section: ${task4InAssigned ? 'Yes ✅' : 'No ❌'}`);
    
    console.log('\nAll tasks created successfully!');
    console.log('Summary:');
    console.log(`1. Task 1 (Pending Review): ${task1.title} (${task1.id})`);
    console.log(`   - Status: ${task1.status}`);
    console.log(`   - Visibility: ${task1.visibility}`);
    console.log(`2. Task 2 (Assigned to Maintenance): ${task2.title} (${task2.id})`);
    console.log(`   - Status: ${task2.status}`);
    console.log(`   - Visibility: ${task2.visibility}`);
    console.log(`   - Assigned to: ${task2.assigned_to} (Maintenance)`);
    console.log(`3. Task 3 (Assigned to Support): ${task3.title} (${task3.id})`);
    console.log(`   - Status: ${task3.status}`);
    console.log(`   - Visibility: ${task3.visibility}`);
    console.log(`   - Assigned to: ${task3.assigned_to} (Support)`);
    console.log(`4. Task 4 (Public): ${task4.title} (${task4.id})`);
    console.log(`   - Status: ${task4.status}`);
    console.log(`   - Visibility: ${task4.visibility}`);
    
    console.log('\nYou can now log in to the dashboard to see these tasks in their respective sections:');
    console.log('- Admin: <EMAIL>');
    console.log('- Maintenance: <EMAIL>');
    console.log('- Support: <EMAIL>');
    console.log('- Teacher: <EMAIL>');
    
    return {
      success: true,
      tasks: {
        pendingReview: task1,
        assignedToMaintenance: task2,
        assignedToSupport: task3,
        public: task4
      }
    };
  } catch (error) {
    console.error('Error creating tasks in different states:', error);
    return {
      success: false,
      error
    };
  }
}

createTasksInDifferentStates();
