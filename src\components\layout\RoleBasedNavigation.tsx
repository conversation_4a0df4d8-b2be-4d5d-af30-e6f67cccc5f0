import React from 'react';
import { NavLink } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useRolePermissions } from '@/hooks/useRolePermissions';
import {
  Shield,
  Building,
  Users,
  FileText,
  Settings,
  Tool,
  BookOpen,
  ShoppingBag,
  Headphones
} from 'lucide-react';

/**
 * Navigation component that adapts to the user's role
 */
const RoleBasedNavigation = () => {
  const { user, userRole } = useAuth();
  const { isSiteAdmin, hasPermission } = useRolePermissions();

  if (!user) return null;

  return (
    <nav className="space-y-2">
      {/* Common navigation for all users */}
      <NavLink
        to="/dashboard"
        className={({ isActive }) =>
          `flex items-center px-3 py-2 text-sm ${
            isActive ? "bg-blue-50 text-blue-700 font-medium" : "text-gray-700 hover:bg-gray-100"
          }`
        }
      >
        <FileText className="h-4 w-4 mr-2" />
        Dashboard
      </NavLink>

      {/* Site Admin Links */}
      {isSiteAdmin && (
        <>
          <div className="px-3 py-2">
            <div className="text-xs font-semibold text-red-600 flex items-center">
              <Shield className="h-3 w-3 mr-1" />
              SITE ADMIN
            </div>
          </div>

          <NavLink
            to="/admin/site"
            className={({ isActive }) =>
              `flex items-center px-3 py-2 text-sm ${
                isActive ? "bg-blue-50 text-blue-700 font-medium" : "text-gray-700 hover:bg-gray-100"
              }`
            }
          >
            <Settings className="h-4 w-4 mr-2" />
            Site Settings
          </NavLink>

          <NavLink
            to="/admin/roles"
            className={({ isActive }) =>
              `flex items-center px-3 py-2 text-sm ${
                isActive ? "bg-blue-50 text-blue-700 font-medium" : "text-gray-700 hover:bg-gray-100"
              }`
            }
          >
            <Users className="h-4 w-4 mr-2" />
            Role Management
          </NavLink>
        </>
      )}

      {/* Organization Admin Links */}
      {hasPermission('manage_organizations') && (
        <>
          <div className="px-3 py-2">
            <div className="text-xs font-semibold text-amber-600 flex items-center">
              <Building className="h-3 w-3 mr-1" />
              ORGANISATION
            </div>
          </div>

          <NavLink
            to="/organization/dashboard"
            className={({ isActive }) =>
              `flex items-center px-3 py-2 text-sm ${
                isActive ? "bg-blue-50 text-blue-700 font-medium" : "text-gray-700 hover:bg-gray-100"
              }`
            }
          >
            <Building className="h-4 w-4 mr-2" />
            Organisation Dashboard
          </NavLink>

          <NavLink
            to="/organization/users"
            className={({ isActive }) =>
              `flex items-center px-3 py-2 text-sm ${
                isActive ? "bg-blue-50 text-blue-700 font-medium" : "text-gray-700 hover:bg-gray-100"
              }`
            }
          >
            <Users className="h-4 w-4 mr-2" />
            User Management
          </NavLink>
        </>
      )}

      {/* Teacher Links */}
      {userRole === 'teacher' && (
        <>
          <div className="px-3 py-2">
            <div className="text-xs font-semibold text-green-600 flex items-center">
              <BookOpen className="h-3 w-3 mr-1" />
              TEACHER
            </div>
          </div>

          <NavLink
            to="/tasks/create"
            className={({ isActive }) =>
              `flex items-center px-3 py-2 text-sm ${
                isActive ? "bg-blue-50 text-blue-700 font-medium" : "text-gray-700 hover:bg-gray-100"
              }`
            }
          >
            <FileText className="h-4 w-4 mr-2" />
            Create Task
          </NavLink>

          <NavLink
            to="/tasks/my-tasks"
            className={({ isActive }) =>
              `flex items-center px-3 py-2 text-sm ${
                isActive ? "bg-blue-50 text-blue-700 font-medium" : "text-gray-700 hover:bg-gray-100"
              }`
            }
          >
            <FileText className="h-4 w-4 mr-2" />
            My Tasks
          </NavLink>
        </>
      )}

      {/* Maintenance Staff Links */}
      {userRole === 'maintenance' && (
        <>
          <div className="px-3 py-2">
            <div className="text-xs font-semibold text-blue-600 flex items-center">
              <Tool className="h-3 w-3 mr-1" />
              MAINTENANCE
            </div>
          </div>

          <NavLink
            to="/tasks/assigned"
            className={({ isActive }) =>
              `flex items-center px-3 py-2 text-sm ${
                isActive ? "bg-blue-50 text-blue-700 font-medium" : "text-gray-700 hover:bg-gray-100"
              }`
            }
          >
            <FileText className="h-4 w-4 mr-2" />
            Assigned Tasks
          </NavLink>
        </>
      )}

      {/* Support Staff Links */}
      {userRole === 'support' && (
        <>
          <div className="px-3 py-2">
            <div className="text-xs font-semibold text-purple-600 flex items-center">
              <Headphones className="h-3 w-3 mr-1" />
              SUPPORT
            </div>
          </div>

          <NavLink
            to="/tasks/assigned"
            className={({ isActive }) =>
              `flex items-center px-3 py-2 text-sm ${
                isActive ? "bg-blue-50 text-blue-700 font-medium" : "text-gray-700 hover:bg-gray-100"
              }`
            }
          >
            <FileText className="h-4 w-4 mr-2" />
            Assigned Tasks
          </NavLink>
        </>
      )}

      {/* Supplier Links */}
      {userRole === 'supplier' && (
        <>
          <div className="px-3 py-2">
            <div className="text-xs font-semibold text-indigo-600 flex items-center">
              <ShoppingBag className="h-3 w-3 mr-1" />
              SUPPLIER
            </div>
          </div>

          <NavLink
            to="/tasks/public"
            className={({ isActive }) =>
              `flex items-center px-3 py-2 text-sm ${
                isActive ? "bg-blue-50 text-blue-700 font-medium" : "text-gray-700 hover:bg-gray-100"
              }`
            }
          >
            <FileText className="h-4 w-4 mr-2" />
            Available Tasks
          </NavLink>
        </>
      )}
    </nav>
  );
};

export default RoleBasedNavigation;
