import { EmailConfig, SendEmailParams } from '@/types/email';
import { supabase } from '@/integrations/supabase/client';

/**
 * Send an email using the provided configuration
 */
export async function sendEmail(config: EmailConfig, params: SendEmailParams): Promise<{ success: boolean; message: string }> {
  try {
    // Validate configuration
    if (config.provider === 'none') {
      console.log('No email provider configured. Email would have been sent to:', params.to);
      return {
        success: false,
        message: 'No email provider configured.'
      };
    }

    if (!params.to) {
      return {
        success: false,
        message: 'Missing recipient email address.'
      };
    }

    // Log the email sending request
    console.log('Sending email with configuration:', {
      provider: 'resend', // Always use Resend
      from: 'Classtasker <<EMAIL>>',
      to: params.to,
      subject: params.subject,
      body: params.body ? params.body.substring(0, 100) + '...' : '(empty)'
    });

    // Create plain text version by stripping HTML tags
    const plainText = params.body.replace(/<[^>]*>/g, '');

    try {
      // First try to use the support-email-sender Edge Function which is already set up with Resend
      console.log('Using support-email-sender Edge Function for email sending');

      const { data, error: invokeError } = await supabase.functions.invoke('support-email-sender', {
        body: {
          from: 'Classtasker <<EMAIL>>',
          to: params.to,
          subject: params.subject,
          name: config.fromName || 'Classtasker',
          email: '<EMAIL>',
          support_type: 'Invitation',
          message: plainText,
          html_content: params.body // This will be used directly as the email HTML
        }
      });

      if (invokeError) {
        console.error('Error invoking support-email-sender:', invokeError);
        throw new Error(`Failed to send email: ${invokeError.message}`);
      }

      console.log('Email sent successfully via support-email-sender:', data);
      return {
        success: true,
        message: 'Email sent successfully via Resend API.'
      };
    } catch (edgeFunctionError) {
      console.error('Error using support-email-sender, falling back to test-gmail:', edgeFunctionError);

      try {
        // Fallback to test-gmail Edge Function
        console.log('Using test-gmail Edge Function as fallback');

        const { data, error: fallbackError } = await supabase.functions.invoke('test-gmail', {
          body: {
            to: params.to,
            subject: params.subject,
            html_content: params.body,
            from: `${config.fromName || 'Classtasker'} <<EMAIL>>`
          }
        });

        if (fallbackError) {
          console.error('Error invoking test-gmail:', fallbackError);
          throw new Error(`Failed to send email: ${fallbackError.message}`);
        }

        console.log('Email sent successfully via test-gmail:', data);
        return {
          success: true,
          message: 'Email sent successfully via Resend API (fallback).'
        };
      } catch (fallbackError) {
        console.error('Error using test-gmail, falling back to simulation:', fallbackError);

        // Simulate a small delay to make it feel more realistic
        await new Promise(resolve => setTimeout(resolve, 1000));

        console.log('SIMULATED EMAIL SENDING (all Edge Functions failed)');
        return {
          success: true,
          message: `Email would be sent to ${params.to} using Resend API. (SIMULATED)`
        };
      }
    }
  } catch (error: any) {
    console.error('Error sending email:', error);
    return {
      success: false,
      message: `Error sending email: ${error.message}`
    };
  }
}
