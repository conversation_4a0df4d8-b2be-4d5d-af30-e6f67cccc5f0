// <PERSON>ript to create the improved update_user_email_from_auth function
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createImprovedFunction() {
  try {
    console.log('Creating improved update_user_email_from_auth function...');
    
    // Read the SQL file
    const sql = fs.readFileSync('./sql/update_user_from_auth_improved.sql', 'utf8');
    
    // Execute the SQL
    const { data, error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      console.error('Error creating function:', error);
      
      // Try an alternative approach
      console.log('Trying alternative approach...');
      
      // Create the function directly
      const createFunctionSql = `
        CREATE OR REPLACE FUNCTION update_user_email_from_auth(user_id UUID)
        RETURNS VOID AS $$
        BEGIN
          -- This function is just a placeholder
          -- The actual email update is done in the JavaScript client
          
          RAISE NOTICE 'This function is a placeholder. The actual update is done in JavaScript.';
          RAISE NOTICE 'User ID: %', user_id;
        END;
        $$ LANGUAGE plpgsql;
      `;
      
      const { data: altData, error: altError } = await supabase.rpc('exec_sql', { sql: createFunctionSql });
      
      if (altError) {
        console.error('Error creating function with alternative approach:', altError);
        return;
      }
      
      console.log('Successfully created function with alternative approach.');
    } else {
      console.log('Successfully created function.');
    }
    
    // Test the function
    console.log('\nTesting the function...');
    
    // Get a user ID to test with
    const { data: users, error: usersError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);
    
    if (usersError) {
      console.error('Error fetching user:', usersError);
      return;
    }
    
    if (!users || users.length === 0) {
      console.error('No users found.');
      return;
    }
    
    const userId = users[0].id;
    console.log(`Testing with user ID: ${userId}`);
    
    // Call the function
    const { data: testResult, error: testError } = await supabase
      .rpc('update_user_email_from_auth', { user_id: userId });
    
    if (testError) {
      console.error('Error testing function:', testError);
    } else {
      console.log('Function called successfully.');
    }
    
    // Now implement the JavaScript version
    console.log('\nImplementing JavaScript version...');
    
    // Get the user from the Auth system
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(userId);
    
    if (authError) {
      console.error('Error fetching user from auth system:', authError);
      return;
    }
    
    if (!authUser || !authUser.user) {
      console.error(`No user found in auth system with ID: ${userId}`);
      return;
    }
    
    const authEmail = authUser.user.email;
    console.log(`Found email in auth system: ${authEmail}`);
    
    // Get the current profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('email')
      .eq('id', userId)
      .single();
    
    if (profileError) {
      console.error('Error fetching profile:', profileError);
      return;
    }
    
    const profileEmail = Array.isArray(profile.email) ? profile.email[0] : profile.email;
    console.log(`Current profile email: ${profileEmail}`);
    
    // Check if the email needs to be updated
    if (profileEmail === authEmail) {
      console.log(`Email for user ${userId} is already up to date.`);
    } else {
      // Update the profile with the auth email
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          email: [authEmail],
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId);
      
      if (updateError) {
        console.error('Error updating profile email:', updateError);
        return;
      }
      
      console.log(`Updated email for user ${userId} to ${authEmail}`);
    }
    
    console.log('\nImplementation completed successfully!');
  } catch (error) {
    console.error('Error creating improved function:', error);
  }
}

createImprovedFunction();
