// <PERSON>ript to create a test invoice in the current Stripe environment
require('dotenv').config();
const Stripe = require('stripe');

// Initialize Stripe with the secret key
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;

if (!stripeSecretKey) {
  console.error('Missing Stripe secret key. Check your environment variables.');
  process.exit(1);
}

// Initialize Stripe with the secret key
const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2023-10-16',
});

async function createTestInvoice() {
  try {
    console.log('Creating a test invoice...');
    console.log(`Using API key: ${stripeSecretKey.substring(0, 7)}...`);
    console.log(`API mode: ${stripeSecretKey.startsWith('sk_live_') ? 'LIVE' : 'TEST'}`);
    
    // First, create a customer
    const customer = await stripe.customers.create({
      email: '<EMAIL>',
      name: 'Test Customer',
      description: 'Test customer for invoice email testing',
    });
    
    console.log(`Created customer: ${customer.id}`);
    
    // Create an invoice item
    const invoiceItem = await stripe.invoiceItems.create({
      customer: customer.id,
      amount: 1000, // $10.00
      currency: 'usd',
      description: 'Test Invoice Item',
    });
    
    console.log(`Created invoice item: ${invoiceItem.id}`);
    
    // Create an invoice
    const invoice = await stripe.invoices.create({
      customer: customer.id,
      auto_advance: false, // Don't automatically finalize the invoice
      collection_method: 'send_invoice',
      days_until_due: 30,
    });
    
    console.log(`Created invoice: ${invoice.id}`);
    
    // Finalize the invoice
    const finalizedInvoice = await stripe.invoices.finalizeInvoice(invoice.id);
    
    console.log(`Finalized invoice: ${finalizedInvoice.id}`);
    console.log(`Invoice status: ${finalizedInvoice.status}`);
    console.log(`Invoice total: ${finalizedInvoice.total / 100} ${finalizedInvoice.currency.toUpperCase()}`);
    console.log(`Invoice URL: ${finalizedInvoice.hosted_invoice_url}`);
    
    console.log('\nUse this invoice ID for testing:');
    console.log(finalizedInvoice.id);
    
    return finalizedInvoice;
  } catch (error) {
    console.error('Error creating test invoice:', error);
  }
}

createTestInvoice();
