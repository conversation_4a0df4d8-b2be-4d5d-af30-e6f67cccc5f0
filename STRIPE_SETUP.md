# Stripe Integration Setup

This document provides instructions for setting up and configuring the Stripe integration for the ClassTasker application.

## Stripe Keys

> **IMPORTANT**: Never hardcode Stripe API keys in your application code. Always use environment variables to store sensitive keys.

The application uses Stripe API keys that should be stored in environment variables:

### Test Mode Keys

You'll need to obtain these keys from your Stripe Dashboard:

- **Public Key**: A key starting with `pk_test_...`
- **Secret Key**: A key starting with `sk_test_...`

These keys are associated with your Stripe account and should be kept secure.

## Configuration Files

The Stripe keys are stored in the following configuration files:

### Frontend (.env.local)

Create a `.env.local` file in the root directory with the following content:

```
# Stripe Configuration (Frontend)
VITE_STRIPE_PUBLIC_KEY=your_stripe_public_key_here
VITE_PLATFORM_FEE_PERCENTAGE=20
```

### Backend (server/.env)

Create a `server/.env` file with the following content:

```
# Stripe
STRIPE_SECRET_KEY=your_stripe_secret_key_here
STRIPE_PUBLIC_KEY=your_stripe_public_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here
PLATFORM_FEE_PERCENTAGE=20
```

## Stripe Integration Components

The application uses the following components for Stripe integration:

1. **SimplePaymentProcessor**: A React component that handles the payment flow using Stripe Checkout.
2. **stripeService**: A service that provides methods for creating payments and payment intents.
3. **stripe-connect.js**: A server-side API that handles Stripe Connect functionality and payment processing.

## Testing the Integration

To test the Stripe integration:

1. Start the development server: `npm run dev`
2. Start the Stripe API server: `node server/index.js`
3. Navigate to a task page and click the "Pay" button
4. You should be redirected to the Stripe Checkout page
5. Use the test card number `4242 4242 4242 4242` with any future expiration date and any CVC

## Troubleshooting

If you encounter issues with the Stripe integration:

1. Check that the Stripe keys are correctly set in both `.env.local` and `server/.env`
2. Ensure that the Stripe API server is running on port 3001
3. Check the server logs for any error messages
4. Verify that the Stripe account is in test mode
5. Make sure the public and secret keys match the same Stripe account

## Going to Production

When moving to production:

1. Replace the test keys with production keys
2. Update the environment variables in both `.env.local` and `server/.env`
3. Set `VITE_API_URL` to the production API URL
4. Update the webhook secret for production
5. Test the integration thoroughly before going live
