import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Get Supabase URL and key from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://qcnotlojmyvpqbbgoxbc.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.-wliEddUk77OA8-AsON_nw2okLtRSqW5OfubOLyON5A';

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

console.log(`Using Supabase URL: ${supabaseUrl}`);
console.log(`Using Supabase Key: ${supabaseKey.substring(0, 10)}...`);

// Users to update
const usersToUpdate = [
  {
    email: '<EMAIL>',
    role: 'maintenance',
    first_name: 'Maintenance',
    last_name: 'Staff',
    account_type: 'school'
  },
  {
    email: '<EMAIL>',
    role: 'support',
    first_name: 'Support',
    last_name: 'Staff',
    account_type: 'school'
  },
  {
    email: '<EMAIL>',
    role: 'teacher',
    first_name: 'Test',
    last_name: 'Teacher',
    account_type: 'school'
  }
];

async function updateProfiles() {
  try {
    console.log('Starting profile updates...');

    // Get organization ID
    const { data: organizations, error: orgError } = await supabase
      .from('organizations')
      .select('id')
      .limit(1);

    if (orgError) {
      console.error('Error fetching organization:', orgError);
      return;
    }

    if (!organizations || organizations.length === 0) {
      console.error('No organizations found');
      return;
    }

    const organizationId = organizations[0].id;
    console.log(`Using organization ID: ${organizationId}`);

    // Get all users from auth
    const { data: authData, error: authError } = await supabase.auth.admin.listUsers();

    if (authError) {
      console.error('Error fetching auth users:', authError);
      return;
    }

    // Process each user
    for (const userData of usersToUpdate) {
      console.log(`\nProcessing user: ${userData.email}`);

      // Find user in auth
      const authUser = authData.users.find(u => u.email === userData.email);

      if (!authUser) {
        console.log(`User ${userData.email} not found in auth`);
        continue;
      }

      console.log(`Found user ${userData.email} with ID: ${authUser.id}`);

      // Get profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', authUser.id)
        .single();

      if (profileError) {
        console.error(`Error fetching profile for ${userData.email}:`, profileError);

        // Try to create profile
        console.log(`Attempting to create profile for ${userData.email}`);

        const { error: createError } = await supabase
          .from('profiles')
          .insert({
            id: authUser.id,
            email: [userData.email],
            role: userData.role,
            first_name: userData.first_name,
            last_name: userData.last_name,
            organization_id: organizationId,
            account_type: userData.account_type,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (createError) {
          console.error(`Error creating profile for ${userData.email}:`, createError);
        } else {
          console.log(`Successfully created profile for ${userData.email}`);
        }
      } else {
        console.log(`Found profile for ${userData.email}:`, profile);

        // Update profile
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            email: [userData.email],
            role: userData.role,
            first_name: userData.first_name,
            last_name: userData.last_name,
            organization_id: organizationId,
            account_type: userData.account_type,
            updated_at: new Date().toISOString()
          })
          .eq('id', authUser.id);

        if (updateError) {
          console.error(`Error updating profile for ${userData.email}:`, updateError);
        } else {
          console.log(`Successfully updated profile for ${userData.email}`);
        }
      }
    }

    console.log('\nProfile updates completed');
  } catch (error) {
    console.error('Error in updateProfiles:', error);
  }
}

// Run the function
updateProfiles();
