// Script to update the invoice to be associated with the current user
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// The payment ID from the admin invoice
const paymentId = '5cdedd27-eab4-498b-b27e-e67caa0214be';

// The ID of the user you're currently logged in as (from the previous script output)
const currentUserId = '4288cd97-e3ed-4e1d-8d22-abdc0d3f28bd'; // <EMAIL>

async function updateInvoiceUser() {
  try {
    console.log(`Updating invoice to be associated with user ID: ${currentUserId}`);

    // Update the payment to be associated with the current user
    const { data: updatedPayment, error: updatePaymentError } = await supabase
      .from('payments')
      .update({
        payer_id: currentUserId,
        payee_id: currentUserId,
      })
      .eq('id', paymentId)
      .select()
      .single();

    if (updatePaymentError) {
      console.error('Error updating payment:', updatePaymentError);
      return;
    }

    console.log('Successfully updated payment:');
    console.log(`- ID: ${updatedPayment.id}`);
    console.log(`- Payer ID: ${updatedPayment.payer_id}`);
    console.log(`- Payee ID: ${updatedPayment.payee_id}`);
    console.log(`- Amount: ${updatedPayment.amount} ${updatedPayment.currency}`);
    console.log(`- Status: ${updatedPayment.status}`);

    console.log('\nThe invoice should now appear in the Organization Invoices panel.');
  } catch (error) {
    console.error('Error updating invoice user:', error);
  }
}

updateInvoiceUser();
