// Script to fix the payment methods in the server API
const fs = require('fs');
const path = require('path');

// Paths
const apiPath = path.join(__dirname, 'server', 'api', 'stripe-connect.js');
const backupPath = path.join(__dirname, 'server', 'api', 'stripe-connect.js.bak');

// Create a backup of the original file
console.log('Creating backup of stripe-connect.js...');
fs.copyFileSync(apiPath, backupPath);
console.log(`Backup created at ${backupPath}`);

// Read the file content
console.log('Reading stripe-connect.js...');
let content = fs.readFileSync(apiPath, 'utf8');

// Fix the payment methods
if (content.includes("'paypal',         // PayPal")) {
  console.log('Removing PayPal from payment methods...');
  
  // Remove PayPal from GBP payment methods
  content = content.replace(
    "'card',           // Credit/debit cards\n        'paypal',         // PayPal\n        'revolut_pay',    // Revolut Pay\n        'bacs_debit'      // Direct Debit (UK)",
    "'card',           // Credit/debit cards\n        'revolut_pay',    // Revolut Pay\n        'bacs_debit'      // Direct Debit (UK)"
  );
  
  // Remove PayPal from EUR payment methods
  content = content.replace(
    "'card',\n        'paypal',\n        'sepa_debit'",
    "'card',\n        'sepa_debit'"
  );
  
  // Remove PayPal from USD payment methods
  content = content.replace(
    "'card',\n        'paypal',\n        'us_bank_account'",
    "'card',\n        'us_bank_account'"
  );
  
  // Write the modified content back to the file
  fs.writeFileSync(apiPath, content, 'utf8');
  console.log('Fix applied successfully!');
  
  console.log('\nChanges made:');
  console.log('- Removed PayPal from payment methods for GBP, EUR, and USD');
  
  console.log('\nPlease restart the server and try the payment again.');
} else {
  console.log('Could not find PayPal in payment methods.');
  console.log('Please apply the fix manually:');
  console.log('1. Open server/api/stripe-connect.js');
  console.log('2. Remove PayPal from the payment method types');
}

console.log('\nIf you need to restore the original file, run:');
console.log(`cp ${backupPath} ${apiPath}`);