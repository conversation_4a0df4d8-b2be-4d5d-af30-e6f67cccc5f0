import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, FileText } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const TermsOfService = () => {
  const navigate = useNavigate();

  return (
    <MainLayout>
      <div className="container mx-auto py-8 px-4 max-w-4xl">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" onClick={() => navigate(-1)} className="mr-3">
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center">
            <FileText className="h-6 w-6 text-classtasker-blue mr-2" />
            <h1 className="text-3xl font-bold">Terms of Service</h1>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-xl text-classtasker-blue">ClassTasker Terms of Service</CardTitle>
            <p className="text-gray-600">Last updated: {new Date().toLocaleDateString('en-GB', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
          </CardHeader>
          <CardContent className="prose prose-gray max-w-none">
            <div className="space-y-6">
              <section>
                <h2 className="text-xl font-semibold mb-3">1. Acceptance of Terms</h2>
                <p className="text-gray-700 leading-relaxed">
                  By accessing and using ClassTasker ("the Service"), you accept and agree to be bound by the terms and provision of this agreement. 
                  If you do not agree to abide by the above, please do not use this service.
                </p>
              </section>

              <section>
                <h2 className="text-xl font-semibold mb-3">2. Description of Service</h2>
                <p className="text-gray-700 leading-relaxed mb-3">
                  ClassTasker is a platform that connects educational institutions with qualified maintenance and service providers. 
                  Our service includes:
                </p>
                <ul className="list-disc pl-6 space-y-1 text-gray-700">
                  <li>Task posting and management for schools</li>
                  <li>Marketplace for suppliers to find and bid on tasks</li>
                  <li>Communication tools between schools and suppliers</li>
                  <li>Payment processing and invoicing</li>
                  <li>Compliance tracking and reporting</li>
                </ul>
              </section>

              <section>
                <h2 className="text-xl font-semibold mb-3">3. User Accounts</h2>
                <p className="text-gray-700 leading-relaxed mb-3">
                  To use certain features of the Service, you must register for an account. You agree to:
                </p>
                <ul className="list-disc pl-6 space-y-1 text-gray-700">
                  <li>Provide accurate, current, and complete information</li>
                  <li>Maintain and update your account information</li>
                  <li>Keep your password secure and confidential</li>
                  <li>Accept responsibility for all activities under your account</li>
                </ul>
              </section>

              <section>
                <h2 className="text-xl font-semibold mb-3">4. Acceptable Use</h2>
                <p className="text-gray-700 leading-relaxed mb-3">
                  You agree not to use the Service to:
                </p>
                <ul className="list-disc pl-6 space-y-1 text-gray-700">
                  <li>Violate any applicable laws or regulations</li>
                  <li>Post false, misleading, or fraudulent content</li>
                  <li>Harass, abuse, or harm other users</li>
                  <li>Interfere with the proper functioning of the Service</li>
                  <li>Attempt to gain unauthorized access to any part of the Service</li>
                </ul>
              </section>

              <section>
                <h2 className="text-xl font-semibold mb-3">5. Payment Terms</h2>
                <p className="text-gray-700 leading-relaxed mb-3">
                  For paid services:
                </p>
                <ul className="list-disc pl-6 space-y-1 text-gray-700">
                  <li>Fees are charged in British Pounds (£)</li>
                  <li>Payment is due according to your chosen billing cycle</li>
                  <li>All fees are non-refundable unless otherwise stated</li>
                  <li>We reserve the right to change our pricing with 30 days notice</li>
                </ul>
              </section>

              <section>
                <h2 className="text-xl font-semibold mb-3">6. Privacy and Data Protection</h2>
                <p className="text-gray-700 leading-relaxed">
                  Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the Service, 
                  to understand our practices regarding the collection and use of your personal information.
                </p>
              </section>

              <section>
                <h2 className="text-xl font-semibold mb-3">7. Intellectual Property</h2>
                <p className="text-gray-700 leading-relaxed">
                  The Service and its original content, features, and functionality are and will remain the exclusive property of 
                  ClassTasker and its licensors. The Service is protected by copyright, trademark, and other laws.
                </p>
              </section>

              <section>
                <h2 className="text-xl font-semibold mb-3">8. Limitation of Liability</h2>
                <p className="text-gray-700 leading-relaxed">
                  In no event shall ClassTasker, nor its directors, employees, partners, agents, suppliers, or affiliates, 
                  be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, 
                  loss of profits, data, use, goodwill, or other intangible losses, resulting from your use of the Service.
                </p>
              </section>

              <section>
                <h2 className="text-xl font-semibold mb-3">9. Termination</h2>
                <p className="text-gray-700 leading-relaxed">
                  We may terminate or suspend your account and bar access to the Service immediately, without prior notice or liability, 
                  under our sole discretion, for any reason whatsoever and without limitation, including but not limited to a breach of the Terms.
                </p>
              </section>

              <section>
                <h2 className="text-xl font-semibold mb-3">10. Governing Law</h2>
                <p className="text-gray-700 leading-relaxed">
                  These Terms shall be interpreted and governed by the laws of England and Wales, without regard to its conflict of law provisions. 
                  Any disputes arising from these Terms will be subject to the exclusive jurisdiction of the courts of England and Wales.
                </p>
              </section>

              <section>
                <h2 className="text-xl font-semibold mb-3">11. Changes to Terms</h2>
                <p className="text-gray-700 leading-relaxed">
                  We reserve the right, at our sole discretion, to modify or replace these Terms at any time. 
                  If a revision is material, we will provide at least 30 days notice prior to any new terms taking effect.
                </p>
              </section>

              <section>
                <h2 className="text-xl font-semibold mb-3">12. Contact Information</h2>
                <p className="text-gray-700 leading-relaxed">
                  If you have any questions about these Terms of Service, please contact us at:
                </p>
                <div className="bg-gray-50 p-4 rounded-lg mt-3">
                  <p className="text-gray-700">
                    <strong>Email:</strong> <EMAIL><br />
                    <strong>Address:</strong> ClassTasker Ltd, United Kingdom
                  </p>
                </div>
              </section>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default TermsOfService;
