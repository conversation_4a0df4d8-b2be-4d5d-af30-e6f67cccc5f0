// <PERSON>ript to directly fix the task completion condition in FixedTask.tsx
const fs = require('fs');
const path = require('path');

// Paths
const fixedTaskPath = path.join(__dirname, 'src', 'pages', 'FixedTask.tsx');
const backupPath = path.join(__dirname, 'src', 'pages', 'FixedTask.tsx.bak');

// Create a backup of the original file
console.log('Creating backup of FixedTask.tsx...');
fs.copyFileSync(fixedTaskPath, backupPath);
console.log(`Backup created at ${backupPath}`);

// Read the file content
console.log('Reading FixedTask.tsx...');
let content = fs.readFileSync(fixedTaskPath, 'utf8');

// Find the line with the task completion condition
const taskCompletionRegex = /{\s*\/\*\s*Task Completion Actions.*\*\/\s*}\s*{\s*isTaskOwner\s*&&\s*\(/;
const hasMatch = taskCompletionRegex.test(content);

if (hasMatch) {
  console.log('Found the task completion condition. Applying fix...');
  
  // Replace isTaskOwner with (isTaskOwner || isAdmin)
  content = content.replace(
    /{\s*isTaskOwner\s*&&\s*\(/,
    '{ (isTaskOwner || isAdmin) && ('
  );
  
  // Also update the comment
  content = content.replace(
    /Task Completion Actions - Only shown to task owners/,
    'Task Completion Actions - Shown to task owners and admins'
  );
  
  // Add isAdmin to the debug info
  content = content.replace(
    /{\s*console\.log\('Task Completion Debug:',\s*{\s*isTaskOwner,/,
    '{ console.log(\'Task Completion Debug:\', { isTaskOwner, isAdmin, canManageCompletion: isTaskOwner || isAdmin,'
  );
  
  // Write the modified content back to the file
  fs.writeFileSync(fixedTaskPath, content, 'utf8');
  console.log('Fix applied successfully!');
  
  console.log('\nChanges made:');
  console.log('1. Changed {isTaskOwner && (} to {(isTaskOwner || isAdmin) && (}');
  console.log('2. Updated the comment to include admins');
  console.log('3. Added isAdmin to the debug info');
  
  console.log('\nPlease refresh the page and check if the task completion component is now visible.');
} else {
  console.log('Could not find the task completion condition in the file.');
  console.log('Please apply the fix manually:');
  console.log('1. Open src/pages/FixedTask.tsx');
  console.log('2. Find the line with: {isTaskOwner && (');
  console.log('3. Replace it with: {(isTaskOwner || isAdmin) && (');
}

console.log('\nIf you need to restore the original file, run:');
console.log(`cp ${backupPath} ${fixedTaskPath}`);