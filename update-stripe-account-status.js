// Script to mark a Stripe Connect account as deleted in our database
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

// Initialize Supabase client with service role key
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or service role key. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// The account ID to mark as deleted
const accountId = 'acct_1RG04aPbhRYYtC4f';

async function updateAccountStatus() {
  try {
    console.log(`Marking Stripe Connect account ${accountId} as deleted in database...`);
    
    // Find the account in our database
    const { data: stripeAccount, error: fetchError } = await supabase
      .from('stripe_accounts')
      .select('user_id, account_status')
      .eq('account_id', accountId)
      .maybeSingle();
    
    if (fetchError) {
      console.error('Error fetching account from database:', fetchError);
      process.exit(1);
    }
    
    if (!stripeAccount) {
      console.log(`Account ${accountId} not found in database.`);
      
      // Try to find if there's a user with this account ID in their profile
      const { data: userProfile, error: profileFetchError } = await supabase
        .from('profiles')
        .select('id')
        .eq('stripe_account_id', accountId)
        .maybeSingle();
      
      if (profileFetchError) {
        console.error('Error checking user profiles:', profileFetchError);
      } else if (userProfile) {
        console.log(`Found user profile with this account ID: ${userProfile.id}`);
        
        // Update the user's profile to remove the Stripe account ID
        const { error: profileUpdateError } = await supabase
          .from('profiles')
          .update({ stripe_account_id: null })
          .eq('id', userProfile.id);
        
        if (profileUpdateError) {
          console.error('Error updating user profile:', profileUpdateError);
        } else {
          console.log(`Successfully removed Stripe account ID from user profile ${userProfile.id}`);
        }
      } else {
        console.log('No user profiles found with this account ID.');
      }
      
      process.exit(0);
    }
    
    console.log(`Found account in database: ${JSON.stringify(stripeAccount)}`);
    
    if (stripeAccount.account_status === 'deleted') {
      console.log('Account is already marked as deleted in the database.');
      process.exit(0);
    }
    
    // Update the stripe_accounts table
    const { error: updateError } = await supabase
      .from('stripe_accounts')
      .update({
        account_status: 'deleted',
        updated_at: new Date().toISOString(),
      })
      .eq('account_id', accountId);
    
    if (updateError) {
      console.error('Error updating account status in database:', updateError);
      process.exit(1);
    }
    
    console.log('Successfully updated account status to "deleted" in database');
    
    // Update the user's profile to remove the Stripe account ID
    if (stripeAccount.user_id) {
      const { error: profileError } = await supabase
        .from('profiles')
        .update({ stripe_account_id: null })
        .eq('id', stripeAccount.user_id);
      
      if (profileError) {
        console.error('Error updating user profile:', profileError);
      } else {
        console.log(`Successfully removed Stripe account ID from user profile ${stripeAccount.user_id}`);
      }
    }
    
    console.log('Account cleanup completed successfully.');
  } catch (error) {
    console.error('Error updating account status:', error);
    process.exit(1);
  }
}

// Execute the function
updateAccountStatus();