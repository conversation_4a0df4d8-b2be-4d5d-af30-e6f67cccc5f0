// Script to update the supplier's email from the Auth system
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// The supplier user ID
const supplierId = '18625693-2496-45a4-a1d8-675a9bf2683b';

async function updateSupplierEmail() {
  try {
    console.log(`Updating email for supplier with ID: ${supplierId}`);
    
    // Get the user from the auth.users table
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(supplierId);
    
    if (authError) {
      console.error('Error fetching user from auth system:', authError);
      return;
    }
    
    if (!authUser || !authUser.user) {
      console.log(`No user found in auth system with ID: ${supplierId}`);
      return;
    }
    
    const authEmail = authUser.user.email;
    console.log(`Found email in auth system: ${authEmail}`);
    
    // Update the profile with the real email
    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        email: [authEmail],
        updated_at: new Date().toISOString(),
      })
      .eq('id', supplierId);
    
    if (updateError) {
      console.error('Error updating profile email:', updateError);
      return;
    }
    
    console.log('✅ Profile email updated successfully!');
    
    // Verify the update
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('email')
      .eq('id', supplierId)
      .single();
    
    if (profileError) {
      console.error('Error fetching updated profile:', profileError);
      return;
    }
    
    console.log('Updated profile email:', profile.email);
    
    if (Array.isArray(profile.email) && profile.email[0] === authEmail) {
      console.log('✅ Email updated correctly!');
    } else {
      console.log('❌ Email not updated correctly!');
    }
  } catch (error) {
    console.error('Error updating supplier email:', error);
  }
}

updateSupplierEmail();
