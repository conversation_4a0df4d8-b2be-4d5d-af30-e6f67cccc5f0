// <PERSON>ript to verify that the RLS fix works correctly
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create Supabase client with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Function to verify RLS policies
async function verifyRlsPolicies() {
  try {
    console.log('=== Verifying RLS Policies ===\n');
    
    // 1. Check if RLS is enabled on the profiles table
    console.log('Checking if RLS is enabled on the profiles table...');
    const { data: rlsData, error: rlsError } = await supabase
      .from('pg_tables')
      .select('rowsecurity')
      .eq('tablename', 'profiles')
      .eq('schemaname', 'public')
      .single();
    
    if (rlsError) {
      console.error('Error checking RLS status:', rlsError);
    } else {
      const rlsEnabled = rlsData?.rowsecurity || false;
      console.log(`RLS enabled on profiles table: ${rlsEnabled ? '✅ YES' : '❌ NO'}`);
    }
    
    // 2. Get all profiles to use for testing
    console.log('\nFetching profiles for testing...');
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, role, organization_id')
      .limit(10);
    
    if (profilesError) {
      console.error('Error fetching profiles:', profilesError);
      return;
    }
    
    console.log(`Found ${profiles.length} profiles for testing`);
    
    // 3. Test accessing profiles as different users
    console.log('\nTesting access patterns...');
    
    // Find an admin user and a regular user
    const adminProfile = profiles.find(p => p.role === 'admin');
    const regularProfile = profiles.find(p => p.role !== 'admin');
    
    if (!adminProfile) {
      console.log('No admin user found for testing');
    } else {
      console.log(`Found admin user: ${adminProfile.id}`);
      
      // Test accessing profiles as admin
      console.log('\nTesting admin access to profiles in same organization...');
      
      // Create a client that impersonates the admin
      const adminClient = createClient(supabaseUrl, process.env.SUPABASE_ANON_KEY);
      
      // Set auth context to admin user
      await adminClient.auth.setSession({
        access_token: `{"sub":"${adminProfile.id}","role":"authenticated","organization_id":"${adminProfile.organization_id}"}`,
        refresh_token: ''
      });
      
      // Try to access profiles in the same organization
      console.time('adminQuery');
      const { data: adminProfiles, error: adminError } = await adminClient
        .from('profiles')
        .select('*')
        .eq('organization_id', adminProfile.organization_id)
        .limit(5);
      console.timeEnd('adminQuery');
      
      if (adminError) {
        console.error('❌ Error when admin accesses profiles:', adminError);
        console.log('This suggests the recursion issue may still exist');
      } else {
        console.log(`✅ Admin successfully accessed ${adminProfiles.length} profiles`);
        console.log('This suggests the recursion issue has been fixed');
      }
    }
    
    if (!regularProfile) {
      console.log('No regular user found for testing');
    } else {
      console.log(`\nFound regular user: ${regularProfile.id}`);
      
      // Test accessing profiles as regular user
      console.log('Testing regular user access to profiles...');
      
      // Create a client that impersonates the regular user
      const regularClient = createClient(supabaseUrl, process.env.SUPABASE_ANON_KEY);
      
      // Set auth context to regular user
      await regularClient.auth.setSession({
        access_token: `{"sub":"${regularProfile.id}","role":"authenticated","organization_id":"${regularProfile.organization_id}"}`,
        refresh_token: ''
      });
      
      // Try to access own profile
      console.time('regularOwnQuery');
      const { data: ownProfile, error: ownError } = await regularClient
        .from('profiles')
        .select('*')
        .eq('id', regularProfile.id)
        .single();
      console.timeEnd('regularOwnQuery');
      
      if (ownError) {
        console.error('❌ Error when user accesses own profile:', ownError);
      } else {
        console.log('✅ User successfully accessed own profile');
      }
      
      // Try to access profiles in same organization
      console.time('regularOrgQuery');
      const { data: orgProfiles, error: orgError } = await regularClient
        .from('profiles')
        .select('*')
        .eq('organization_id', regularProfile.organization_id)
        .neq('id', regularProfile.id)
        .limit(5);
      console.timeEnd('regularOrgQuery');
      
      if (orgError) {
        console.error('❌ Error when user accesses organization profiles:', orgError);
      } else {
        console.log(`✅ User successfully accessed ${orgProfiles.length} profiles in their organization`);
      }
    }
    
    console.log('\n=== Verification Summary ===');
    console.log('If all tests passed with ✅, the RLS recursion issue has been fixed.');
    console.log('If any test failed with ❌, further investigation is needed.');
    
  } catch (error) {
    console.error('Error verifying RLS policies:', error);
  }
}

// Run the verification
verifyRlsPolicies();