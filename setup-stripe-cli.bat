@echo off
echo Setting up Stripe CLI for local development...
echo.
echo Step 1: Login to Stripe CLI
echo This will open a browser window to authenticate with Stripe.
echo.
echo Running: stripe login
stripe login
echo.
echo Step 2: Start forwarding events to your local server
echo This will allow <PERSON><PERSON> to send webhook events to your local server.
echo.
echo Running: stripe listen --forward-to http://localhost:3000/api/stripe-webhook
echo.
echo After this completes, copy the webhook signing secret and update your .env file.
echo.
echo Press any key to continue...
pause > nul
stripe listen --forward-to http://localhost:3000/api/stripe-webhook