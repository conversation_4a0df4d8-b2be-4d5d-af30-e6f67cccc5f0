# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Environment variables - SECURITY CRITICAL
.env
.env.local
.env.development
.env.production
.env.test
.env.staging
.env.*
!.env.example

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Test and utility scripts
check-*.js
check-*.cjs
test-*.js
test-*.cjs
apply-*.js
fix-*.js
modify-*.js

# Keep important scripts
!apply-invoices-update.js
!activate-stripe-account.js
!scripts/test-invitation-load.js
.vercel
