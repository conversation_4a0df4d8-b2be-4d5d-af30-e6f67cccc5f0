# PWA Debug Panel

This debug panel helps troubleshoot issues with the PWA, especially for GetStream chat integration.

## Integration Instructions

### Option 1: Using the Higher-Order Component (Recommended)

1. Import the `withDebugPanel` HOC in your main App component:

```tsx
// In App.tsx or your main component
import { withDebugPanel } from './components/debug/DebugPanel';

// Your existing App component
const App: React.FC = () => {
  return (
    // Your app content
  );
};

// Export the wrapped component
export default withDebugPanel(App);
```

### Option 2: Adding the Component Directly

If you prefer to add the debug panel to a specific component:

```tsx
// In your component file
import DebugPanel from './components/debug/DebugPanel';

const YourComponent: React.FC = () => {
  return (
    <>
      {/* Your component content */}
      <DebugPanel />
    </>
  );
};
```

## Usage Instructions

1. **Activate the Debug Panel**: Tap 5 times in the top-left corner of the screen
2. **View Logs**: All console logs will be displayed in the panel
3. **Test GetStream**: Click the "Test GetStream" button to test the GetStream connection
4. **Test PWA**: Click the "Test PWA" button to check PWA status
5. **Clear Logs**: Click the "Clear" button to clear the logs
6. **Close Panel**: Click the "Close" button to hide the panel

## Features

- Captures all console logs (log, error, warn, info)
- Tests GetStream connection and token generation
- Checks PWA status and service worker
- Displays environment variables
- Auto-scrolls to the latest logs

## Troubleshooting GetStream Issues

The debug panel helps identify common GetStream issues:

1. **API Key Issues**: Check if the API key is available in the environment
2. **Token Generation**: Test if the token endpoint is accessible
3. **URL Construction**: Verify that URLs are constructed correctly for PWA mode
4. **Environment Variables**: Check if environment variables are accessible in PWA mode

## Removing the Debug Panel for Production

When you're done debugging, you can:

1. Remove the HOC wrapper:
```tsx
// Change this:
export default withDebugPanel(App);

// Back to this:
export default App;
```

2. Or remove the direct component import and usage.

## Security Note

This debug panel should not be included in production builds that are deployed to end users, as it exposes sensitive information like API keys and tokens. Use it only for development and testing purposes.
