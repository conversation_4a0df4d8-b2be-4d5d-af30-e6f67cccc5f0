import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import PWAInstallButton from '@/components/pwa/PWAInstallButton';
import PWAVersionIndicator from '@/components/pwa/PWAVersionIndicator';
import { isPWA, isOnline, clearAllPWACaches } from '@/utils/pwa-utils';

/**
 * PWA Debug Page
 * This page is used to test and debug PWA functionality
 */
const PWADebug: React.FC = () => {
  const [activeTab, setActiveTab] = useState('info');
  const [isPwaMode, setIsPwaMode] = useState(false);
  const [isOnlineStatus, setIsOnlineStatus] = useState(true);
  const [installPromptSupported, setInstallPromptSupported] = useState(false);
  const [serviceWorkerSupported, setServiceWorkerSupported] = useState(false);
  const [serviceWorkerRegistered, setServiceWorkerRegistered] = useState(false);
  const [cacheApiSupported, setCacheApiSupported] = useState(false);
  const [manifestInfo, setManifestInfo] = useState<any>(null);
  const [cachesCleared, setCachesCleared] = useState(false);

  // Check PWA features on mount
  useEffect(() => {
    // Check if running as PWA
    setIsPwaMode(isPWA());
    
    // Check online status
    setIsOnlineStatus(isOnline());
    
    // Check if install prompt is supported
    setInstallPromptSupported('BeforeInstallPromptEvent' in window);
    
    // Check if service worker is supported
    setServiceWorkerSupported('serviceWorker' in navigator);
    
    // Check if service worker is registered
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistration()
        .then(registration => {
          setServiceWorkerRegistered(!!registration);
        })
        .catch(error => {
          console.error('Error checking service worker registration:', error);
        });
    }
    
    // Check if Cache API is supported
    setCacheApiSupported('caches' in window);
    
    // Get manifest info
    const manifestLink = document.querySelector('link[rel="manifest"]');
    if (manifestLink) {
      fetch(manifestLink.getAttribute('href') || '')
        .then(response => response.json())
        .then(data => {
          setManifestInfo(data);
        })
        .catch(error => {
          console.error('Error fetching manifest:', error);
        });
    }
    
    // Add event listeners for online/offline events
    const handleOnline = () => setIsOnlineStatus(true);
    const handleOffline = () => setIsOnlineStatus(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Handle clearing caches
  const handleClearCaches = () => {
    clearAllPWACaches();
    setCachesCleared(true);
    
    // Reset after 3 seconds
    setTimeout(() => {
      setCachesCleared(false);
    }, 3000);
  };

  return (
    <div className="container max-w-4xl mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">PWA Debug</h1>
          <p className="text-gray-500">Test and debug Progressive Web App functionality</p>
        </div>
        <div className="flex items-center gap-2">
          <PWAVersionIndicator />
          <PWAInstallButton />
        </div>
      </div>
      
      <Tabs defaultValue="info" value={activeTab} onValueChange={setActiveTab} className="mb-6">
        <TabsList className="grid grid-cols-3">
          <TabsTrigger value="info">PWA Info</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="actions">Actions</TabsTrigger>
        </TabsList>
        
        <TabsContent value="info" className="mt-4 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>PWA Status</CardTitle>
              <CardDescription>Current status of the Progressive Web App</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="font-medium">Running as PWA:</span>
                <Badge variant={isPwaMode ? "default" : "outline"}>
                  {isPwaMode ? "Yes" : "No"}
                </Badge>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="font-medium">Online Status:</span>
                <Badge variant={isOnlineStatus ? "default" : "destructive"}>
                  {isOnlineStatus ? "Online" : "Offline"}
                </Badge>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="font-medium">Service Worker:</span>
                <Badge variant={serviceWorkerRegistered ? "default" : "outline"}>
                  {serviceWorkerRegistered ? "Registered" : "Not Registered"}
                </Badge>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Manifest Information</CardTitle>
              <CardDescription>Web App Manifest details</CardDescription>
            </CardHeader>
            <CardContent>
              {manifestInfo ? (
                <div className="space-y-2">
                  <div>
                    <span className="font-medium">Name:</span> {manifestInfo.name}
                  </div>
                  <div>
                    <span className="font-medium">Short Name:</span> {manifestInfo.short_name}
                  </div>
                  <div>
                    <span className="font-medium">Start URL:</span> {manifestInfo.start_url}
                  </div>
                  <div>
                    <span className="font-medium">Display Mode:</span> {manifestInfo.display}
                  </div>
                  <div>
                    <span className="font-medium">Theme Color:</span> {manifestInfo.theme_color}
                  </div>
                  <div>
                    <span className="font-medium">Icons:</span> {manifestInfo.icons?.length || 0} icons
                  </div>
                  <div>
                    <span className="font-medium">Shortcuts:</span> {manifestInfo.shortcuts?.length || 0} shortcuts
                  </div>
                </div>
              ) : (
                <div className="text-gray-500">Loading manifest information...</div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="features" className="mt-4 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Browser Support</CardTitle>
              <CardDescription>PWA feature support in the current browser</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="font-medium">Service Worker API:</span>
                <Badge variant={serviceWorkerSupported ? "default" : "destructive"}>
                  {serviceWorkerSupported ? "Supported" : "Not Supported"}
                </Badge>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="font-medium">Install Prompt:</span>
                <Badge variant={installPromptSupported ? "default" : "outline"}>
                  {installPromptSupported ? "Supported" : "Not Supported"}
                </Badge>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="font-medium">Cache API:</span>
                <Badge variant={cacheApiSupported ? "default" : "destructive"}>
                  {cacheApiSupported ? "Supported" : "Not Supported"}
                </Badge>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>PWA Features</CardTitle>
              <CardDescription>Features implemented in this PWA</CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
                  <span>Offline support for chat messages</span>
                </li>
                <li className="flex items-center">
                  <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
                  <span>Offline support for tasks</span>
                </li>
                <li className="flex items-center">
                  <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
                  <span>Mobile-optimized UI</span>
                </li>
                <li className="flex items-center">
                  <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
                  <span>Installable app</span>
                </li>
                <li className="flex items-center">
                  <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
                  <span>App shortcuts</span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="actions" className="mt-4 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>PWA Actions</CardTitle>
              <CardDescription>Actions to test PWA functionality</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Button 
                  variant="outline" 
                  onClick={handleClearCaches}
                  className="w-full"
                >
                  {cachesCleared ? "Caches Cleared!" : "Clear PWA Caches"}
                </Button>
                <p className="text-xs text-gray-500 mt-1">
                  Clears all cached data stored by the PWA
                </p>
              </div>
              
              <div>
                <Button 
                  variant="outline" 
                  onClick={() => window.location.reload()}
                  className="w-full"
                >
                  Reload Page
                </Button>
                <p className="text-xs text-gray-500 mt-1">
                  Reload the current page
                </p>
              </div>
              
              <div>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    if ('serviceWorker' in navigator) {
                      navigator.serviceWorker.getRegistration().then(registration => {
                        if (registration) {
                          registration.update();
                          alert('Service worker update requested');
                        } else {
                          alert('No service worker registration found');
                        }
                      });
                    } else {
                      alert('Service workers not supported');
                    }
                  }}
                  className="w-full"
                >
                  Update Service Worker
                </Button>
                <p className="text-xs text-gray-500 mt-1">
                  Force an update of the service worker
                </p>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Network Testing</CardTitle>
              <CardDescription>Test offline functionality</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                To test offline functionality, use your browser's developer tools to simulate offline mode:
              </p>
              <ol className="list-decimal list-inside space-y-2 text-sm">
                <li>Open Developer Tools (F12 or Ctrl+Shift+I)</li>
                <li>Go to the Network tab</li>
                <li>Check the "Offline" checkbox</li>
                <li>Try navigating to different pages in the app</li>
              </ol>
            </CardContent>
            <CardFooter>
              <p className="text-xs text-gray-500">
                Note: Some browsers also have a dedicated "Application" tab with PWA tools
              </p>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PWADebug;
