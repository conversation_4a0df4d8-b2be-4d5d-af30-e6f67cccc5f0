# GetStream Token Server Fix

## Issue
The application was trying to connect to a GetStream token server on port 3002, but the API server was running on port 3004, causing connection errors:

```
Failed to load resource: net::ERR_CONNECTION_REFUSED
:3002/api/getstream/token:1
```

## Solution
1. Started the dedicated GetStream token server on port 3002 using:
   ```
   node server/getstream-token-server.js
   ```

2. The GetStream token server is configured to run on port 3002 in `server/getstream-token-server.js`:
   ```javascript
   const app = express();
   const PORT = 3002;
   ```

3. The client is configured to use port 3002 in `src/utils/apiConfig.ts`:
   ```typescript
   if (process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost') {
     return import.meta.env.VITE_API_BASE_URL || 'http://localhost:3002';
   }
   ```

4. The environment variable is set in `.env`:
   ```
   VITE_API_BASE_URL=http://localhost:3002
   ```

## Server Architecture
The application now uses the following servers:

1. **GetStream Token Server** (`server/getstream-token-server.js`):
   - Handles GetStream token generation and channel management
   - Runs on port 3002
   - Started with `node server/getstream-token-server.js`

2. **Stripe API Server** (`server/index.js`):
   - Handles all Stripe-related functionality
   - Runs on port 3001
   - Started with `npm run stripe-server`

3. **API Server** (`server/api-server.cjs`):
   - Handles other API functionality
   - Runs on port 3004
   - Started with `npm run api-server`

4. **Development Server** (Vite):
   - Serves the frontend application
   - Runs on port 8082
   - Started with `npm run dev`

## Deployment Steps
1. Start the GetStream token server:
   ```
   node server/getstream-token-server.js
   ```

2. Start the main application servers:
   ```
   npm run dev:all
   ```

## Vercel Deployment Considerations
For Vercel deployment, the GetStream token functionality should be implemented as an API route in the `api/getstream/token.js` file, which is already set up in the codebase.
