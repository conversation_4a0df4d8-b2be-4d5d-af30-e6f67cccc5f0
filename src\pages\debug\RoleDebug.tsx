import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRolePermissions } from '@/hooks/useRolePermissions';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Shield, User, Building } from 'lucide-react';

/**
 * Debug component to display role and permission information
 */
const RoleDebug = () => {
  const { 
    user, 
    profile, 
    isAdmin, 
    isTeacher, 
    isSupport, 
    isMaintenance, 
    isSiteAdmin: authContextIsSiteAdmin,
    userRole 
  } = useAuth();
  
  const { 
    isSiteAdmin: hookIsSiteAdmin, 
    getAllPermissions 
  } = useRolePermissions();
  
  const permissions = getAllPermissions();
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Role Debug Information</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              User Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm font-medium text-gray-500">User ID:</p>
              <p className="font-mono text-sm">{user?.id || 'Not logged in'}</p>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-500">Email:</p>
              <p>{user?.email || 'Not available'}</p>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-500">Auth Metadata:</p>
              <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40">
                {JSON.stringify(user?.user_metadata, null, 2) || 'No metadata'}
              </pre>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="h-5 w-5 mr-2" />
              Role Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Database Role:</p>
              <p className="font-medium">{userRole || 'None'}</p>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-500">Role Flags:</p>
              <div className="flex flex-wrap gap-2 mt-1">
                <Badge variant={isAdmin ? "default" : "outline"}>
                  Admin: {isAdmin ? 'Yes' : 'No'}
                </Badge>
                <Badge variant={isTeacher ? "default" : "outline"}>
                  Teacher: {isTeacher ? 'Yes' : 'No'}
                </Badge>
                <Badge variant={isSupport ? "default" : "outline"}>
                  Support: {isSupport ? 'Yes' : 'No'}
                </Badge>
                <Badge variant={isMaintenance ? "default" : "outline"}>
                  Maintenance: {isMaintenance ? 'Yes' : 'No'}
                </Badge>
              </div>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-500">Site Admin Status:</p>
              <div className="flex flex-wrap gap-2 mt-1">
                <Badge variant={authContextIsSiteAdmin ? "destructive" : "outline"}>
                  From AuthContext: {authContextIsSiteAdmin ? 'Yes' : 'No'}
                </Badge>
                <Badge variant={hookIsSiteAdmin ? "destructive" : "outline"}>
                  From useRolePermissions: {hookIsSiteAdmin ? 'Yes' : 'No'}
                </Badge>
                <Badge variant={profile?.is_site_admin ? "destructive" : "outline"}>
                  From Profile: {profile?.is_site_admin ? 'Yes' : 'No'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Building className="h-5 w-5 mr-2" />
            Profile Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto max-h-96">
            {JSON.stringify(profile, null, 2) || 'No profile data'}
          </pre>
        </CardContent>
      </Card>
      
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Permissions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {permissions.map(permission => (
              <Badge key={permission} variant="secondary">
                {permission}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RoleDebug;
