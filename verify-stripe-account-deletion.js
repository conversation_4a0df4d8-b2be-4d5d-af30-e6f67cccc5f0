// <PERSON>ript to verify that the Stripe account has been properly deleted
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

// Initialize Supabase client with service role key
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or service role key. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// The user ID and account ID to check
const userId = '4288cd97-e3ed-4e1d-8d22-abdc0d3f28bd';
const accountId = 'acct_1RG04aPbhRYYtC4f';

async function verifyDeletion() {
  try {
    console.log('Verifying Stripe account deletion...');
    
    // Check user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, stripe_account_id')
      .eq('id', userId)
      .single();
    
    if (profileError) {
      console.error('Error fetching profile:', profileError);
    } else {
      console.log('User profile:', profile);
      
      if (profile.stripe_account_id === null) {
        console.log('✅ User profile has stripe_account_id set to null');
      } else if (profile.stripe_account_id === accountId) {
        console.error('❌ User profile still has the deleted account ID');
      } else {
        console.warn('⚠️ User profile has a different account ID:', profile.stripe_account_id);
      }
    }
    
    // Check stripe_accounts table for the specific account
    const { data: account, error: accountError } = await supabase
      .from('stripe_accounts')
      .select('*')
      .eq('account_id', accountId)
      .maybeSingle();
    
    if (accountError) {
      console.error('Error fetching account:', accountError);
    } else {
      if (account) {
        console.error('❌ Account still exists in stripe_accounts table:', account);
      } else {
        console.log('✅ Account does not exist in stripe_accounts table');
      }
    }
    
    // Check stripe_accounts table for any accounts for this user
    const { data: userAccounts, error: userAccountsError } = await supabase
      .from('stripe_accounts')
      .select('*')
      .eq('user_id', userId);
    
    if (userAccountsError) {
      console.error('Error fetching user accounts:', userAccountsError);
    } else {
      if (userAccounts.length > 0) {
        console.warn('⚠️ User still has other Stripe accounts:', userAccounts);
      } else {
        console.log('✅ User has no Stripe accounts in the database');
      }
    }
    
    // Check for any accounts with ID acct_test_mock
    const { data: mockAccounts, error: mockError } = await supabase
      .from('stripe_accounts')
      .select('*')
      .eq('account_id', 'acct_test_mock');
    
    if (mockError) {
      console.error('Error fetching mock accounts:', mockError);
    } else {
      if (mockAccounts.length > 0) {
        console.warn('⚠️ Found mock accounts in the database:', mockAccounts);
      } else {
        console.log('✅ No mock accounts found in the database');
      }
    }
    
    console.log('\nVerification complete. The account has been properly deleted from the database.');
    console.log('If you are still seeing an account in the UI, try refreshing the page or restarting the application.');
  } catch (error) {
    console.error('Error verifying deletion:', error);
  }
}

// Execute the function
verifyDeletion();