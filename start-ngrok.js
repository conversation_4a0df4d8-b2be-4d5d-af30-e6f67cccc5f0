// Script to start ngrok and tunnel our local server
import ngrok from 'ngrok';
import dotenv from 'dotenv';
import fs from 'fs';

// Load environment variables
dotenv.config();

async function startNgrok() {
  try {
    console.log('Starting ngrok tunnel...');
    
    // Connect to ngrok and create a tunnel to our local server
    const url = await ngrok.connect({
      addr: 3000, // The port our server is running on
      region: 'us', // The region to use
    });
    
    console.log(`Ngrok tunnel established at: ${url}`);
    
    // Update the .env file with the ngrok URL
    const envContent = fs.readFileSync('.env', 'utf8');
    
    // Replace the STRIPE_CONNECT_EXPRESS_RETURN_URL and STRIPE_CONNECT_EXPRESS_REFRESH_URL
    const updatedEnvContent = envContent
      .replace(/STRIPE_CONNECT_EXPRESS_RETURN_URL=.*/g, `STRIPE_CONNECT_EXPRESS_RETURN_URL=${url}/api/stripe-connect`)
      .replace(/STRIPE_CONNECT_EXPRESS_REFRESH_URL=.*/g, `STRIPE_CONNECT_EXPRESS_REFRESH_URL=${url}/api/stripe-connect`);
    
    fs.writeFileSync('.env', updatedEnvContent);
    
    console.log('Updated .env file with ngrok URL');
    
    // Update the .env.local file with the ngrok URL
    const envLocalContent = fs.readFileSync('.env.local', 'utf8');
    
    // Replace the VITE_API_URL
    const updatedEnvLocalContent = envLocalContent
      .replace(/VITE_API_URL=.*/g, `VITE_API_URL=${url}`)
      .replace(/VITE_STRIPE_CONNECT_EXPRESS_RETURN_URL=.*/g, `VITE_STRIPE_CONNECT_EXPRESS_RETURN_URL=${url}/api/stripe-connect`)
      .replace(/VITE_STRIPE_CONNECT_EXPRESS_REFRESH_URL=.*/g, `VITE_STRIPE_CONNECT_EXPRESS_REFRESH_URL=${url}/api/stripe-connect`);
    
    fs.writeFileSync('.env.local', updatedEnvLocalContent);
    
    console.log('Updated .env.local file with ngrok URL');
    
    console.log('\nNgrok tunnel is now running. Press Ctrl+C to stop.');
    console.log(`API URL: ${url}`);
    console.log(`Return URL: ${url}/api/stripe-connect`);
    console.log(`Refresh URL: ${url}/api/stripe-connect`);
    
    // Keep the process running
    process.stdin.resume();
    
    // Handle process termination
    process.on('SIGINT', async () => {
      console.log('Stopping ngrok tunnel...');
      await ngrok.kill();
      console.log('Ngrok tunnel stopped');
      process.exit(0);
    });
  } catch (error) {
    console.error('Error starting ngrok tunnel:', error);
    process.exit(1);
  }
}

// Execute the function
startNgrok();