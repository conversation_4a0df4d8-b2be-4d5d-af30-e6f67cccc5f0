/**
 * Chat Debug Component
 * 
 * This component is used to debug chat-related database tables and queries.
 */

import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';

const ChatDebug: React.FC = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [tableInfo, setTableInfo] = useState<any>({});
  const [chatThreads, setChatThreads] = useState<any[]>([]);
  const [messages, setMessages] = useState<any[]>([]);

  // Check if tables exist and their structure
  const checkTables = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Check chat_threads table
      const { data: chatThreadsInfo, error: chatThreadsError } = await supabase
        .from('chat_threads')
        .select('*')
        .limit(5);
      
      // Check task_messages table
      const { data: messagesInfo, error: messagesError } = await supabase
        .from('task_messages')
        .select('*')
        .limit(5);
      
      setTableInfo({
        chatThreads: {
          exists: !chatThreadsError,
          error: chatThreadsError?.message || null,
          sample: chatThreadsInfo || [],
          columns: chatThreadsInfo && chatThreadsInfo.length > 0 
            ? Object.keys(chatThreadsInfo[0]) 
            : []
        },
        messages: {
          exists: !messagesError,
          error: messagesError?.message || null,
          sample: messagesInfo || [],
          columns: messagesInfo && messagesInfo.length > 0 
            ? Object.keys(messagesInfo[0]) 
            : []
        }
      });
    } catch (err) {
      setError(`Error checking tables: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  // Fetch chat threads for the current user
  const fetchChatThreads = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const { data, error } = await supabase
        .from('chat_threads')
        .select(`
          id,
          task_id,
          status,
          created_at,
          updated_at,
          user_id,
          supplier_id,
          task:tasks(id, title, status)
        `)
        .or(`user_id.eq.${user.id},supplier_id.eq.${user.id}`)
        .order('updated_at', { ascending: false });
      
      if (error) {
        throw new Error(`Failed to fetch threads: ${error.message}`);
      }
      
      setChatThreads(data || []);
    } catch (err) {
      setError(`Error fetching chat threads: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  // Fetch messages for a specific thread
  const fetchMessages = async (threadId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error } = await supabase
        .from('task_messages')
        .select(`
          id,
          thread_id,
          sender_id,
          content,
          created_at,
          is_system_message,
          is_read
        `)
        .eq('thread_id', threadId)
        .order('created_at', { ascending: true });
      
      if (error) {
        throw new Error(`Failed to fetch messages: ${error.message}`);
      }
      
      setMessages(data || []);
    } catch (err) {
      setError(`Error fetching messages: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkTables();
  }, []);

  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-6">Chat System Debug</h1>
      
      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      <Tabs defaultValue="tables">
        <TabsList>
          <TabsTrigger value="tables">Database Tables</TabsTrigger>
          <TabsTrigger value="threads">Chat Threads</TabsTrigger>
          <TabsTrigger value="messages">Messages</TabsTrigger>
        </TabsList>
        
        <TabsContent value="tables" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Database Tables Check</CardTitle>
              <CardDescription>
                Checking if chat-related tables exist and their structure
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button 
                onClick={checkTables} 
                disabled={loading}
                className="mb-4"
              >
                {loading ? 'Checking...' : 'Check Tables'}
              </Button>
              
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-2">chat_threads Table</h3>
                  <div className="bg-gray-100 p-4 rounded-md">
                    <p>Exists: <span className={tableInfo.chatThreads?.exists ? 'text-green-600' : 'text-red-600'}>
                      {tableInfo.chatThreads?.exists ? 'Yes' : 'No'}
                    </span></p>
                    {tableInfo.chatThreads?.error && (
                      <p className="text-red-500">Error: {tableInfo.chatThreads.error}</p>
                    )}
                    {tableInfo.chatThreads?.columns?.length > 0 && (
                      <div className="mt-2">
                        <p className="font-medium">Columns:</p>
                        <ul className="list-disc list-inside">
                          {tableInfo.chatThreads.columns.map((col: string) => (
                            <li key={col}>{col}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-2">task_messages Table</h3>
                  <div className="bg-gray-100 p-4 rounded-md">
                    <p>Exists: <span className={tableInfo.messages?.exists ? 'text-green-600' : 'text-red-600'}>
                      {tableInfo.messages?.exists ? 'Yes' : 'No'}
                    </span></p>
                    {tableInfo.messages?.error && (
                      <p className="text-red-500">Error: {tableInfo.messages.error}</p>
                    )}
                    {tableInfo.messages?.columns?.length > 0 && (
                      <div className="mt-2">
                        <p className="font-medium">Columns:</p>
                        <ul className="list-disc list-inside">
                          {tableInfo.messages.columns.map((col: string) => (
                            <li key={col}>{col}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="threads" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Chat Threads</CardTitle>
              <CardDescription>
                Chat threads for the current user
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button 
                onClick={fetchChatThreads} 
                disabled={loading || !user}
                className="mb-4"
              >
                {loading ? 'Loading...' : 'Fetch Chat Threads'}
              </Button>
              
              {chatThreads.length > 0 ? (
                <div className="space-y-4">
                  {chatThreads.map(thread => (
                    <div key={thread.id} className="border p-4 rounded-md">
                      <div className="flex justify-between">
                        <h3 className="font-medium">Thread ID: {thread.id}</h3>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => fetchMessages(thread.id)}
                        >
                          View Messages
                        </Button>
                      </div>
                      <p>Task: {thread.task?.title || 'Unknown'}</p>
                      <p>Status: {thread.status}</p>
                      <p>User ID: {thread.user_id}</p>
                      <p>Supplier ID: {thread.supplier_id}</p>
                      <p>Created: {new Date(thread.created_at).toLocaleString()}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <p>No chat threads found. Click the button to fetch threads.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="messages" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Messages</CardTitle>
              <CardDescription>
                Messages for a selected thread
              </CardDescription>
            </CardHeader>
            <CardContent>
              {messages.length > 0 ? (
                <div className="space-y-4">
                  {messages.map(message => (
                    <div key={message.id} className="border p-4 rounded-md">
                      <p className="font-medium">Message ID: {message.id}</p>
                      <p>Content: {message.content}</p>
                      <p>Sender ID: {message.sender_id}</p>
                      <p>System Message: {message.is_system_message ? 'Yes' : 'No'}</p>
                      <p>Read: {message.is_read ? 'Yes' : 'No'}</p>
                      <p>Created: {new Date(message.created_at).toLocaleString()}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <p>No messages found. Select a thread from the "Chat Threads" tab to view messages.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ChatDebug;
