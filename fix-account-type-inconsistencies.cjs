// Script to fix account type inconsistencies
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixAccountTypeInconsistencies() {
  try {
    console.log('Checking for account type inconsistencies...');
    
    // Step 1: Find profiles where role is 'supplier' but account_type is not 'supplier'
    const { data: supplierRoleProfiles, error: supplierRoleError } = await supabase
      .from('profiles')
      .select('id, email, role, account_type')
      .eq('role', 'supplier')
      .neq('account_type', 'supplier');
    
    if (supplierRoleError) {
      console.error('Error checking for supplier role profiles:', supplierRoleError);
    } else if (supplierRoleProfiles && supplierRoleProfiles.length > 0) {
      console.log(`Found ${supplierRoleProfiles.length} profiles with role='supplier' but account_type!='supplier':`);
      
      for (const profile of supplierRoleProfiles) {
        console.log(`- ID: ${profile.id}, Email: ${Array.isArray(profile.email) ? profile.email[0] : profile.email}, Role: ${profile.role}, Account Type: ${profile.account_type}`);
        
        // Fix the inconsistency
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            account_type: 'supplier',
            updated_at: new Date().toISOString()
          })
          .eq('id', profile.id);
        
        if (updateError) {
          console.error(`Error updating account_type for profile ${profile.id}:`, updateError);
        } else {
          console.log(`✅ Fixed account_type for profile ${profile.id}`);
        }
      }
    } else {
      console.log('No profiles found with role=\'supplier\' but account_type!=\'supplier\'');
    }
    
    // Step 2: Find profiles where account_type is 'supplier' but role is not 'supplier'
    const { data: supplierAccountProfiles, error: supplierAccountError } = await supabase
      .from('profiles')
      .select('id, email, role, account_type')
      .eq('account_type', 'supplier')
      .neq('role', 'supplier');
    
    if (supplierAccountError) {
      console.error('Error checking for supplier account profiles:', supplierAccountError);
    } else if (supplierAccountProfiles && supplierAccountProfiles.length > 0) {
      console.log(`\nFound ${supplierAccountProfiles.length} profiles with account_type='supplier' but role!='supplier':`);
      
      for (const profile of supplierAccountProfiles) {
        console.log(`- ID: ${profile.id}, Email: ${Array.isArray(profile.email) ? profile.email[0] : profile.email}, Role: ${profile.role}, Account Type: ${profile.account_type}`);
        
        // Fix the inconsistency
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            role: 'supplier',
            updated_at: new Date().toISOString()
          })
          .eq('id', profile.id);
        
        if (updateError) {
          console.error(`Error updating role for profile ${profile.id}:`, updateError);
        } else {
          console.log(`✅ Fixed role for profile ${profile.id}`);
        }
      }
    } else {
      console.log('\nNo profiles found with account_type=\'supplier\' but role!=\'supplier\'');
    }
    
    // Step 3: Verify all supplier profiles
    const { data: supplierProfiles, error: supplierError } = await supabase
      .from('profiles')
      .select('id, email, role, account_type')
      .or('role.eq.supplier,account_type.eq.supplier');
    
    if (supplierError) {
      console.error('Error checking supplier profiles:', supplierError);
    } else {
      console.log(`\nFound ${supplierProfiles.length} supplier profiles:`);
      supplierProfiles.forEach(profile => {
        const email = Array.isArray(profile.email) ? profile.email[0] : profile.email;
        console.log(`- ID: ${profile.id}, Email: ${email}, Role: ${profile.role}, Account Type: ${profile.account_type}`);
        
        // Check for any remaining inconsistencies
        if (profile.role === 'supplier' && profile.account_type !== 'supplier') {
          console.log(`⚠️ WARNING: Profile ${profile.id} (${email}) still has inconsistent values!`);
        } else if (profile.account_type === 'supplier' && profile.role !== 'supplier') {
          console.log(`⚠️ WARNING: Profile ${profile.id} (${email}) still has inconsistent values!`);
        }
      });
    }
    
    console.log('\nAccount type consistency check completed!');
  } catch (error) {
    console.error('Error fixing account type inconsistencies:', error);
  }
}

fixAccountTypeInconsistencies();
