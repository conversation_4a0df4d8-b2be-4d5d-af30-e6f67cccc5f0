import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// SECURITY: Get Supabase URL and key from environment variables - NEVER hardcode service role keys
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://qcnotlojmyvpqbbgoxbc.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Validate that the service role key is present
if (!supabaseKey) {
  console.error('SECURITY ERROR: SUPABASE_SERVICE_ROLE_KEY not found in environment variables');
  console.error('This script requires the service role key to function');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

console.log(`Using Supabase URL: ${supabaseUrl}`);
console.log(`Using Supabase Key: ${supabaseKey.substring(0, 10)}...`);

// Users to create
const usersToCreate = [
  {
    email: '<EMAIL>',
    password: 'test-password-123',
    role: 'maintenance',
    first_name: 'Maintenance',
    last_name: 'Staff',
    account_type: 'school'
  },
  {
    email: '<EMAIL>',
    password: 'test-password-123',
    role: 'support',
    first_name: 'Support',
    last_name: 'Staff',
    account_type: 'school'
  },
  {
    email: '<EMAIL>',
    password: 'test-password-123',
    role: 'teacher',
    first_name: 'Test',
    last_name: 'Teacher',
    account_type: 'school'
  }
];

async function createUsers() {
  try {
    console.log('Starting user creation...');

    // Get organization ID
    const { data: organizations, error: orgError } = await supabase
      .from('organizations')
      .select('id')
      .limit(1);

    if (orgError) {
      console.error('Error fetching organization:', orgError);
      return;
    }

    if (!organizations || organizations.length === 0) {
      console.error('No organizations found');
      return;
    }

    const organizationId = organizations[0].id;
    console.log(`Using organization ID: ${organizationId}`);

    // Process each user
    for (const userData of usersToCreate) {
      console.log(`\nProcessing user: ${userData.email}`);

      // Check if user already exists
      const { data: existingUsers, error: checkError } = await supabase.auth.admin.listUsers();

      if (checkError) {
        console.error('Error checking existing users:', checkError);
        continue;
      }

      const existingUser = existingUsers.users.find(u => u.email === userData.email);

      if (existingUser) {
        console.log(`User ${userData.email} already exists with ID: ${existingUser.id}`);

        // Check if profile exists
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', existingUser.id)
          .single();

        if (profileError && profileError.code !== 'PGRST116') {
          console.error(`Error checking profile for ${userData.email}:`, profileError);
          continue;
        }

        if (profile) {
          console.log(`Profile already exists for ${userData.email}`);

          // Update profile
          const { error: updateError } = await supabase
            .from('profiles')
            .update({
              role: userData.role,
              first_name: userData.first_name,
              last_name: userData.last_name,
              organization_id: organizationId,
              account_type: userData.account_type,
              updated_at: new Date().toISOString()
            })
            .eq('id', existingUser.id);

          if (updateError) {
            console.error(`Error updating profile for ${userData.email}:`, updateError);
          } else {
            console.log(`Successfully updated profile for ${userData.email}`);
          }
        } else {
          console.log(`Creating profile for existing user ${userData.email}`);

          // Create profile
          const { error: createProfileError } = await supabase
            .from('profiles')
            .insert({
              id: existingUser.id,
              email: [userData.email],
              role: userData.role,
              first_name: userData.first_name,
              last_name: userData.last_name,
              organization_id: organizationId,
              account_type: userData.account_type,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (createProfileError) {
            console.error(`Error creating profile for ${userData.email}:`, createProfileError);
          } else {
            console.log(`Successfully created profile for ${userData.email}`);
          }
        }
      } else {
        console.log(`Creating new user: ${userData.email}`);

        // Create user in auth
        const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
          email: userData.email,
          password: userData.password,
          email_confirm: true,
          user_metadata: {
            first_name: userData.first_name,
            last_name: userData.last_name,
            role: userData.role,
            account_type: userData.account_type
          }
        });

        if (createError) {
          console.error(`Error creating user ${userData.email}:`, createError);
          continue;
        }

        console.log(`Created user ${userData.email} with ID: ${newUser.user.id}`);

        // Create profile manually using SQL
        const sql = `
          INSERT INTO profiles (
            id,
            email,
            role,
            first_name,
            last_name,
            organization_id,
            account_type,
            created_at,
            updated_at
          )
          VALUES (
            '${newUser.user.id}',
            ARRAY['${userData.email}'],
            '${userData.role}',
            '${userData.first_name}',
            '${userData.last_name}',
            '${organizationId}',
            '${userData.account_type}',
            NOW(),
            NOW()
          )
          ON CONFLICT (id) DO UPDATE
          SET
            email = ARRAY['${userData.email}'],
            role = '${userData.role}',
            first_name = '${userData.first_name}',
            last_name = '${userData.last_name}',
            organization_id = '${organizationId}',
            account_type = '${userData.account_type}',
            updated_at = NOW();
        `;

        const { error: sqlError } = await supabase.rpc('exec_sql', { sql_query: sql });

        if (sqlError) {
          console.error(`Error creating profile with SQL for ${userData.email}:`, sqlError);

          // Try direct insert as fallback
          const { error: insertError } = await supabase
            .from('profiles')
            .insert({
              id: newUser.user.id,
              email: [userData.email],
              role: userData.role,
              first_name: userData.first_name,
              last_name: userData.last_name,
              organization_id: organizationId,
              account_type: userData.account_type,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (insertError) {
            console.error(`Error creating profile with direct insert for ${userData.email}:`, insertError);
          } else {
            console.log(`Successfully created profile for ${userData.email} with direct insert`);
          }
        } else {
          console.log(`Successfully created profile for ${userData.email} with SQL`);
        }
      }
    }

    console.log('\nUser creation completed');
  } catch (error) {
    console.error('Error in createUsers:', error);
  }
}

// Run the function
createUsers();
