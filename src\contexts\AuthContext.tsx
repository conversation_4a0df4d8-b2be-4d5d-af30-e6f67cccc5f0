
import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Session, User } from "@supabase/supabase-js";
import { supabase } from "@/integrations/supabase/client";
import { supabaseAdmin } from "@/services/supabaseAdmin";
import { toast } from "@/hooks/use-toast";
import { shouldBypassAuthRedirect } from "@/utils/routeUtils";
import { isPWA, isOnline, storeProfileData, getCachedProfileData, clearProfileCache } from '@/utils/pwa-utils';
import InvitationSyncService from "@/services/invitationSyncService";

type Profile = {
  id: string;
  account_type: string;
  first_name?: string;
  last_name?: string;
  bio?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
  organization_id?: string;
  role?: string;
  is_site_admin?: boolean;
  business_name?: string;
  business_description?: string;
  address?: string;
  city?: string;
  postcode?: string;
  phone?: string;
  website?: string;
  services?: string;
  stripe_account_id?: string;
}

type AuthContextType = {
  session: Session | null;
  user: User | null;
  profile: Profile | null;
  isLoading: boolean;
  isLoadingProfile: boolean;
  isSchool: boolean;
  isSupplier: boolean;
  isAdmin: boolean;
  isTeacher: boolean;
  isSupport: boolean;
  isMaintenance: boolean;
  isSiteAdmin: boolean;
  organizationId: string | null;
  userRole: string | null;
  signUp: (email: string, password: string, name: string, accountType: string) => Promise<void>;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  updateUserProfile: (updates: { first_name?: string; last_name?: string; bio?: string; avatar_url?: string }) => Promise<void>;
  updateUserRole: (role: string) => Promise<void>;
  acceptInvitation: (token: string) => Promise<boolean>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingProfile, setIsLoadingProfile] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();

  // Fetch user profile data
  const fetchProfile = async (userId: string) => {
    try {
      setIsLoadingProfile(true);
      console.log("Fetching profile for user:", userId);
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        throw error;
      }

      if (data) {
        console.log("Profile data fetched:", data);
        setProfile(data as Profile);
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      toast({
        variant: "destructive",
        title: "Error loading profile",
        description: "There was a problem loading your profile information.",
      });
    } finally {
      setIsLoadingProfile(false);
    }
  };

  // Derived state for account type and role - ALWAYS use database values from profile, not metadata
  const isSchool = !!profile && profile.account_type === 'school';
  const isSupplier = !!profile && profile.account_type === 'supplier';

  // IMPORTANT: Always use role from database (profile) and ignore metadata
  // This ensures we're using the single source of truth (the database)
  const userRole = profile?.role as string || null;

  // Role-based permissions
  const isAdmin = userRole === 'admin';
  const isTeacher = userRole === 'teacher';
  const isSupport = userRole === 'support';
  const isMaintenance = userRole === 'maintenance';

  // Site admin status - from database only
  const isSiteAdmin = !!profile && !!profile.is_site_admin;

  // Get organization ID from profile (database only)
  const organizationId = profile?.organization_id || null;

  // Debug information
  console.log('AuthContext - User metadata (NOT USED FOR ROLES):', user?.user_metadata);
  console.log('AuthContext - Database role (from profile):', userRole);
  console.log('AuthContext - Is admin (based on database role):', isAdmin);
  console.log('AuthContext - Is site admin (based on database):', isSiteAdmin);
  console.log('AuthContext - Profile data (source of truth):', profile);

  // Check if the user needs to be redirected to supplier onboarding
  useEffect(() => {
    if (user && !isLoading && !isLoadingProfile && profile) {
      // For suppliers, redirect to supplier onboarding if they haven't completed it
      if (profile.account_type === 'supplier') {
        const supplierOnboardingComplete =
          !!profile.first_name &&
          !!profile.last_name &&
          !!profile.business_name;

        if (!supplierOnboardingComplete && location.pathname !== '/supplier/onboarding') {
          console.log('Supplier has not completed onboarding, redirecting to supplier onboarding');
          navigate('/supplier/onboarding');
          return;
        }
      }
      // For schools/organizations, redirect to organization setup if they don't have an organization
      else if (!profile.organization_id && location.pathname !== '/organization/setup') {
        console.log('User has no organization, redirecting to organization setup');
        navigate('/organization/setup');
        return;
      }
    }
  }, [user, isLoading, isLoadingProfile, profile, navigate, location.pathname]);

  useEffect(() => {
    console.log("Setting up auth state listener");
    let mounted = true;
    let retryCount = 0;
    let retryTimer: NodeJS.Timeout | null = null;

    // Function to retry profile fetch with exponential backoff
    const fetchProfileWithRetry = async (userId: string, maxRetries = 3) => {
      try {
        setIsLoadingProfile(true);
        console.log(`Fetching profile for user: ${userId} (attempt ${retryCount + 1}/${maxRetries + 1})`);

        // For PWA in offline mode, try to use cached data
        if (isPWA() && !isOnline()) {
          console.log("PWA in offline mode, checking for cached profile data");
          const cachedProfile = getCachedProfileData(userId);
          if (cachedProfile) {
            console.log("Using cached profile data in offline mode:", cachedProfile);
            setProfile(cachedProfile as Profile);
            setIsLoadingProfile(false);
            return;
          } else {
            console.log("No cached profile data available in offline mode");
          }
        }

        // First, check if the user still exists in auth.users
        const { data: authUserData, error: authUserError } = await supabase.auth.getUser();

        if (authUserError) {
          console.error('Error checking auth user:', authUserError);
          // This could indicate the user's session is invalid
          handleDeletedUser();
          return;
        }

        if (!authUserData.user) {
          console.log('User no longer exists in auth system, signing out');
          handleDeletedUser();
          return;
        }

        // For PWA in online mode, check if we have cached data first
        if (isPWA() && isOnline()) {
          const cachedProfile = getCachedProfileData(userId);
          if (cachedProfile) {
            // Use cached data immediately while fetching fresh data
            console.log("Using cached profile data while fetching fresh data");
            setProfile(cachedProfile as Profile);
          }
        }

        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .single();

        if (error) {
          console.error('Error fetching profile:', error);

          // Check if this is a "not found" error, which could indicate the user was deleted
          if (error.code === 'PGRST116' || error.message.includes('no rows') || error.message.includes('406')) {
            console.log('Profile not found, user may have been deleted');

            // Verify if the user still exists in auth.users but not in profiles
            // This is a strong indicator the user was deleted
            const { data: profileData, error: countError } = await supabase
              .from('profiles')
              .select('id')
              .eq('id', userId);

            if (countError || !profileData || profileData.length === 0) {
              console.log('Confirmed user profile does not exist, signing out');
              handleDeletedUser();
              return;
            }
          }

          // If we haven't reached max retries, try again with exponential backoff
          if (retryCount < maxRetries) {
            retryCount++;
            const delay = Math.min(1000 * Math.pow(2, retryCount), 10000); // Exponential backoff with 10s max
            console.log(`Retrying profile fetch in ${delay}ms...`);

            if (retryTimer) clearTimeout(retryTimer);
            retryTimer = setTimeout(() => {
              if (mounted) fetchProfileWithRetry(userId, maxRetries);
            }, delay);
            return;
          }

          throw error;
        }

        if (data) {
          console.log("Profile data fetched successfully:", data);
          setProfile(data as Profile);

          // Cache the profile data for PWA
          if (isPWA()) {
            console.log("Caching profile data for PWA");
            storeProfileData(userId, data);
          }

          retryCount = 0; // Reset retry count on success
        } else {
          console.log("No profile data found, checking if we need to create one");

          // If no profile exists, try to create one from auth data
          if (user) {
            await createProfileFromAuth(user);
          }
        }
      } catch (error) {
        console.error('Error in fetchProfileWithRetry:', error);
        toast({
          variant: "destructive",
          title: "Error loading profile",
          description: "There was a problem loading your profile information.",
        });
      } finally {
        setIsLoadingProfile(false);
      }
    };

    // Helper function to handle deleted user scenario
    const handleDeletedUser = async () => {
      // Show a toast to inform the user
      toast({
        title: "Account not found",
        description: "Your account appears to have been deleted. You will be signed out.",
        variant: "destructive",
      });

      // Clear all auth state
      setSession(null);
      setUser(null);
      setProfile(null);

      // Clear any pending retry timers
      if (retryTimer) clearTimeout(retryTimer);

      // Sign out from Supabase to clear the session
      await supabase.auth.signOut();

      // Redirect to home page
      navigate('/', { replace: true });
    };

    // Function to create a profile from auth data if it doesn't exist
    const createProfileFromAuth = async (user: User) => {
      try {
        setIsLoadingProfile(true);
        console.log("Attempting to create profile from auth data");

        // Extract data from user metadata
        const metadata = user.user_metadata || {};
        const firstName = metadata.first_name || metadata.name?.split(' ')[0] || '';
        const lastName = metadata.last_name || metadata.name?.split(' ').slice(1).join(' ') || '';
        const accountType = metadata.account_type || 'school'; // Default to school if not specified
        const role = metadata.role || null;

        // Create a new profile
        const { data, error } = await supabase
          .from('profiles')
          .insert([{
            id: user.id,
            email: [user.email],
            first_name: firstName,
            last_name: lastName,
            account_type: accountType,
            role: role,
          }])
          .select()
          .single();

        if (error) {
          console.error('Error creating profile from auth data:', error);
          throw error;
        }

        if (data) {
          console.log("Successfully created profile from auth data:", data);
          setProfile(data as Profile);
        }
      } catch (error) {
        console.error('Error in createProfileFromAuth:', error);
      } finally {
        setIsLoadingProfile(false);
      }
    };

    // Set up the auth state listener first
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, currentSession) => {
        console.log("Auth state change event:", event);

        if (!mounted) return;

        if (event === 'SIGNED_OUT') {
          // Clear all auth state on sign out
          setSession(null);
          setUser(null);
          setProfile(null);

          // Clear any pending retry timers
          if (retryTimer) clearTimeout(retryTimer);

          // Redirect to home on sign-out, unless on a bypass route
          if (!shouldBypassAuthRedirect(location.pathname)) {
            navigate('/', { replace: true });
          }
          return;
        }

        // Update session state for all other events
        setSession(currentSession);
        setUser(currentSession?.user ?? null);

        if ((event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') && currentSession?.user) {
          console.log("User authenticated, fetching profile");
          // Reset retry count for new sign-in
          retryCount = 0;

          // Clear any existing retry timers
          if (retryTimer) clearTimeout(retryTimer);

          // Fetch profile with retry capability
          if (mounted) {
            fetchProfileWithRetry(currentSession.user.id);

            // Check for pending invitation to accept
            setTimeout(() => {
              if (!mounted) return;

              const pendingToken = localStorage.getItem('pendingInvitationToken');
              const pendingEmail = localStorage.getItem('pendingInvitationEmail');
              const isNewUserWithInvitation = localStorage.getItem('newUserWithInvitation') === 'true';

              if (pendingToken && pendingEmail && pendingEmail.toLowerCase() === currentSession.user.email?.toLowerCase()) {
                console.log('Found pending invitation to accept:', pendingToken);
                console.log('Is new user with invitation:', isNewUserWithInvitation);

                // For new users, we need to delay the invitation acceptance to ensure the user is fully registered
                const acceptPendingInvitation = () => {
                  console.log('Accepting pending invitation now...');
                  acceptInvitation(pendingToken).then(success => {
                    if (success) {
                      console.log('Successfully accepted pending invitation');
                      // Clear the pending invitation
                      localStorage.removeItem('pendingInvitationToken');
                      localStorage.removeItem('pendingInvitationEmail');
                      localStorage.removeItem('newUserWithInvitation');

                      // Refresh the page to show the updated organization
                      window.location.reload();
                    } else {
                      console.error('Failed to accept pending invitation');
                    }
                  });
                };

                if (isNewUserWithInvitation) {
                  // For new users, delay the invitation acceptance to ensure the user is fully registered
                  // Use a longer delay (5 seconds) to ensure the profile is created
                  console.log('Delaying invitation acceptance for new user...');
                  setTimeout(() => {
                    // Add retry logic for new users
                    const attemptAcceptInvitation = async (attempts = 0, maxAttempts = 3) => {
                      console.log(`Attempting to accept invitation (attempt ${attempts + 1} of ${maxAttempts})...`);

                      try {
                        // Check if profile exists before attempting to accept invitation
                        const { data: profileCheck, error: profileError } = await supabase
                          .from('profiles')
                          .select('id')
                          .eq('id', user.id)
                          .single();

                        if (profileError || !profileCheck) {
                          console.log('Profile not ready yet, waiting before retry...');
                          if (attempts < maxAttempts - 1) {
                            // Wait and try again
                            setTimeout(() => attemptAcceptInvitation(attempts + 1, maxAttempts), 2000);
                            return;
                          } else {
                            console.error('Max attempts reached, profile not ready');
                            // Try anyway as a last resort
                          }
                        } else {
                          console.log('Profile is ready, accepting invitation...');
                        }

                        // Proceed with invitation acceptance
                        acceptPendingInvitation();
                      } catch (error) {
                        console.error('Error checking profile:', error);
                        if (attempts < maxAttempts - 1) {
                          // Wait and try again
                          setTimeout(() => attemptAcceptInvitation(attempts + 1, maxAttempts), 2000);
                        } else {
                          // Last attempt
                          acceptPendingInvitation();
                        }
                      }
                    };

                    // Start the retry process
                    attemptAcceptInvitation();
                  }, 3000);
                } else {
                  // For existing users, accept the invitation immediately
                  acceptPendingInvitation();
                }
              }
            }, 0);
          }

          // Check if we need to redirect to organization setup or supplier onboarding
          const redirectToOrgSetup = localStorage.getItem('redirectToOrgSetup') === 'true';
          const redirectToSupplierOnboarding = localStorage.getItem('redirectToSupplierOnboarding') === 'true';

          if (redirectToOrgSetup) {
            console.log("Redirecting to organization setup page");
            localStorage.removeItem('redirectToOrgSetup');
            navigate('/organization/setup', { replace: true });
          } else if (redirectToSupplierOnboarding) {
            console.log("Redirecting to supplier onboarding page");
            localStorage.removeItem('redirectToSupplierOnboarding');
            navigate('/supplier/onboarding', { replace: true });
          }
          // Otherwise, only redirect to dashboard if on login or register page
          else if (location.pathname === '/login' || location.pathname === '/register') {
            console.log("Redirecting to dashboard from login/register page");
            navigate('/dashboard', { replace: true });
          } else {
            console.log("No redirection needed for path:", location.pathname);
          }
        }
      }
    );

    // Then check for existing session
    const checkSession = async () => {
      try {
        setIsLoading(true);
        setIsLoadingProfile(true);

        const { data: { session: currentSession } } = await supabase.auth.getSession();
        console.log("Checking for existing session:", currentSession ? "Found" : "None");

        if (!mounted) return;

        // If we have a session, verify the user still exists in auth
        if (currentSession?.user) {
          // Verify the user still exists
          const { data: authUserData, error: authUserError } = await supabase.auth.getUser();

          if (authUserError || !authUserData.user) {
            console.log('User session exists but user not found in auth system');
            handleDeletedUser();
            return;
          }

          // Set session and user state
          setSession(currentSession);
          setUser(currentSession.user);

          // Use the retry-capable profile fetch
          await fetchProfileWithRetry(currentSession.user.id);
        } else {
          // No session, clear state
          setSession(null);
          setUser(null);
          setProfile(null);
        }
      } catch (error) {
        console.error("Error checking session:", error);
      } finally {
        // Always set loading to false, even if there was an error
        if (mounted) {
          setIsLoading(false);
          setIsLoadingProfile(false);
        }
      }
    };

    // Start the session check
    checkSession();

    // Initialize the invitation sync service
    const cleanupSync = InvitationSyncService.init();

    return () => {
      mounted = false;

      // Clear any pending retry timers
      if (retryTimer) {
        clearTimeout(retryTimer);
      }

      // Unsubscribe from auth changes
      subscription.unsubscribe();

      // Clean up invitation sync
      cleanupSync();
    };
  }, [navigate, location.pathname]);

  // Sign up with email and password
  const signUp = async (email: string, password: string, name: string, accountType: string) => {
    try {
      setIsLoading(true);

      // First, check if there are any pending invitations for this email
      console.log('Checking for pending invitations for email:', email);
      let pendingInvitations = [];

      try {
        const { data, error: invitationsError } = await supabaseAdmin
          .from('user_invitations')
          .select('*')
          .eq('email', email)
          .eq('status', 'pending');

        if (invitationsError) {
          console.error('Error checking for pending invitations:', invitationsError);
          // Continue with signup anyway
        } else {
          pendingInvitations = data || [];
          console.log('Found pending invitations:', pendingInvitations.length || 0);
        }
      } catch (invitationCheckError) {
        console.error('Exception checking for pending invitations:', invitationCheckError);
        // Continue with signup anyway
      }

      // Determine role based on account type
      // If accountType is 'supplier', role should also be 'supplier'
      // For 'school' accounts, role will be set later based on invitation or defaults to null
      const userRole = accountType === 'supplier' ? 'supplier' : null;

      console.log(`Setting up user with account_type: ${accountType}, role: ${userRole || 'not set yet'}`);

      // Proceed with the signup
      // We still include metadata for backward compatibility, but the profiles table is the source of truth
      const { error, data } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
            account_type: accountType,
            role: userRole, // Include role in metadata for backward compatibility
            // Split name into first and last name for better display
            first_name: name.split(' ')[0],
            last_name: name.split(' ').slice(1).join(' ')
          }
        }
      });

      if (error) throw error;

      console.log("Sign up successful:", data);

      // Update user metadata to ensure display name is set correctly
      if (data.user) {
        try {
          await supabaseAdmin.auth.admin.updateUserById(data.user.id, {
            user_metadata: {
              ...data.user.user_metadata,
              full_name: name,
              name: name, // For backward compatibility
              first_name: name.split(' ')[0],
              last_name: name.split(' ').slice(1).join(' ')
            }
          });
          console.log("Updated user metadata with proper name fields");
        } catch (updateError) {
          console.error("Error updating user metadata:", updateError);
          // Continue with signup process even if this fails
        }
      }

      // If we have pending invitations and a user was created, accept them
      if (pendingInvitations && pendingInvitations.length > 0 && data.user) {
        console.log('Automatically accepting pending invitations for new user...');

        // Store the invitations in localStorage for processing after login
        localStorage.setItem('pendingInvitationsForNewUser', JSON.stringify(pendingInvitations));
        localStorage.setItem('newUserWithInvitation', 'true');

        // For each invitation, try to accept it immediately
        for (const invitation of pendingInvitations) {
          try {
            console.log('Accepting invitation with token:', invitation.token);

            // Update the invitation status directly
            const { error: updateError } = await supabaseAdmin
              .from('user_invitations')
              .update({ status: 'accepted' })
              .eq('token', invitation.token);

            if (updateError) {
              console.error('Error updating invitation status:', updateError);
            } else {
              console.log('Successfully updated invitation status to accepted');
            }

            // Update the profile with the organization ID and role
            const { error: profileUpdateError } = await supabaseAdmin
              .from('profiles')
              .update({
                organization_id: invitation.organization_id,
                role: invitation.role,
                email: [email]
              })
              .eq('id', data.user.id);

            if (profileUpdateError) {
              console.error('Error updating profile:', profileUpdateError);
            } else {
              console.log('Successfully updated profile with organization ID and role');
            }

            // Also try the SQL function as a backup
            const { error: functionError } = await supabaseAdmin.rpc('accept_invitation', {
              token_param: invitation.token,
              user_id_param: data.user.id
            });

            if (functionError) {
              console.error('Error calling accept_invitation function:', functionError);
            } else {
              console.log('Successfully called accept_invitation function');
            }
          } catch (acceptError) {
            console.error('Error accepting invitation:', acceptError);
          }
        }

        toast({
          title: "Account created successfully",
          description: "Welcome to Classtasker! Your account has been created and you've been added to the organization.",
        });
      } else {
        // For school accounts without invitations, redirect to organization setup
        if (accountType === 'school') {
          // Store a flag to redirect to organization setup after login
          localStorage.setItem('redirectToOrgSetup', 'true');
          console.log('Setting flag to redirect to organization setup after login');
        } else if (accountType === 'supplier') {
          // Store a flag to redirect to supplier onboarding after login
          localStorage.setItem('redirectToSupplierOnboarding', 'true');
          console.log('Setting flag to redirect to supplier onboarding after login');
        }

        toast({
          title: "Account created successfully",
          description: "Welcome to Classtasker! Please check your email for verification.",
        });
      }
    } catch (error: any) {
      console.error("Sign up error:", error);
      toast({
        variant: "destructive",
        title: "Sign up failed",
        description: error.message || "There was a problem with your signup.",
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Sign in with email and password
  const signIn = async (email: string, password: string) => {
    try {
      setIsLoading(true);

      console.log("Attempting sign in for:", email);
      const { error, data } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error("Sign in error:", error);
        throw error;
      }

      console.log("Sign in successful:", data.user?.id);

      toast({
        title: "Signed in successfully",
        description: "Welcome back to Classtasker!",
      });
    } catch (error: any) {
      console.error("Sign in error caught:", error);
      toast({
        variant: "destructive",
        title: "Sign in failed",
        description: error.message || "Invalid email or password.",
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Sign out - improved to ensure clean state transition
  const signOut = async () => {
    try {
      console.log("Starting sign out process");
      setIsLoading(true);

      // Clear profile cache if in PWA mode
      if (isPWA() && user) {
        console.log("Clearing profile cache for PWA");
        clearProfileCache(user.id);
      }

      // Perform the Supabase sign out first
      await supabase.auth.signOut();
      console.log("Supabase signOut completed successfully");

      // We don't need to manually update state here because the onAuthStateChange
      // listener will handle that when it receives the SIGNED_OUT event

      toast({
        title: "Signed out successfully",
      });
    } catch (error: any) {
      console.error("Sign out error:", error);

      // Even if there's an error with Supabase, clear local state
      setSession(null);
      setUser(null);
      setProfile(null);

      // Clear profile cache even on error
      if (isPWA() && user) {
        clearProfileCache(user.id);
      }

      toast({
        variant: "destructive",
        title: "Sign out issue",
        description: "There was a problem signing out, but you've been logged out of this device.",
      });

      // Force navigation to home page
      navigate('/', { replace: true });
    } finally {
      setIsLoading(false);
    }
  };

  // Update user profile
  const updateUserProfile = async (updates: { first_name?: string; last_name?: string; bio?: string; avatar_url?: string }) => {
    try {
      setIsLoading(true);

      if (!user) throw new Error("No user is logged in");

      // Always include updated_at timestamp
      const updatesWithTimestamp = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('profiles')
        .update(updatesWithTimestamp)
        .eq('id', user.id);

      if (error) throw error;

      // Update local state with new profile data
      setProfile(prev => {
        const updated = prev ? { ...prev, ...updatesWithTimestamp } : null;

        // Update cache for PWA
        if (isPWA() && updated) {
          storeProfileData(user.id, updated);
        }

        return updated;
      });

      toast({
        title: "Profile updated",
        description: "Your profile has been updated successfully.",
      });
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Update failed",
        description: error.message || "Failed to update profile.",
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Update user role with automatic account_type consistency
  const updateUserRole = async (role: string) => {
    try {
      setIsLoading(true);

      if (!user) throw new Error("No user is logged in");

      // Determine if account_type needs to be updated
      // If role is 'supplier', account_type should also be 'supplier'
      // Otherwise, account_type should be 'school'
      const accountType = role === 'supplier' ? 'supplier' : 'school';

      console.log(`Updating user role to ${role} and account_type to ${accountType}`);

      const { error } = await supabase
        .from('profiles')
        .update({
          role: role,
          account_type: accountType,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);

      if (error) throw error;

      // Update local state with new role and account_type
      setProfile(prev => prev ? { ...prev, role, account_type: accountType } : null);

      // Also update user metadata for backward compatibility
      // Note: The profiles table is the source of truth, this is just for consistency
      await supabase.auth.updateUser({
        data: {
          role: role,
          account_type: accountType
        }
      });

      toast({
        title: "Role updated",
        description: `Your role has been updated to ${role}.`,
      });
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Role update failed",
        description: error.message || "Failed to update role.",
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to process invitation data
  const processInvitation = async (invitation: any, token: string, userId: string): Promise<boolean> => {
    console.log('Processing invitation:', invitation);
    const organizationId = invitation.organization_id;
    const role = invitation.role || 'teacher';

    // Use a transaction-like approach to ensure all updates succeed or fail together
    let success = true;

    // STEP 1: Update the profile with organization ID, role, and email
    // Also ensure account_type is consistent with role
    const accountType = role === 'supplier' ? 'supplier' : 'school';
    console.log(`Updating profile with organization ID: ${organizationId}, role: ${role}, account_type: ${accountType}`);

    const { error: profileUpdateError } = await supabaseAdmin
      .from('profiles')
      .update({
        organization_id: organizationId,
        role: role,
        account_type: accountType,
        email: [user.email]
      })
      .eq('id', userId);

    if (profileUpdateError) {
      console.error('Error updating profile:', profileUpdateError);
      success = false;
    } else {
      console.log('Successfully updated profile');
    }

    // STEP 2: Update invitation status
    console.log('Updating invitation status to accepted...');
    const { error: statusUpdateError } = await supabaseAdmin
      .from('user_invitations')
      .update({ status: 'accepted' })
      .eq('token', token);

    if (statusUpdateError) {
      console.error('Error updating invitation status:', statusUpdateError);
      success = false;
    } else {
      console.log('Successfully updated invitation status to accepted');
    }

    // STEP 3: Call the SQL function as a backup
    const { error: functionError } = await supabaseAdmin.rpc('accept_invitation', {
      token_param: token,
      user_id_param: userId
    });

    if (functionError) {
      console.error('Error calling accept_invitation function:', functionError);
      // Don't fail the whole operation if just the function call fails
    } else {
      console.log('Successfully called accept_invitation function');
    }

    if (!success) {
      throw new Error('Failed to update profile or invitation status');
    }

    // STEP 4: Refresh the profile to get the latest data
    await fetchProfile(userId);

    // STEP 5: Update localStorage for UI consistency
    const acceptedInvitations = JSON.parse(localStorage.getItem('acceptedInvitations') || '[]');
    if (!acceptedInvitations.includes(token)) {
      acceptedInvitations.push(token);
      localStorage.setItem('acceptedInvitations', JSON.stringify(acceptedInvitations));
    }

    // STEP 6: Update the local profile state optimistically
    if (profile) {
      const updatedProfile = {
        ...profile,
        organization_id: organizationId,
        role: role,
        account_type: accountType,
        email: [user.email]
      };

      // Also update PWA cache if applicable
      if (isPWA()) {
        storeProfileData(userId, updatedProfile);
      }

      setProfile(updatedProfile);
      console.log('Updated profile state optimistically:', updatedProfile);
    }

    return true;
  };



  // Accept invitation and join organization with optimistic UI updates
  const acceptInvitation = async (token: string): Promise<boolean> => {
    try {
      setIsLoading(true);

      if (!user) throw new Error("No user is logged in");

      console.log('Accepting invitation with token:', token, 'for user:', user.id);

      // First, get the invitation from the database using multiple approaches
      console.log('Getting invitation from database...');

      // Try with supabaseAdmin first
      const { data: dbInvitation, error: dbError } = await supabaseAdmin
        .from('user_invitations')
        .select('*')
        .eq('token', token)
        .single();

      if (dbError) {
        console.error('Error fetching invitation with admin client:', dbError);

        // Try with regular client as fallback
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('user_invitations')
          .select('*')
          .eq('token', token)
          .single();

        if (fallbackError || !fallbackData) {
          console.error('Error fetching invitation with fallback:', fallbackError);

          // Try using the RPC function if available
          try {
            const { data: rpcData, error: rpcError } = await supabase.rpc('get_invitation_by_token', {
              token_param: token
            });

            if (rpcError || !rpcData) {
              console.error('Error fetching invitation using RPC:', rpcError);
              throw new Error('Invitation not found in database. Please check the invitation link and try again.');
            }

            // Use the RPC data
            return await processInvitation(rpcData, token, user.id);
          } catch (rpcError) {
            console.error('RPC function not available or error:', rpcError);
            throw new Error('Invitation not found in database. Please check the invitation link and try again.');
          }
        } else {
          // Use the fallback data
          return await processInvitation(fallbackData, token, user.id);
        }
      }

      console.log('Found invitation in database:', dbInvitation);
      const organizationId = dbInvitation.organization_id;
      const role = dbInvitation.role || 'teacher';

      // Use a transaction-like approach to ensure all updates succeed or fail together
      let success = true;

      // STEP 1: Update the profile with organization ID, role, and email
      // Also ensure account_type is consistent with role
      const accountType = role === 'supplier' ? 'supplier' : 'school';
      console.log(`Updating profile with organization ID: ${organizationId}, role: ${role}, account_type: ${accountType}`);

      const { error: profileUpdateError } = await supabaseAdmin
        .from('profiles')
        .update({
          organization_id: organizationId,
          role: role,
          account_type: accountType,
          email: [user.email]
        })
        .eq('id', user.id);

      if (profileUpdateError) {
        console.error('Error updating profile:', profileUpdateError);
        success = false;
      } else {
        console.log('Successfully updated profile');
      }

      // STEP 2: Update invitation status
      console.log('Updating invitation status to accepted...');
      const { error: statusUpdateError } = await supabaseAdmin
        .from('user_invitations')
        .update({ status: 'accepted' })
        .eq('token', token);

      if (statusUpdateError) {
        console.error('Error updating invitation status:', statusUpdateError);
        success = false;
      } else {
        console.log('Successfully updated invitation status to accepted');
      }

      // STEP 3: Call the SQL function as a backup
      const { error: functionError } = await supabaseAdmin.rpc('accept_invitation', {
        token_param: token,
        user_id_param: user.id
      });

      if (functionError) {
        console.error('Error calling accept_invitation function:', functionError);
        // Don't fail the whole operation if just the function call fails
      } else {
        console.log('Successfully called accept_invitation function');
      }

      if (!success) {
        throw new Error('Failed to update profile or invitation status');
      }

      // STEP 4: Refresh the profile to get the latest data
      await fetchProfile(user.id);

      // STEP 5: Update localStorage for UI consistency
      const acceptedInvitations = JSON.parse(localStorage.getItem('acceptedInvitations') || '[]');
      if (!acceptedInvitations.includes(token)) {
        acceptedInvitations.push(token);
        localStorage.setItem('acceptedInvitations', JSON.stringify(acceptedInvitations));
      }

      // STEP 6: Update the local profile state optimistically
      if (profile) {
        const updatedProfile = {
          ...profile,
          organization_id: organizationId,
          role: role,
          account_type: accountType,
          email: [user.email]
        };

        // Also update PWA cache if applicable
        if (isPWA()) {
          storeProfileData(user.id, updatedProfile);
        }

        setProfile(updatedProfile);
        console.log('Updated profile state optimistically:', updatedProfile);
      }

      // STEP 7: Clear any pending invitation data
      // We'll keep the data for now and let the confirmation page clear it
      // This ensures the confirmation page can show the success state
      // localStorage.removeItem('pendingInvitationToken');
      // localStorage.removeItem('pendingInvitationEmail');
      // localStorage.removeItem('pendingInvitationOrgName');
      // localStorage.removeItem('newUserWithInvitation');

      return true;
    } catch (error: any) {
      console.error('Error accepting invitation:', error);
      toast({
        variant: "destructive",
        title: "Failed to accept invitation",
        description: error.message || "The invitation may be invalid or expired.",
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        session,
        user,
        profile,
        isLoading,
        isLoadingProfile,
        isSchool,
        isSupplier,
        isAdmin,
        isTeacher,
        isSupport,
        isMaintenance,
        isSiteAdmin,
        organizationId,
        userRole,
        signUp,
        signIn,
        signOut,
        updateUserProfile,
        updateUserRole,
        acceptInvitation
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
