// Simple Express server for handling email testing
import express from 'express';
import nodemailer from 'nodemailer';
import cors from 'cors';
import bodyParser from 'body-parser';

const app = express();
const PORT = 3001;

// Middleware
app.use(cors({
  origin: ['http://localhost:8080', 'http://localhost:8081', 'http://localhost:8082', 'http://127.0.0.1:8082'],
  credentials: true
}));
app.use(bodyParser.json());

// Root route for health check
app.get('/', (req, res) => {
  res.json({ status: 'Email test server is running' });
});

// Email test endpoint
app.post('/api/test-email', async (req, res) => {
  console.log('Test email API route called', req.body);

  try {
    const { config, testEmail } = req.body;

    if (!config || !testEmail) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameters'
      });
    }

    // Validate email configuration
    if (config.provider === 'smtp') {
      if (!config.smtpHost || !config.smtpPort || !config.smtpUsername || !config.smtpPassword) {
        return res.status(400).json({
          success: false,
          message: 'Missing SMTP configuration. Please provide host, port, username, and password.'
        });
      }
    } else {
      return res.status(400).json({
        success: false,
        message: `Unsupported email provider: ${config.provider}`
      });
    }

    console.log('Sending test email to:', testEmail);
    console.log('With configuration:', {
      provider: config.provider,
      host: config.smtpHost,
      port: config.smtpPort,
      secure: config.smtpSecure,
      username: config.smtpUsername,
      // Password is masked for security
    });

    try {
      // Create a test transporter
      const transporter = nodemailer.createTransport({
        host: config.smtpHost,
        port: config.smtpPort,
        secure: config.smtpSecure || false,
        auth: {
          user: config.smtpUsername,
          pass: config.smtpPassword,
        },
        debug: true, // Enable debug output
      });

      // Verify connection configuration
      try {
        await transporter.verify();
        console.log('SMTP connection verified successfully');
      } catch (verifyError) {
        console.error('SMTP connection verification failed:', verifyError);
        return res.status(400).json({
          success: false,
          message: `SMTP connection failed: ${verifyError.message}`
        });
      }

      // Create email content
      const timestamp = new Date().toISOString();
      const subject = `Classtasker Email Test - ${timestamp}`;
      const body = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Classtasker Email Test</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #4f46e5; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
            .content { padding: 20px; border: 1px solid #e0e0e0; border-top: none; border-radius: 0 0 5px 5px; }
            .footer { margin-top: 20px; font-size: 12px; color: #666; text-align: center; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Classtasker Email Test</h1>
          </div>
          <div class="content">
            <p>Hello,</p>
            <p>This is a test email from Classtasker to verify your email configuration.</p>
            <p>If you're seeing this, your email configuration is working correctly!</p>
            <p><strong>Test timestamp:</strong> ${timestamp}</p>
            <p>Configuration details:</p>
            <ul>
              <li>Provider: ${config.provider}</li>
              <li>From: ${config.fromEmail}</li>
              <li>SMTP Host: ${config.smtpHost}</li>
              <li>SMTP Port: ${config.smtpPort}</li>
              <li>Secure: ${config.smtpSecure ? 'Yes' : 'No'}</li>
              <li>To: ${testEmail}</li>
            </ul>
          </div>
          <div class="footer">
            <p>This is an automated message from Classtasker. Please do not reply to this email.</p>
            <p>&copy; ${new Date().getFullYear()} Classtasker. All rights reserved.</p>
          </div>
        </body>
        </html>
      `;

      // Send test email
      const info = await transporter.sendMail({
        from: `"${config.fromName || 'Classtasker'}" <${config.fromEmail}>`,
        to: testEmail,
        subject: subject,
        text: 'This is a test email from Classtasker to verify your SMTP configuration.',
        html: body
      });

      console.log('Email sent successfully:', info);

      // Return success
      return res.status(200).json({
        success: true,
        message: `Test email sent to ${testEmail} using ${config.provider} provider. Message ID: ${info.messageId}`
      });
    } catch (emailError) {
      console.error('Error sending email:', emailError);
      return res.status(500).json({
        success: false,
        message: `Error sending email: ${emailError.message}`
      });
    }
  } catch (error) {
    console.error('Error testing email:', error);
    return res.status(500).json({
      success: false,
      message: `Server error: ${error.message}`
    });
  }
});

// Start server
const server = app.listen(PORT, () => {
  console.log(`Email test server running on port ${PORT}`);
});

// Keep the server running
process.on('SIGINT', () => {
  console.log('Shutting down server...');
  server.close(() => {
    console.log('Server shut down');
    process.exit(0);
  });
});

// Log that we're ready
console.log('Server is ready to accept connections');

// Keep the process alive
setInterval(() => {}, 1000);
