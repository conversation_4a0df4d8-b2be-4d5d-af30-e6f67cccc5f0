/**
 * <PERSON><PERSON><PERSON> to check the authentication state for the admin user
 *
 * This script will:
 * 1. Check the admin user's auth record
 * 2. Check the admin user's profile record
 * 3. Verify the admin user's role and permissions
 *
 * Run this script with Node.js:
 * node test_scripts/check_admin_auth.js
 */

import { createClient } from '@supabase/supabase-js';

// SECURITY: Supabase credentials - NEVER hardcode service role keys
const SUPABASE_URL = 'https://qcnotlojmyvpqbbgoxbc.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Validate that the service role key is present
if (!SUPABASE_SERVICE_KEY) {
  console.error('SECURITY ERROR: SUPABASE_SERVICE_ROLE_KEY not found in environment variables');
  console.error('This script requires the service role key to function');
  process.exit(1);
}

// Admin user ID
const ADMIN_ID = '4288cd97-e3ed-4e1d-8d22-abdc0d3f28bd';

// Create Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Main function to check admin auth
async function checkAdminAuth() {
  console.log('Checking Admin Authentication State');
  console.log('----------------------------------');

  try {
    // Step 1: Check the admin user's auth record
    console.log('\nStep 1: Checking admin user auth record');
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(ADMIN_ID);

    if (authError) {
      throw new Error(`Error fetching admin auth user: ${authError.message}`);
    }

    if (!authUser || !authUser.user) {
      throw new Error('Admin auth user not found');
    }

    console.log('Admin auth user found:');
    console.log(`- ID: ${authUser.user.id}`);
    console.log(`- Email: ${authUser.user.email}`);
    console.log(`- Role: ${authUser.user.role}`);
    console.log('- User Metadata:', authUser.user.user_metadata);

    // Step 2: Check the admin user's profile record
    console.log('\nStep 2: Checking admin user profile record');
    const { data: profileUser, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', ADMIN_ID)
      .single();

    if (profileError) {
      throw new Error(`Error fetching admin profile: ${profileError.message}`);
    }

    if (!profileUser) {
      throw new Error('Admin profile not found');
    }

    console.log('Admin profile found:');
    console.log(`- ID: ${profileUser.id}`);
    console.log(`- Email: ${profileUser.email}`);
    console.log(`- Role: ${profileUser.role}`);
    console.log(`- Account Type: ${profileUser.account_type}`);
    console.log(`- Organization ID: ${profileUser.organization_id}`);

    // Step 3: Verify the admin user's role and permissions
    console.log('\nStep 3: Verifying admin user role and permissions');

    const isAuthAdmin = authUser.user.user_metadata?.role === 'admin';
    const isProfileAdmin = profileUser.role === 'admin';

    console.log(`- Is admin in auth metadata: ${isAuthAdmin}`);
    console.log(`- Is admin in profile: ${isProfileAdmin}`);

    if (!isAuthAdmin) {
      console.warn('WARNING: Admin role not set in auth metadata');
    }

    if (!isProfileAdmin) {
      console.warn('WARNING: Admin role not set in profile');
    }

    // Step 4: Check if the admin user can update tasks
    console.log('\nStep 4: Testing admin task update permissions');

    // Create a test task
    const { data: testTask, error: testTaskError } = await supabase
      .from('tasks')
      .insert([
        {
          title: `Auth Test Task ${Math.floor(Math.random() * 10000)}`,
          description: 'This is a test task for admin auth testing',
          location: 'Test Location',
          category: 'Other',
          budget: 1000,
          due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'open',
          visibility: 'public',
          user_id: ADMIN_ID,
          payment_status: 'unpaid'
        }
      ])
      .select()
      .single();

    if (testTaskError) {
      throw new Error(`Error creating test task: ${testTaskError.message}`);
    }

    console.log(`Test task created: ${testTask.id}`);

    // Try to update the task as the admin user
    // First, set the auth context to the admin user
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123' // This is a test password, replace with the actual password
    });

    if (signInError) {
      console.warn(`WARNING: Could not sign in as admin: ${signInError.message}`);
      console.log('Skipping task update test with admin auth context');
    } else {
      console.log('Signed in as admin user');

      // Create a new Supabase client with the admin's session
      const adminSupabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
          detectSessionInUrl: false
        }
      });

      // Set the admin's session
      await adminSupabase.auth.setSession(signInData.session);

      // Try to update the task
      const { error: updateError } = await adminSupabase
        .from('tasks')
        .update({ status: 'assigned' })
        .eq('id', testTask.id);

      if (updateError) {
        console.warn(`WARNING: Admin could not update task: ${updateError.message}`);
      } else {
        console.log('Admin successfully updated task');
      }
    }

    // Clean up the test task
    const { error: deleteError } = await supabase
      .from('tasks')
      .delete()
      .eq('id', testTask.id);

    if (deleteError) {
      console.warn(`WARNING: Could not delete test task: ${deleteError.message}`);
    } else {
      console.log('Test task deleted');
    }

    return {
      success: true,
      isAuthAdmin,
      isProfileAdmin
    };
  } catch (error) {
    console.error('Check failed:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// Run the check
checkAdminAuth().then(result => {
  if (result.success) {
    console.log('\nAdmin auth check completed successfully!');
    if (result.isAuthAdmin && result.isProfileAdmin) {
      console.log('Admin user has correct roles in both auth and profile tables.');
    } else {
      console.log('WARNING: Admin user has inconsistent roles between auth and profile tables.');
    }
  } else {
    console.log('\nAdmin auth check failed!');
  }
});
