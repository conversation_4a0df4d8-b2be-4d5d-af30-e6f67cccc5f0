
import { useState } from "react";
import { <PERSON>v<PERSON><PERSON>, Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Menu, X, User, Bell, MessageSquare, HelpCircle, ChevronDown, LogOut, LayoutDashboard, CreditCard, Settings, Users, Shield, Map, Building, ShoppingBag, BookOpen, Wrench, Headphones, FileText } from "lucide-react";
import NotificationBadge from "@/components/ui/notification-badge";
import { useAuth } from "@/contexts/AuthContext";
import { useRolePermissions } from "@/hooks/useRolePermissions";
import { toast } from "@/hooks/use-toast";
import AdminServerStatus from "@/components/admin/AdminServerStatus";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { user, signOut, isAdmin, isTeacher, isMaintenance, isSupport, isSupplier, userRole } = useAuth();
  const { isSiteAdmin } = useRolePermissions();

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const handleSignOut = async () => {
    try {
      // Close mobile menu if open
      if (isOpen) {
        setIsOpen(false);
      }

      // Sign out - let the Auth context handle redirects
      await signOut();
    } catch (error) {
      console.error("Error handling sign out:", error);
      toast({
        variant: "destructive",
        title: "Sign out failed",
        description: "There was a problem signing out. Please try again."
      });
    }
  };

  return (
    <nav className="bg-white shadow-sm py-4 sticky top-0 z-50">
      <div className="container mx-auto px-4 flex justify-between items-center">
        <Link to="/" className="flex items-center space-x-2">
          <div className="w-9 h-9 rounded-full bg-classtasker-blue text-white flex items-center justify-center font-bold text-xl">C</div>
          <span className="text-xl font-bold text-classtasker-dark">Classtasker</span>
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center space-x-8">
          <NavLink
            to="/tasks"
            className={({ isActive }) =>
              isActive ? "text-classtasker-blue font-medium" : "text-gray-600 hover:text-classtasker-blue transition-colors"
            }
          >
            Find Available Tasks
          </NavLink>
          <NavLink
            to="/post-task"
            className={({ isActive }) =>
              isActive ? "text-classtasker-blue font-medium" : "text-gray-600 hover:text-classtasker-blue transition-colors"
            }
          >
            Post a Task
          </NavLink>
          <NavLink
            to="/how-it-works"
            className={({ isActive }) =>
              isActive ? "text-classtasker-blue font-medium" : "text-gray-600 hover:text-classtasker-blue transition-colors"
            }
          >
            How It Works
          </NavLink>
          <NavLink
            to="/plans"
            className={({ isActive }) =>
              isActive ? "text-classtasker-blue font-medium" : "text-gray-600 hover:text-classtasker-blue transition-colors"
            }
          >
            Plans
          </NavLink>
        </div>

        {/* Authentication Section */}
        <div className="hidden md:flex items-center space-x-4">
          {user ? (
            <>
              <Button variant="ghost" size="icon" asChild>
                <Link to="/dashboard?tab=messages">
                  <MessageSquare className="h-5 w-5 text-gray-600" />
                  <span className="sr-only">Messages</span>
                </Link>
              </Button>
              <NotificationBadge variant="button" />

              {/* User Profile Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center gap-2 hover:bg-gray-100">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="bg-classtasker-blue text-white">
                        {user.email?.charAt(0).toUpperCase() || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>My Account</DropdownMenuLabel>
                  <DropdownMenuSeparator />

                  {/* Common links for all users */}
                  <DropdownMenuItem asChild>
                    <Link to={`/profile/${user.id}`} className="flex items-center cursor-pointer">
                      <User className="mr-2 h-4 w-4" />
                      <span>Profile</span>
                    </Link>
                  </DropdownMenuItem>

                  <DropdownMenuItem asChild>
                    <Link to="/dashboard" className="flex items-center cursor-pointer">
                      <LayoutDashboard className="mr-2 h-4 w-4" />
                      <span>Dashboard</span>
                    </Link>
                  </DropdownMenuItem>

                  {/* Only show Payments link for site admins and organization admins */}
                  {(isSiteAdmin || isAdmin) && (
                    <DropdownMenuItem asChild>
                      <Link to="/payments" className="flex items-center cursor-pointer">
                        <CreditCard className="mr-2 h-4 w-4" />
                        <span>Payments</span>
                      </Link>
                    </DropdownMenuItem>
                  )}

                  {/* Supplier-specific links */}
                  {isSupplier && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuLabel className="flex items-center">
                        <ShoppingBag className="mr-2 h-4 w-4 text-indigo-600" />
                        <span>Supplier</span>
                      </DropdownMenuLabel>

                      <DropdownMenuItem asChild>
                        <Link to="/tasks/public" className="flex items-center cursor-pointer">
                          <FileText className="mr-2 h-4 w-4" />
                          <span>Available Tasks</span>
                        </Link>
                      </DropdownMenuItem>

                      <DropdownMenuItem asChild>
                        <Link to="/stripe-connect" className="flex items-center cursor-pointer">
                          <CreditCard className="mr-2 h-4 w-4" />
                          <span>Stripe Connect</span>
                        </Link>
                      </DropdownMenuItem>
                    </>
                  )}

                  {/* Teacher-specific links */}
                  {isTeacher && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuLabel className="flex items-center">
                        <BookOpen className="mr-2 h-4 w-4 text-green-600" />
                        <span>Teacher</span>
                      </DropdownMenuLabel>

                      <DropdownMenuItem asChild>
                        <Link to="/tasks/create" className="flex items-center cursor-pointer">
                          <FileText className="mr-2 h-4 w-4" />
                          <span>Create Task</span>
                        </Link>
                      </DropdownMenuItem>

                      <DropdownMenuItem asChild>
                        <Link to="/tasks/my-tasks" className="flex items-center cursor-pointer">
                          <FileText className="mr-2 h-4 w-4" />
                          <span>My Tasks</span>
                        </Link>
                      </DropdownMenuItem>
                    </>
                  )}

                  {/* Maintenance Staff links */}
                  {isMaintenance && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuLabel className="flex items-center">
                        <Wrench className="mr-2 h-4 w-4 text-blue-600" />
                        <span>Maintenance</span>
                      </DropdownMenuLabel>

                      <DropdownMenuItem asChild>
                        <Link to="/tasks/assigned" className="flex items-center cursor-pointer">
                          <FileText className="mr-2 h-4 w-4" />
                          <span>Assigned Tasks</span>
                        </Link>
                      </DropdownMenuItem>
                    </>
                  )}

                  {/* Support Staff links */}
                  {isSupport && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuLabel className="flex items-center">
                        <Headphones className="mr-2 h-4 w-4 text-purple-600" />
                        <span>Support</span>
                      </DropdownMenuLabel>

                      <DropdownMenuItem asChild>
                        <Link to="/tasks/assigned" className="flex items-center cursor-pointer">
                          <FileText className="mr-2 h-4 w-4" />
                          <span>Assigned Tasks</span>
                        </Link>
                      </DropdownMenuItem>
                    </>
                  )}

                  {/* Site Admin Section */}
                  {isSiteAdmin && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuLabel className="flex items-center">
                        <Shield className="mr-2 h-4 w-4 text-red-500" />
                        <span>Site Admin</span>
                      </DropdownMenuLabel>

                      <DropdownMenuItem asChild>
                        <Link to="/admin/site" className="flex items-center cursor-pointer">
                          <Settings className="mr-2 h-4 w-4" />
                          <span>Site Settings</span>
                        </Link>
                      </DropdownMenuItem>

                      <DropdownMenuItem asChild>
                        <Link to="/admin/roles" className="flex items-center cursor-pointer">
                          <Users className="mr-2 h-4 w-4" />
                          <span>Role Management</span>
                        </Link>
                      </DropdownMenuItem>
                    </>
                  )}

                  {/* Regular Admin Section */}
                  {isAdmin && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuLabel className="flex items-center">
                        <Shield className="mr-2 h-4 w-4 text-amber-500" />
                        <span>Admin</span>
                      </DropdownMenuLabel>

                      {/* Organization Management Section */}

                      <DropdownMenuItem asChild>
                        <Link to="/organization/dashboard" className="flex items-center cursor-pointer">
                          <Building className="mr-2 h-4 w-4" />
                          <span>Organisation Dashboard</span>
                        </Link>
                      </DropdownMenuItem>

                      {/* Only show Members link for site admins */}
                      {isSiteAdmin && (
                        <DropdownMenuItem asChild>
                          <Link to="/organization/members" className="flex items-center cursor-pointer">
                            <Users className="mr-2 h-4 w-4" />
                            <span>Members</span>
                          </Link>
                        </DropdownMenuItem>
                      )}

                      <DropdownMenuItem asChild>
                        <Link to="/admin/tasks" className="flex items-center cursor-pointer">
                          <FileText className="mr-2 h-4 w-4" />
                          <span>Task Management</span>
                        </Link>
                      </DropdownMenuItem>

                      <DropdownMenuItem asChild>
                        <Link to="/organization/dashboard?tab=settings" className="flex items-center cursor-pointer">
                          <Settings className="mr-2 h-4 w-4" />
                          <span>Settings</span>
                        </Link>
                      </DropdownMenuItem>

                      <DropdownMenuItem asChild>
                        <Link to="/organization/invoices" className="flex items-center cursor-pointer">
                          <CreditCard className="mr-2 h-4 w-4" />
                          <span>Invoices</span>
                        </Link>
                      </DropdownMenuItem>

                      {/* Only show System Management Section for site admins */}
                      {isSiteAdmin && (
                        <>
                          <DropdownMenuLabel className="pl-6 text-xs text-gray-500 font-normal mt-2 flex items-center justify-between">
                            <span>System</span>
                            <AdminServerStatus />
                          </DropdownMenuLabel>

                          <DropdownMenuItem asChild>
                            <Link to="/admin" className="flex items-center cursor-pointer">
                              <Shield className="mr-2 h-4 w-4" />
                              <span>Admin Dashboard</span>
                            </Link>
                          </DropdownMenuItem>

                          <DropdownMenuItem asChild>
                            <Link to="/admin/users" className="flex items-center cursor-pointer">
                              <Users className="mr-2 h-4 w-4" />
                              <span>User Management</span>
                            </Link>
                          </DropdownMenuItem>

                          <DropdownMenuItem asChild>
                            <Link to="/admin/email" className="flex items-center cursor-pointer">
                              <Settings className="mr-2 h-4 w-4" />
                              <span>Email Configuration</span>
                            </Link>
                          </DropdownMenuItem>

                          <DropdownMenuItem asChild>
                            <Link to="/route-explorer" className="flex items-center cursor-pointer">
                              <Map className="mr-2 h-4 w-4" />
                              <span>Route Explorer</span>
                            </Link>
                          </DropdownMenuItem>
                        </>
                      )}
                    </>
                  )}

                  {/* Help & Support link for all users */}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link to="/help" className="flex items-center cursor-pointer">
                      <HelpCircle className="mr-2 h-4 w-4" />
                      <span>Help & Support</span>
                    </Link>
                  </DropdownMenuItem>

                  <DropdownMenuSeparator />

                  <DropdownMenuItem onClick={handleSignOut} className="flex items-center cursor-pointer text-red-600 focus:text-red-600">
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Sign Out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          ) : (
            <>
              <Button variant="outline" asChild>
                <Link to="/login">Log In</Link>
              </Button>
              <Button className="bg-classtasker-blue hover:bg-blue-600" asChild>
                <Link to="/register">Sign Up</Link>
              </Button>
            </>
          )}
        </div>

        {/* Mobile Navigation and Notifications */}
        <div className="md:hidden flex items-center space-x-2">
          {user && (
            <NotificationBadge variant="icon" />
          )}
          <Button variant="ghost" size="icon" onClick={toggleMenu}>
            {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </Button>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isOpen && (
        <div className="md:hidden bg-white py-4 px-4 shadow-md absolute top-16 left-0 right-0 z-50">
          <div className="flex flex-col space-y-4">
            <NavLink
              to="/tasks"
              className={({ isActive }) =>
                isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
              }
              onClick={toggleMenu}
            >
              Find Available Tasks
            </NavLink>
            <NavLink
              to="/post-task"
              className={({ isActive }) =>
                isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
              }
              onClick={toggleMenu}
            >
              Post a Task
            </NavLink>
            <NavLink
              to="/how-it-works"
              className={({ isActive }) =>
                isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
              }
              onClick={toggleMenu}
            >
              How It Works
            </NavLink>
            <NavLink
              to="/plans"
              className={({ isActive }) =>
                isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
              }
              onClick={toggleMenu}
            >
              Plans
            </NavLink>
            <NavLink
              to="/help"
              className={({ isActive }) =>
                isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
              }
              onClick={toggleMenu}
            >
              Help
            </NavLink>
            <hr className="my-2" />
            {user ? (
              <>
                {/* Common links for all users */}
                <NavLink
                  to={`/profile/${user.id}`}
                  className={({ isActive }) =>
                    isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                  }
                  onClick={toggleMenu}
                >
                  Profile
                </NavLink>
                <NavLink
                  to="/dashboard"
                  className={({ isActive }) =>
                    isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                  }
                  onClick={toggleMenu}
                >
                  Dashboard
                </NavLink>
                <NavLink
                  to="/dashboard?tab=messages"
                  className={({ isActive }) =>
                    isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                  }
                  onClick={toggleMenu}
                >
                  Messages
                </NavLink>
                <NavLink
                  to="/notifications"
                  className={({ isActive }) =>
                    isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                  }
                  onClick={toggleMenu}
                >
                  Notifications
                </NavLink>

                {/* Only show Payments link for site admins and organization admins */}
                {(isSiteAdmin || isAdmin) && (
                  <NavLink
                    to="/payments"
                    className={({ isActive }) =>
                      isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                    }
                    onClick={toggleMenu}
                  >
                    Payments
                  </NavLink>
                )}

                {/* Supplier-specific links */}
                {isSupplier && (
                  <>
                    <div className="mt-2 mb-1 px-1">
                      <div className="flex items-center text-xs font-medium text-indigo-600">
                        <ShoppingBag className="h-3 w-3 mr-1" />
                        SUPPLIER
                      </div>
                    </div>

                    <NavLink
                      to="/tasks/public"
                      className={({ isActive }) =>
                        isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                      }
                      onClick={toggleMenu}
                    >
                      Available Tasks
                    </NavLink>

                    <NavLink
                      to="/stripe-connect"
                      className={({ isActive }) =>
                        isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                      }
                      onClick={toggleMenu}
                    >
                      Stripe Connect
                    </NavLink>
                  </>
                )}

                {/* Teacher-specific links */}
                {isTeacher && (
                  <>
                    <div className="mt-2 mb-1 px-1">
                      <div className="flex items-center text-xs font-medium text-green-600">
                        <BookOpen className="h-3 w-3 mr-1" />
                        TEACHER
                      </div>
                    </div>

                    <NavLink
                      to="/tasks/create"
                      className={({ isActive }) =>
                        isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                      }
                      onClick={toggleMenu}
                    >
                      Create Task
                    </NavLink>

                    <NavLink
                      to="/tasks/my-tasks"
                      className={({ isActive }) =>
                        isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                      }
                      onClick={toggleMenu}
                    >
                      My Tasks
                    </NavLink>
                  </>
                )}

                {/* Maintenance Staff links */}
                {isMaintenance && (
                  <>
                    <div className="mt-2 mb-1 px-1">
                      <div className="flex items-center text-xs font-medium text-blue-600">
                        <Wrench className="h-3 w-3 mr-1" />
                        MAINTENANCE
                      </div>
                    </div>

                    <NavLink
                      to="/tasks/assigned"
                      className={({ isActive }) =>
                        isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                      }
                      onClick={toggleMenu}
                    >
                      Assigned Tasks
                    </NavLink>
                  </>
                )}

                {/* Support Staff links */}
                {isSupport && (
                  <>
                    <div className="mt-2 mb-1 px-1">
                      <div className="flex items-center text-xs font-medium text-purple-600">
                        <Headphones className="h-3 w-3 mr-1" />
                        SUPPORT
                      </div>
                    </div>

                    <NavLink
                      to="/tasks/assigned"
                      className={({ isActive }) =>
                        isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                      }
                      onClick={toggleMenu}
                    >
                      Assigned Tasks
                    </NavLink>
                  </>
                )}

                {/* Site Admin Section */}
                {isSiteAdmin && (
                  <>
                    <div className="mt-2 mb-1 px-1">
                      <div className="flex items-center text-xs font-medium text-red-600">
                        <Shield className="h-3 w-3 mr-1" />
                        SITE ADMIN
                      </div>
                    </div>

                    <NavLink
                      to="/admin/site"
                      className={({ isActive }) =>
                        isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                      }
                      onClick={toggleMenu}
                    >
                      Site Settings
                    </NavLink>

                    <NavLink
                      to="/admin/roles"
                      className={({ isActive }) =>
                        isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                      }
                      onClick={toggleMenu}
                    >
                      Role Management
                    </NavLink>
                  </>
                )}

                {/* Regular Admin Section */}
                {isAdmin && (
                  <>
                    <div className="mt-2 mb-1 px-1">
                      <div className="flex items-center text-xs font-medium text-amber-600">
                        <Shield className="h-3 w-3 mr-1" />
                        ADMIN
                      </div>
                    </div>

                    {/* Organization Management Section */}

                    <NavLink
                      to="/organization/dashboard"
                      className={({ isActive }) =>
                        isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                      }
                      onClick={toggleMenu}
                    >
                      Organisation Dashboard
                    </NavLink>

                    {/* Only show Members link for site admins */}
                    {isSiteAdmin && (
                      <NavLink
                        to="/organization/members"
                        className={({ isActive }) =>
                          isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                        }
                        onClick={toggleMenu}
                      >
                        Members
                      </NavLink>
                    )}

                    <NavLink
                      to="/admin/tasks"
                      className={({ isActive }) =>
                        isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                      }
                      onClick={toggleMenu}
                    >
                      Task Management
                    </NavLink>

                    <NavLink
                      to="/organization/dashboard?tab=settings"
                      className={({ isActive }) =>
                        isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                      }
                      onClick={toggleMenu}
                    >
                      Settings
                    </NavLink>

                    <NavLink
                      to="/organization/invoices"
                      className={({ isActive }) =>
                        isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                      }
                      onClick={toggleMenu}
                    >
                      Invoices
                    </NavLink>

                    {/* System Management Section - Only for site admins */}
                    {isSiteAdmin && (
                      <>
                        <div className="mt-2 mb-1 px-1 flex items-center justify-between">
                          <div className="text-xs text-gray-500">System</div>
                          <AdminServerStatus />
                        </div>

                        <NavLink
                          to="/admin"
                          className={({ isActive }) =>
                            isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                          }
                          onClick={toggleMenu}
                        >
                          Admin Dashboard
                        </NavLink>

                        <NavLink
                          to="/admin/users"
                          className={({ isActive }) =>
                            isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                          }
                          onClick={toggleMenu}
                        >
                          User Management
                        </NavLink>

                        <NavLink
                          to="/admin/email"
                          className={({ isActive }) =>
                            isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                          }
                          onClick={toggleMenu}
                        >
                          Email Configuration
                        </NavLink>

                        <NavLink
                          to="/route-explorer"
                          className={({ isActive }) =>
                            isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                          }
                          onClick={toggleMenu}
                        >
                          Route Explorer
                        </NavLink>
                      </>
                    )}
                  </>
                )}
                <NavLink
                  to="/help"
                  className={({ isActive }) =>
                    isActive ? "text-classtasker-blue font-medium" : "text-gray-600"
                  }
                  onClick={toggleMenu}
                >
                  Help & Support
                </NavLink>
                <Button variant="outline" onClick={handleSignOut}>
                  Sign Out
                </Button>
              </>
            ) : (
              <div className="flex flex-col space-y-2">
                <Button variant="outline" asChild>
                  <Link to="/login" onClick={toggleMenu}>Log In</Link>
                </Button>
                <Button className="bg-classtasker-blue hover:bg-blue-600" asChild>
                  <Link to="/register" onClick={toggleMenu}>Sign Up</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
