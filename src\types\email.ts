export type EmailProvider = 'smtp' | 'none';

export interface SchoolEmailConfig {
  schoolId: string;
  schoolName: string;
  provider: EmailProvider;
  apiKey?: string;
  fromEmail?: string;
  fromName?: string;



  // SMTP specific
  smtpHost?: string;
  smtpPort?: number;
  smtpUsername?: string;
  smtpPassword?: string;
  smtpSecure?: boolean;
}

export interface EmailConfig {
  // Global configuration (used as default)
  provider: EmailProvider;
  apiKey?: string;
  fromEmail?: string;
  fromName?: string;



  // SMTP specific
  smtpHost?: string;
  smtpPort?: number;
  smtpUsername?: string;
  smtpPassword?: string;
  smtpSecure?: boolean;

  // School-specific configurations
  schoolConfigs?: SchoolEmailConfig[];
}

export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  variables: string[];
}

export interface SendEmailParams {
  to: string;
  subject: string;
  body: string;
  templateId?: string;
  variables?: Record<string, string>;
  schoolId?: string; // Optional school ID to use school-specific config
}

export interface EmailServiceStatus {
  configured: boolean;
  provider: EmailProvider;
  lastChecked?: string;
  status: 'active' | 'inactive' | 'error';
  error?: string;
}
