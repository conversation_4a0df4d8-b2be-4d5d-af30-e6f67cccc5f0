// Script to fix the PaymentElement mounting issue in EnhancedPaymentProcessor.tsx
const fs = require('fs');
const path = require('path');

// Paths
const processorPath = path.join(__dirname, 'src', 'components', 'stripe', 'EnhancedPaymentProcessor.tsx');
const backupPath = path.join(__dirname, 'src', 'components', 'stripe', 'EnhancedPaymentProcessor.tsx.element-fix');

// Create a backup of the original file
console.log('Creating backup of EnhancedPaymentProcessor.tsx...');
fs.copyFileSync(processorPath, backupPath);
console.log(`Backup created at ${backupPath}`);

// Read the file content
console.log('Reading EnhancedPaymentProcessor.tsx...');
let content = fs.readFileSync(processorPath, 'utf8');

// Fix 1: Add a useEffect to ensure elements are mounted before submission
console.log('Adding element mounting check...');
const handleSubmitFunctionStart = 'const handleSubmit = async (event: React.FormEvent) => {';
const elementCheckCode = `
  // Add a state to track if elements are mounted
  const [elementsReady, setElementsReady] = useState(false);

  // Check if elements are mounted
  useEffect(() => {
    if (elements) {
      setElementsReady(true);
    }
  }, [elements]);

`;

// Insert the element check code before the handleSubmit function
if (content.includes(handleSubmitFunctionStart)) {
  content = content.replace(
    handleSubmitFunctionStart,
    elementCheckCode + handleSubmitFunctionStart
  );
}

// Fix 2: Update the submit button to be disabled if elements aren't ready
console.log('Updating submit button disabled state...');
const submitButtonDisabled = 'disabled={!stripe || loading}';
const updatedDisabled = 'disabled={!stripe || !elementsReady || loading}';

if (content.includes(submitButtonDisabled)) {
  content = content.replace(submitButtonDisabled, updatedDisabled);
}

// Fix 3: Add a check in the handleSubmit function to ensure elements are ready
console.log('Adding elements ready check in handleSubmit...');
const elementsCheck = 'if (!stripe || !elements) {';
const updatedElementsCheck = 'if (!stripe || !elements || !elementsReady) {';

if (content.includes(elementsCheck)) {
  content = content.replace(elementsCheck, updatedElementsCheck);
}

// Write the modified content back to the file
fs.writeFileSync(processorPath, content, 'utf8');
console.log('Fix applied successfully!');

console.log('\nChanges made:');
console.log('1. Added state to track if Stripe Elements are properly mounted');
console.log('2. Added useEffect to check when elements are ready');
console.log('3. Updated submit button to be disabled until elements are ready');
console.log('4. Added additional check in handleSubmit function');

console.log('\nPlease refresh the page and try the payment again.');
console.log('\nIf you need to restore the original file, run:');
console.log(`cp ${backupPath} ${processorPath}`);