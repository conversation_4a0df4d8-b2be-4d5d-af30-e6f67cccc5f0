import { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, UserPlus } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

const CreateTestUsers = () => {
  const [isCreating, setIsCreating] = useState(false);
  const [results, setResults] = useState<string[]>([]);
  const { toast } = useToast();

  const createTestUsers = async () => {
    setIsCreating(true);
    setResults([]);

    try {
      // Add log message
      addResult('Starting test user creation...');

      // Create a school user
      addResult('Creating school test user...');
      const schoolEmail = `school_${Date.now()}@test.com`;
      const schoolPassword = 'password123';

      const { data: schoolData, error: schoolError } = await supabase.auth.signUp({
        email: schoolEmail,
        password: schoolPassword,
        options: {
          data: {
            name: 'Test School User',
            account_type: 'school',
          }
        }
      });

      if (schoolError) {
        addResult(`❌ Error creating school user: ${schoolError.message}`);
      } else {
        addResult(`✅ School user created: ${schoolEmail}`);
        addResult(`School user ID: ${schoolData.user?.id}`);

        // Update profile
        if (schoolData.user?.id) {
          const { error: profileError } = await supabase
            .from('profiles')
            .update({
              first_name: 'Test',
              last_name: 'School',
              account_type: 'school'
            })
            .eq('id', schoolData.user.id);

          if (profileError) {
            addResult(`❌ Error updating school profile: ${profileError.message}`);
          } else {
            addResult('✅ School profile updated');
          }
        }
      }

      // Create a supplier user
      addResult('Creating supplier test user...');
      const supplierEmail = `supplier_${Date.now()}@test.com`;
      const supplierPassword = 'password123';

      const { data: supplierData, error: supplierError } = await supabase.auth.signUp({
        email: supplierEmail,
        password: supplierPassword,
        options: {
          data: {
            name: 'Test Supplier User',
            account_type: 'supplier',
          }
        }
      });

      if (supplierError) {
        addResult(`❌ Error creating supplier user: ${supplierError.message}`);
      } else {
        addResult(`✅ Supplier user created: ${supplierEmail}`);
        addResult(`Supplier user ID: ${supplierData.user?.id}`);

        // Update profile
        if (supplierData.user?.id) {
          const { error: profileError } = await supabase
            .from('profiles')
            .update({
              first_name: 'Test',
              last_name: 'Supplier',
              account_type: 'supplier'
            })
            .eq('id', supplierData.user.id);

          if (profileError) {
            addResult(`❌ Error updating supplier profile: ${profileError.message}`);
          } else {
            addResult('✅ Supplier profile updated');
          }
        }
      }

      // Create a test task using RPC to bypass RLS
      addResult('Creating test task...');

      if (schoolData.user?.id) {
        try {
          // First try using RPC function if it exists
          addResult('Attempting to create task via RPC function...');
          const { data: rpcData, error: rpcError } = await supabase
            .rpc('create_debug_task', {
              title_param: 'Test Task for Debug',
              description_param: 'This is a test task created for debugging purposes',
              user_id_param: schoolData.user.id,
              category_param: 'Other',
              location_param: 'Test Location',
              budget_param: 100,
              due_date_param: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days from now
            });

          if (rpcError) {
            addResult(`RPC method failed: ${rpcError.message}`);
            addResult('Falling back to direct insert...');

            // Fallback to direct insert
            const { data: taskData, error: taskError } = await supabase
              .from('tasks')
              .insert({
                title: 'Test Task for Debug',
                description: 'This is a test task created for debugging purposes',
                user_id: schoolData.user.id,
                status: 'open',
                category: 'Other',
                location: 'Test Location',
                budget: 100,
                due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days from now
              })
              .select();

            if (taskError) {
              addResult(`❌ Error creating test task: ${taskError.message}`);
              addResult('Note: You may need to create a database function to bypass RLS for task creation.');
              addResult('SQL for function creation:');
              addResult(`
-- Create a function to bypass RLS for debug task creation
CREATE OR REPLACE FUNCTION create_debug_task(
  title_param TEXT,
  description_param TEXT,
  user_id_param UUID,
  category_param TEXT,
  location_param TEXT,
  budget_param NUMERIC,
  due_date_param TIMESTAMPTZ
) RETURNS UUID
SECURITY DEFINER
AS $$
DECLARE
  task_id UUID;
BEGIN
  INSERT INTO tasks (
    title, description, user_id, status, category, location, budget, due_date
  ) VALUES (
    title_param, description_param, user_id_param, 'open', category_param, location_param, budget_param, due_date_param
  )
  RETURNING id INTO task_id;

  RETURN task_id;
END;
$$ LANGUAGE plpgsql;
              `);
            } else if (taskData && taskData.length > 0) {
              addResult(`✅ Test task created with ID: ${taskData[0].id}`);
            }
          } else {
            addResult(`✅ Test task created via RPC with ID: ${rpcData}`);
          }
        } catch (err: any) {
          addResult(`❌ Unexpected error creating task: ${err.message}`);
        }
      }

      addResult('Test user creation completed!');

      toast({
        title: 'Test Users Created',
        description: 'Test school and supplier users have been created successfully.',
      });
    } catch (error: any) {
      console.error('Error creating test users:', error);
      addResult(`❌ Unexpected error: ${error.message}`);

      toast({
        variant: 'destructive',
        title: 'Error Creating Test Users',
        description: error.message || 'An unexpected error occurred.',
      });
    } finally {
      setIsCreating(false);
    }
  };

  const addResult = (message: string) => {
    setResults(prev => [...prev, message]);
    console.log(message);
  };

  return (
    <MainLayout>
      <div className="container mx-auto py-6 px-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <UserPlus className="mr-2 h-5 w-5" />
              Create Test Users
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-gray-600">
              This utility creates test school and supplier users for debugging purposes.
              It will also create a test task assigned to the school user.
            </p>

            <Button
              onClick={createTestUsers}
              disabled={isCreating}
              className="mb-6"
            >
              {isCreating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Test Users...
                </>
              ) : (
                <>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Create Test Users
                </>
              )}
            </Button>

            {results.length > 0 && (
              <div className="mt-4 border rounded-md p-4 bg-gray-50">
                <h3 className="font-medium mb-2">Results:</h3>
                <div className="space-y-1 font-mono text-sm">
                  {results.map((result, index) => (
                    <div key={index} className="whitespace-pre-wrap">{result}</div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default CreateTestUsers;
