import { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

const DebugMessages = () => {
  const [messages, setMessages] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  const fetchMessages = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      console.log('Fetching all task messages...');
      
      // First try to get all messages
      const { data: allMessages, error: messagesError } = await supabase
        .from('task_messages')
        .select('*')
        .limit(100);
        
      if (messagesError) {
        console.error('Error fetching messages:', messagesError);
        setError(`Error fetching messages: ${messagesError.message}`);
        setMessages([]);
      } else {
        console.log(`Found ${allMessages?.length || 0} messages`);
        setMessages(allMessages || []);
      }
    } catch (err) {
      console.error('Unexpected error:', err);
      setError('An unexpected error occurred');
      setMessages([]);
    } finally {
      setIsLoading(false);
    }
  };
  
  const createTestMessage = async () => {
    if (!user) {
      setError('You must be logged in to create a test message');
      return;
    }
    
    try {
      // First get a task to attach the message to
      const { data: tasks, error: tasksError } = await supabase
        .from('tasks')
        .select('id')
        .limit(1);
        
      if (tasksError || !tasks || tasks.length === 0) {
        setError('No tasks found to attach message to');
        return;
      }
      
      const taskId = tasks[0].id;
      
      // Create a test message
      const { data, error } = await supabase
        .from('task_messages')
        .insert({
          task_id: taskId,
          sender_id: user.id,
          content: 'This is a test message created at ' + new Date().toISOString()
        });
        
      if (error) {
        console.error('Error creating test message:', error);
        setError(`Error creating test message: ${error.message}`);
      } else {
        console.log('Test message created successfully');
        fetchMessages();
      }
    } catch (err) {
      console.error('Unexpected error creating test message:', err);
      setError('An unexpected error occurred while creating test message');
    }
  };
  
  useEffect(() => {
    fetchMessages();
  }, []);
  
  return (
    <MainLayout>
      <div className="container mx-auto py-6 px-4">
        <h1 className="text-2xl font-bold mb-6">Debug Messages</h1>
        
        <div className="mb-4 flex gap-4">
          <Button onClick={fetchMessages} disabled={isLoading}>
            {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
            Refresh Messages
          </Button>
          
          <Button onClick={createTestMessage} disabled={isLoading || !user}>
            Create Test Message
          </Button>
        </div>
        
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}
        
        <div className="bg-white rounded-lg border p-4">
          <h2 className="text-lg font-semibold mb-2">Messages in Database ({messages.length})</h2>
          
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
            </div>
          ) : messages.length === 0 ? (
            <p className="text-gray-500 py-4">No messages found in the database.</p>
          ) : (
            <div className="space-y-4">
              {messages.map((message) => (
                <div key={message.id} className="border p-3 rounded">
                  <div className="flex justify-between">
                    <span className="font-medium">ID: {message.id}</span>
                    <span className="text-sm text-gray-500">{new Date(message.created_at).toLocaleString()}</span>
                  </div>
                  <div className="mt-1">
                    <span className="text-sm text-gray-700">Task ID: {message.task_id}</span>
                  </div>
                  <div className="mt-1">
                    <span className="text-sm text-gray-700">Sender ID: {message.sender_id}</span>
                  </div>
                  <p className="mt-2">{message.content}</p>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
};

export default DebugMessages;
