// <PERSON><PERSON><PERSON> to force a browser refresh by adding a version parameter to the JavaScript files
const fs = require('fs');
const path = require('path');

// Path to the index.html file
const indexPath = path.join(__dirname, 'index.html');

// Check if the file exists
if (!fs.existsSync(indexPath)) {
  console.error(`Error: index.html not found at ${indexPath}`);
  process.exit(1);
}

// Read the file content
console.log('Reading index.html...');
let content = fs.readFileSync(indexPath, 'utf8');

// Add a version parameter to the JavaScript files
const timestamp = Date.now();
console.log(`Adding version parameter: ?v=${timestamp}`);

// Replace script tags with versioned ones
content = content.replace(
  /(<script\s+[^>]*src=["'])([^"']+)(["'][^>]*>)/g,
  `$1$2?v=${timestamp}$3`
);

// Write the modified content back to the file
fs.writeFileSync(indexPath, content, 'utf8');
console.log('Version parameter added successfully!');
console.log('Please refresh the page and try again.');