/**
 * Email tester service for testing SMTP connections
 */
import { supabase } from '@/integrations/supabase/client';

/**
 * Test SMTP connection and send a test email
 * @param {Object} config - Email configuration
 * @param {string} testEmail - Email address to send test to
 * @returns {Promise<{success: boolean, message: string}>}
 */
export async function testSmtpConnection(config, testEmail) {
  try {
    // Validate required fields
    if (!config.smtpHost || !config.smtpPort || !config.smtpUsername || !config.smtpPassword) {
      return {
        success: false,
        message: 'Missing SMTP configuration. Please provide host, port, username, and password.'
      };
    }

    if (!config.fromEmail) {
      return {
        success: false,
        message: 'Missing sender email address. Please provide a from email.'
      };
    }

    if (!testEmail) {
      return {
        success: false,
        message: 'Missing test email address. Please provide an email address to send the test to.'
      };
    }

    console.log('Testing SMTP connection with:', {
      host: config.smtpHost,
      port: config.smtpPort,
      secure: config.smtpSecure,
      auth: {
        user: config.smtpUsername,
        pass: '********' // Masked for security
      }
    });

    // Call Supabase Edge Function using the Supabase client
    console.log('USING SUPABASE EDGE FUNCTION FOR EMAIL TESTING');
    console.log('Express server is NOT running - this is 100% using the Edge Function');

    // Get the current session for authentication
    const { data: sessionData } = await supabase.auth.getSession();
    const accessToken = sessionData?.session?.access_token;

    // Log authentication status (without exposing the token)
    console.log('Authentication status:', accessToken ? 'Token available' : 'No token available');

    // Use direct fetch with authorization header instead of invoke
    try {
      const response = await fetch('https://qcnotlojmyvpqbbgoxbc.supabase.co/functions/v1/test-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken || ''}`,
        },
        body: JSON.stringify({
          config,
          testEmail
        })
      });

      // Check if the response is ok
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Edge Function error:', response.status, errorText);
        return {
          success: false,
          message: `Edge Function error: ${response.status} - ${errorText || response.statusText}`
        };
      }

      // Parse the response and return the result
      const data = await response.json();
      return {
        success: data.success,
        message: data.message
      };
    } catch (fetchError) {
      console.error('Fetch error:', fetchError);
      return {
        success: false,
        message: `Fetch error: ${fetchError.message}`
      };
    }
  } catch (error) {
    console.error('Error testing email:', error);
    return {
      success: false,
      message: `Error: ${error.message}`
    };
  }
}
