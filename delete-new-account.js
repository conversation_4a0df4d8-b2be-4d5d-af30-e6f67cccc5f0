// <PERSON>ript to delete the newly created Stripe Connect account
import dotenv from 'dotenv';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

// Initialize Stripe with the secret key
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;

if (!stripeSecretKey) {
  console.error('Missing Stripe secret key. Check your environment variables.');
  process.exit(1);
}

// Initialize Stripe with the secret key
const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2025-03-31.basil',
});

// Initialize Supabase client with service role key
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or service role key. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// The account ID to delete - can be passed as a command-line argument
const accountId = process.argv[2] || 'acct_1RG0kKPcwoXxMqF1';

console.log(`Using account ID: ${accountId}`);

async function deleteStripeAccount() {
  try {
    console.log(`Deleting Stripe Connect account ${accountId}...`);
    
    // First, retrieve the account to verify it exists
    try {
      const account = await stripe.accounts.retrieve(accountId);
      console.log(`Found account: ${account.id}`);
      
      // Get the user ID associated with this account from our database
      const { data: stripeAccount, error: fetchError } = await supabase
        .from('stripe_accounts')
        .select('user_id')
        .eq('account_id', accountId)
        .maybeSingle();
      
      if (fetchError) {
        console.error('Error fetching account from database:', fetchError);
      }
      
      const userId = stripeAccount?.user_id;
      console.log(`Associated user ID: ${userId || 'Not found in database'}`);
      
      // Delete the account in Stripe
      const deletedAccount = await stripe.accounts.del(accountId);
      
      if (deletedAccount.deleted) {
        console.log(`Successfully deleted Stripe account ${accountId}`);
        
        // Delete the account from the database
        if (userId) {
          // Delete the account from the database
          const { error: deleteError } = await supabase
            .from('stripe_accounts')
            .delete()
            .eq('account_id', accountId);
          
          if (deleteError) {
            console.error('Error deleting account from database:', deleteError);
          } else {
            console.log('Deleted account from database');
          }
          
          // Update the user's profile to remove the Stripe account ID
          const { error: profileError } = await supabase
            .from('profiles')
            .update({ stripe_account_id: null })
            .eq('id', userId);
          
          if (profileError) {
            console.error('Error updating user profile:', profileError);
          } else {
            console.log('Removed Stripe account ID from user profile');
          }
        } else {
          console.log('Account not found in database, no database updates needed');
        }
      } else {
        console.error('Failed to delete Stripe account');
      }
    } catch (error) {
      console.error(`Error retrieving or deleting account: ${error.message}`);
      
      if (error.code === 'account_invalid') {
        console.log('Account does not exist or has already been deleted.');
        
        // Still update our database to remove any references to this account
        const { data: stripeAccount, error: fetchError } = await supabase
          .from('stripe_accounts')
          .select('user_id')
          .eq('account_id', accountId)
          .maybeSingle();
        
        if (!fetchError && stripeAccount) {
          // Delete the account from the database
          await supabase
            .from('stripe_accounts')
            .delete()
            .eq('account_id', accountId);
          
          // Update the user's profile to remove the Stripe account ID
          await supabase
            .from('profiles')
            .update({ stripe_account_id: null })
            .eq('id', stripeAccount.user_id);
          
          console.log(`Updated database records for account ${accountId}`);
        }
      }
    }
  } catch (error) {
    console.error('Error in delete account script:', error);
    process.exit(1);
  }
}

// Execute the function
deleteStripeAccount();