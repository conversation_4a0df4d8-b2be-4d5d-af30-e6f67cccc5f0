// <PERSON>ript to delete the account from the UI
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

// Initialize Supabase client with service role key
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or service role key. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// The account ID to delete from the UI
const accountId = process.argv[2] || 'acct_1RG1JOPdh3upw4bs';

async function deleteAccountFromUI() {
  try {
    console.log(`Deleting account ${accountId} from the UI...`);
    
    // Get the user ID associated with this account from our database
    const { data: stripeAccount, error: fetchError } = await supabase
      .from('stripe_accounts')
      .select('user_id')
      .eq('account_id', accountId)
      .maybeSingle();
    
    if (fetchError) {
      console.error('Error fetching account from database:', fetchError);
      process.exit(1);
    }
    
    if (!stripeAccount) {
      console.error(`Account ${accountId} not found in database.`);
      process.exit(1);
    }
    
    const userId = stripeAccount.user_id;
    console.log(`Associated user ID: ${userId}`);
    
    // Delete the account from the database
    const { error: deleteError } = await supabase
      .from('stripe_accounts')
      .delete()
      .eq('account_id', accountId);
    
    if (deleteError) {
      console.error('Error deleting account from database:', deleteError);
      process.exit(1);
    }
    
    console.log(`Deleted account ${accountId} from the database.`);
    
    // Update the user's profile to remove the Stripe account ID
    const { error: profileError } = await supabase
      .from('profiles')
      .update({ stripe_account_id: null })
      .eq('id', userId);
    
    if (profileError) {
      console.error('Error updating user profile:', profileError);
      process.exit(1);
    }
    
    console.log(`Removed Stripe account ID from user profile.`);
    
    console.log(`Account ${accountId} has been deleted from the UI.`);
  } catch (error) {
    console.error('Error deleting account from UI:', error);
    process.exit(1);
  }
}

// Execute the function
deleteAccountFromUI();