import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// SECURITY: Get Supabase URL and key from environment variables - NEVER hardcode service role keys
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://qcnotlojmyvpqbbgoxbc.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Validate that the service role key is present
if (!supabaseKey) {
  console.error('SECURITY ERROR: SUPABASE_SERVICE_ROLE_KEY not found in environment variables');
  console.error('This script requires the service role key to function');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

console.log(`Using Supabase URL: ${supabaseUrl}`);
console.log(`Using Supabase Key: ${supabaseKey.substring(0, 10)}...`);

// Test users to create
const testUsers = [
  {
    email: '<EMAIL>',
    password: 'test-password-123',
    role: 'supplier',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    organization_id: null // Suppliers don't need an organization
  },
  {
    email: '<EMAIL>',
    password: 'test-password-123',
    role: 'admin',
    first_name: 'Admin',
    last_name: 'User',
    organization_id: null // We'll set this later if needed
  },
  {
    email: '<EMAIL>',
    password: 'test-password-123',
    role: 'maintenance',
    first_name: 'Maintenance',
    last_name: 'Staff',
    organization_id: null // We'll set this later if needed
  },
  {
    email: '<EMAIL>',
    password: 'test-password-123',
    role: 'support',
    first_name: 'Support',
    last_name: 'Staff',
    organization_id: null // We'll set this later if needed
  }
];

async function createTestUsers() {
  try {
    console.log('Starting test user creation...');

    // Step 1: Get or create an organization for the admin
    console.log('\nStep 1: Getting or creating an organization');

    let organizationId = null;

    // Check if any organizations exist
    const { data: organizations, error: orgError } = await supabase
      .from('organizations')
      .select('id, name')
      .limit(1);

    if (orgError) {
      console.error('Error checking organizations:', orgError);
    } else if (organizations && organizations.length > 0) {
      organizationId = organizations[0].id;
      console.log(`Using existing organization: ${organizations[0].name} (${organizationId})`);
    } else {
      // Create a new organization
      const { data: newOrg, error: createOrgError } = await supabase
        .from('organizations')
        .insert({
          name: 'Test School',
          type: 'school',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (createOrgError) {
        console.error('Error creating organization:', createOrgError);
      } else {
        organizationId = newOrg.id;
        console.log(`Created new organization: ${newOrg.name} (${organizationId})`);
      }
    }

    // Update the admin user with the organization ID
    if (organizationId) {
      const adminUser = testUsers.find(u => u.email === '<EMAIL>');
      if (adminUser) {
        adminUser.organization_id = organizationId;
      }
    }

    // Step 2: Process each test user
    for (const testUser of testUsers) {
      console.log(`\nProcessing user: ${testUser.email} (${testUser.role})`);

      // Step 2.1: Check if user exists in auth
      console.log(`Checking if ${testUser.email} exists in auth...`);

      const { data: { users }, error: listError } = await supabase.auth.admin.listUsers();

      if (listError) {
        console.error('Error listing users:', listError);
        continue;
      }

      const existingUser = users.find(u => u.email === testUser.email);
      let userId = null;

      if (existingUser) {
        console.log(`User ${testUser.email} already exists in auth with ID: ${existingUser.id}`);
        userId = existingUser.id;
      } else {
        // Step 2.2: Create user in auth
        console.log(`Creating user ${testUser.email} in auth...`);

        const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
          email: testUser.email,
          password: testUser.password,
          email_confirm: true,
          user_metadata: {
            first_name: testUser.first_name,
            last_name: testUser.last_name,
            role: testUser.role
          }
        });

        if (createError) {
          console.error(`Error creating user ${testUser.email}:`, createError);
          continue;
        }

        console.log(`Created user ${testUser.email} with ID: ${newUser.user.id}`);
        userId = newUser.user.id;
      }

      if (!userId) {
        console.error(`Could not get user ID for ${testUser.email}`);
        continue;
      }

      // Step 2.3: Check if profile exists
      console.log(`Checking if profile exists for ${testUser.email}...`);

      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (profileError && profileError.code !== 'PGRST116') {
        console.error(`Error checking profile for ${testUser.email}:`, profileError);
        continue;
      }

      if (profile) {
        console.log(`Profile already exists for ${testUser.email}`);

        // Step 2.4: Update profile if needed
        console.log(`Updating profile for ${testUser.email}...`);

        const updates = {
          role: testUser.role,
          first_name: testUser.first_name,
          last_name: testUser.last_name,
          organization_id: testUser.organization_id,
          email: [testUser.email],
          updated_at: new Date().toISOString()
        };

        const { error: updateError } = await supabase
          .from('profiles')
          .update(updates)
          .eq('id', userId);

        if (updateError) {
          console.error(`Error updating profile for ${testUser.email}:`, updateError);
        } else {
          console.log(`Successfully updated profile for ${testUser.email}`);
        }
      } else {
        // Step 2.5: Create profile
        console.log(`Creating profile for ${testUser.email}...`);

        const { error: createProfileError } = await supabase
          .from('profiles')
          .insert({
            id: userId,
            email: [testUser.email],
            role: testUser.role,
            first_name: testUser.first_name,
            last_name: testUser.last_name,
            organization_id: testUser.organization_id,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (createProfileError) {
          console.error(`Error creating profile for ${testUser.email}:`, createProfileError);
        } else {
          console.log(`Successfully created profile for ${testUser.email}`);
        }
      }
    }

    console.log('\nTest user creation completed');
  } catch (error) {
    console.error('Error in createTestUsers:', error);
  }
}

// Run the function
createTestUsers();
