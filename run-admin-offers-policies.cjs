// <PERSON>ript to run the SQL file that adds admin policies for offers
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

// Validate environment variables
if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.error('Error: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set in .env.local');
  process.exit(1);
}

// Create Supabase client with service role key for admin access
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function main() {
  try {
    console.log('Starting to apply admin offers policies...');

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'sql', 'add_admin_offers_policies.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    // Execute the SQL
    console.log('Executing SQL to add admin offers policies...');
    const { error } = await supabase.rpc('exec_sql', { sql: sqlContent });

    if (error) {
      console.error('Error executing SQL:', error);
      
      // Try an alternative approach if the exec_sql function doesn't exist
      console.log('Trying alternative approach with individual statements...');
      
      // Split the SQL into individual statements
      const statements = sqlContent
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0);
      
      // Execute each statement individually
      for (const stmt of statements) {
        console.log(`Executing statement: ${stmt.substring(0, 50)}...`);
        const { error } = await supabase.rpc('exec_sql', { sql: stmt });
        
        if (error) {
          console.error('Error executing statement:', error);
          throw error;
        }
      }
    }

    console.log('Successfully applied admin offers policies!');
    
    // Verify the policies were created
    console.log('Verifying policies...');
    const { data, error: listError } = await supabase
      .from('pg_policies')
      .select('*')
      .ilike('policyname', '%admin%offer%');
    
    if (listError) {
      console.error('Error verifying policies:', listError);
    } else {
      console.log('Policies found:', data?.length || 0);
      if (data && data.length > 0) {
        data.forEach(policy => {
          console.log(`- ${policy.policyname}`);
        });
      }
    }

  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

main();