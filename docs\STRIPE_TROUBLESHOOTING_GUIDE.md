# Stripe Integration Troubleshooting Guide

This document provides a comprehensive guide for troubleshooting Stripe integration issues in the ClassTasker application. It covers common problems, their potential causes, and recommended solutions.

## Table of Contents

1. [Environment Configuration Issues](#environment-configuration-issues)
2. [Stripe Connect Account Issues](#stripe-connect-account-issues)
3. [Payment Processing Issues](#payment-processing-issues)
4. [Webhook Issues](#webhook-issues)
5. [Database Integration Issues](#database-integration-issues)
6. [Invoice Generation Issues](#invoice-generation-issues)
7. [Testing and Development](#testing-and-development)
8. [Security Best Practices](#security-best-practices)

## Environment Configuration Issues

### API Key Mismatches

**Symptoms:**
- "Invalid API key" errors
- Authentication failures with Stripe API
- Payments failing at the initialization stage

**Potential Causes:**
- Using test keys in production or vice versa
- Outdated API keys
- Missing or incorrect API keys in environment variables

**Solutions:**
1. Verify that you're using the correct API keys for your environment:
   - Test keys start with `sk_test_` and `pk_test_`
   - Live keys start with `sk_live_` and `pk_live_`
2. Check that your environment variables are properly set:
   ```
   # Server-side
   STRIPE_SECRET_KEY=sk_test_51REKxHAwo0W7IrjoSqaZGrFgvvXG2QZRl6RN5CVpiQqVIExcX50qW0JrkxePyyTw4ERl89lBj2l0K7YVIm0IGX0w00dbu4fbiS
   
   # Client-side (for Vite)
   VITE_STRIPE_PUBLIC_KEY=pk_test_51RFz5yPWBcixtWOchgiZ3Zj7qsG8pV9W9IGbjwl2yim9iIQSDyR1hJhJpWGv8HFJGsswwfaEm0J8sjTdMilbUfLM00Sv1YQD4B
   ```
3. Ensure that frontend variables use the `VITE_` prefix for Vite projects
4. Restart your server after updating environment variables

### HTTPS Requirements

**Symptoms:**
- "Livemode requests must always be redirected via HTTPS" errors
- Redirect failures in live mode

**Solutions:**
1. Always use HTTPS URLs for redirect URLs in live mode
2. For local development, use test mode API keys
3. For production, ensure your domain has a valid SSL certificate
4. Update your environment variables with the correct HTTPS URLs:
   ```
   STRIPE_CONNECT_EXPRESS_RETURN_URL=https://your-production-domain.com/stripe-connect
   STRIPE_CONNECT_EXPRESS_REFRESH_URL=https://your-production-domain.com/stripe-connect
   ```

## Stripe Connect Account Issues

### Onboarding Problems

**Symptoms:**
- Users unable to complete onboarding
- "Account not found" errors
- Incomplete verification status

**Potential Causes:**
- Incorrect redirect URLs
- Missing capabilities in account creation
- Database inconsistencies

**Solutions:**
1. Verify that your Stripe Connect account creation includes all required capabilities:
   ```javascript
   const account = await stripe.accounts.create({
     type: 'express',
     country: 'GB',
     email: userEmail,
     capabilities: {
       card_payments: { requested: true },
       transfers: { requested: true },
       // Add other capabilities as needed
     },
     business_type: 'individual',
   });
   ```
2. Check that your redirect URLs are correctly configured and accessible
3. Verify that the account is being properly stored in your database:
   ```sql
   SELECT * FROM stripe_accounts WHERE user_id = 'user_id_here';
   ```
4. Use the Stripe CLI to check account status:
   ```
   stripe connect accounts retrieve acct_123456789
   ```

### Account Status Issues

**Symptoms:**
- Account shows as "pending" when it should be "active"
- Charges or payouts not enabled

**Solutions:**
1. Verify the account status in the Stripe Dashboard
2. Check that all required verification steps have been completed
3. Update the account status in your database:
   ```sql
   UPDATE stripe_accounts
   SET 
     charges_enabled = true,
     payouts_enabled = true,
     account_status = 'active'
   WHERE account_id = 'acct_123456789';
   ```
4. Ensure your webhook handler is properly updating account status:
   ```javascript
   case 'account.updated': {
     const account = event.data.object;
     await supabase
       .from('stripe_accounts')
       .update({
         charges_enabled: account.charges_enabled,
         payouts_enabled: account.payouts_enabled,
         account_status: account.charges_enabled && account.payouts_enabled ? 'active' : 'pending',
       })
       .eq('account_id', account.id);
   }
   ```

## Payment Processing Issues

### Payment Intent Creation Failures

**Symptoms:**
- "Failed to create payment intent" errors
- Payment process stops at initialization

**Potential Causes:**
- Invalid API keys
- Missing required parameters
- Stripe account not properly configured

**Solutions:**
1. Check your server logs for detailed error messages
2. Verify that your payment intent creation includes all required parameters:
   ```javascript
   const paymentIntentParams = {
     amount: amount * 100, // Convert to cents
     currency: 'gbp',
     payment_method_types: ['card', 'bacs_debit'],
     customer: stripeCustomerId,
     metadata: {
       task_id: taskId,
       offer_id: offerId,
     },
     // For Connect accounts
     application_fee_amount: platformFee * 100,
     transfer_data: {
       destination: stripeAccountId,
     },
   };
   ```
3. Ensure the Stripe account has the necessary capabilities enabled
4. Verify that the amount is valid (greater than minimum amount)

### Payment Confirmation Failures

**Symptoms:**
- Payments fail during confirmation
- "Payment failed" errors in the UI
- Payments stuck in "processing" state

**Potential Causes:**
- Card declined
- 3D Secure authentication failures
- Network issues

**Solutions:**
1. Check the Stripe Dashboard for detailed error messages
2. Implement better error handling in your payment confirmation code:
   ```javascript
   const { error, paymentIntent } = await stripe.confirmPayment({
     elements,
     confirmParams: {
       return_url: window.location.origin + '/payment-confirmation',
     },
     redirect: 'if_required',
   });
   
   if (error) {
     // Handle specific error types
     if (error.type === 'card_error') {
       setError('Your card was declined. Please try another payment method.');
     } else if (error.type === 'validation_error') {
       setError('Please check your card details and try again.');
     } else {
       setError(error.message || 'An unexpected error occurred.');
     }
     console.error('Payment error:', error);
   }
   ```
3. Ensure your UI properly handles all payment states (processing, succeeded, failed)
4. Implement retry logic for temporary failures

## Webhook Issues

### Webhook Verification Failures

**Symptoms:**
- "No signatures found matching the expected signature" errors
- Webhook events not being processed

**Potential Causes:**
- Incorrect webhook secret
- Missing or malformed signature header
- Request body modification

**Solutions:**
1. Verify that your webhook secret is correctly set in your environment variables:
   ```
   STRIPE_WEBHOOK_SECRET=whsec_o5pmNuZzrffmpbaJiVpq7FYXoT0NoAL4
   ```
2. Ensure your webhook endpoint is correctly verifying the signature:
   ```javascript
   const sig = req.headers['stripe-signature'];
   try {
     event = stripe.webhooks.constructEvent(
       req.body,
       sig,
       process.env.STRIPE_WEBHOOK_SECRET
     );
   } catch (err) {
     console.error(`Webhook signature verification failed: ${err.message}`);
     return res.status(400).send(`Webhook Error: ${err.message}`);
   }
   ```
3. Use the Stripe CLI to test your webhook locally:
   ```
   stripe listen --forward-to localhost:3001/api/stripe-webhook
   ```

### Missing Webhook Events

**Symptoms:**
- Database not being updated after payment events
- Account status not reflecting changes

**Potential Causes:**
- Webhook not registered for all required events
- Event handler missing for specific event types
- Errors in event processing

**Solutions:**
1. Register your webhook for all required events in the Stripe Dashboard
2. Implement handlers for all relevant event types:
   ```javascript
   switch (event.type) {
     case 'payment_intent.succeeded':
       // Handle successful payment
       break;
     case 'payment_intent.payment_failed':
       // Handle failed payment
       break;
     case 'account.updated':
       // Handle account updates
       break;
     // Add handlers for other event types
     default:
       console.log(`Unhandled event type: ${event.type}`);
   }
   ```
3. Add comprehensive logging to your webhook handler
4. Check the Stripe Dashboard for webhook delivery status

## Database Integration Issues

### Missing or Incorrect Tables

**Symptoms:**
- "relation does not exist" errors (code 42P01)
- Data not being stored or retrieved correctly

**Potential Causes:**
- Database schema not properly initialized
- Migration scripts not run
- Table names mismatch

**Solutions:**
1. Check if required tables exist and create them if needed:
   ```sql
   CREATE TABLE IF NOT EXISTS stripe_accounts (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     user_id UUID NOT NULL REFERENCES auth.users(id),
     account_id TEXT NOT NULL,
     account_type TEXT NOT NULL,
     charges_enabled BOOLEAN DEFAULT FALSE,
     payouts_enabled BOOLEAN DEFAULT FALSE,
     account_status TEXT DEFAULT 'pending',
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );
   
   CREATE TABLE IF NOT EXISTS stripe_customers (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     user_id UUID NOT NULL REFERENCES auth.users(id),
     customer_id TEXT NOT NULL,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );
   ```
2. Implement robust error handling for database operations:
   ```javascript
   if (error.code === '42P01') {
     console.warn('Table does not exist. Creating table...');
     // Create table or handle gracefully
   }
   ```
3. Verify table names and column names match what your code expects

### Data Synchronization Issues

**Symptoms:**
- Inconsistent data between Stripe and your database
- Missing or outdated account information

**Solutions:**
1. Implement a synchronization function to reconcile data:
   ```javascript
   async function syncStripeAccountData(userId) {
     // Get account from database
     const { data: account } = await supabase
       .from('stripe_accounts')
       .select('*')
       .eq('user_id', userId)
       .single();
     
     if (account) {
       // Get latest data from Stripe
       const stripeAccount = await stripe.accounts.retrieve(account.account_id);
       
       // Update database with latest Stripe data
       await supabase
         .from('stripe_accounts')
         .update({
           charges_enabled: stripeAccount.charges_enabled,
           payouts_enabled: stripeAccount.payouts_enabled,
           account_status: stripeAccount.charges_enabled && stripeAccount.payouts_enabled ? 'active' : 'pending',
           updated_at: new Date().toISOString(),
         })
         .eq('user_id', userId);
     }
   }
   ```
2. Run periodic synchronization jobs to ensure data consistency
3. Add validation checks before critical operations

## Invoice Generation Issues

### Invoice Creation Failures

**Symptoms:**
- "Failed to create invoice" errors
- Missing invoices after successful payments

**Potential Causes:**
- Missing customer information
- Invalid payment data
- Stripe API errors

**Solutions:**
1. Ensure customer exists before creating an invoice:
   ```javascript
   // Get or create customer
   const stripeCustomerId = await getOrCreateCustomer(userId, email, name);
   if (!stripeCustomerId) {
     throw new Error('Failed to create or retrieve Stripe customer');
   }
   ```
2. Verify that all required invoice data is present:
   ```javascript
   const invoice = await stripe.invoices.create({
     customer: stripeCustomerId,
     collection_method: 'charge_automatically',
     auto_advance: true,
     metadata: {
       payment_id: paymentId,
       task_id: taskId,
     },
   });
   ```
3. Implement retry logic for invoice creation
4. Add detailed logging for invoice creation process

### Email Delivery Issues

**Symptoms:**
- Invoices created but not delivered via email
- "Email not sent" errors

**Solutions:**
1. Verify that the customer has a valid email address
2. Check that Stripe is configured to send invoice emails:
   ```javascript
   await stripe.invoices.sendInvoice(invoice.id);
   ```
3. Implement a fallback email delivery mechanism using your own email service
4. Add a UI option for users to request invoice emails manually

## Testing and Development

### Test Mode vs. Live Mode

**Best Practices:**
1. Always use test mode API keys for development and testing
2. Create a separate set of test accounts for each environment
3. Use Stripe's test card numbers for payment testing:
   - `4242 4242 4242 4242` - Successful payment
   - `4000 0000 0000 0002` - Declined payment
   - `4000 0027 6000 3184` - 3D Secure authentication required
4. Test all payment flows, including failures and edge cases

### Local Development with Stripe CLI

The Stripe CLI is invaluable for local development and testing:

1. Install the Stripe CLI: [https://stripe.com/docs/stripe-cli](https://stripe.com/docs/stripe-cli)
2. Log in and connect to your Stripe account:
   ```
   stripe login
   ```
3. Forward webhook events to your local server:
   ```
   stripe listen --forward-to localhost:3001/api/stripe-webhook
   ```
4. Trigger test webhook events:
   ```
   stripe trigger payment_intent.succeeded
   ```
5. Inspect Connect accounts:
   ```
   stripe connect accounts list
   stripe connect accounts get acct_123456789
   ```

## Security Best Practices

### API Key Management

1. Never expose your Stripe secret key in client-side code
2. Use environment variables to store API keys
3. Rotate API keys periodically and after team member departures
4. Use restricted API keys with only the permissions they need

### Data Protection

1. Minimize the payment data you store in your own database
2. Never store full card details
3. Use Stripe's PCI-compliant elements for collecting payment information
4. Implement proper access controls for payment and customer data

### Error Handling and Logging

1. Log detailed error information for debugging but sanitize sensitive data
2. Implement structured error handling for all Stripe operations
3. Set up monitoring and alerts for payment failures
4. Create a process for reviewing and addressing recurring payment issues

## Checklist for Troubleshooting

When encountering Stripe integration issues, follow this checklist:

1. **Check Environment Variables**
   - Are the correct API keys set?
   - Are webhook secrets configured?
   - Are redirect URLs properly set?

2. **Verify Database State**
   - Do all required tables exist?
   - Is the data consistent with Stripe's records?
   - Are there any orphaned or incomplete records?

3. **Inspect Stripe Dashboard**
   - Check the logs for detailed error messages
   - Verify account status and capabilities
   - Check webhook delivery status

4. **Review Server Logs**
   - Look for detailed error messages
   - Check for webhook verification failures
   - Identify any API call failures

5. **Test with Stripe CLI**
   - Trigger test events
   - Verify webhook delivery
   - Check account status

6. **Verify Frontend Integration**
   - Is the Stripe.js library properly loaded?
   - Are Elements configured correctly?
   - Is error handling implemented?

By following this guide, you should be able to identify and resolve most Stripe integration issues in the ClassTasker application.
