import React, { useState, useEffect, useRef } from 'react';

type LogType = 'log' | 'error' | 'warn' | 'info';

interface LogEntry {
  type: LogType;
  content: string;
  time: string;
}

/**
 * Debug panel component for PWA debugging
 * Activate by tapping 5 times in the top-left corner of the screen
 */
const DebugPanel: React.FC = () => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [visible, setVisible] = useState(false);
  const [tapCount, setTapCount] = useState(0);
  const logContainerRef = useRef<HTMLDivElement>(null);

  // Capture console logs when panel is visible
  useEffect(() => {
    if (!visible) return;

    const originalLog = console.log;
    const originalError = console.error;
    const originalWarn = console.warn;
    const originalInfo = console.info;

    console.log = (...args) => {
      originalLog(...args);
      addLog('log', args);
    };

    console.error = (...args) => {
      originalError(...args);
      addLog('error', args);
    };

    console.warn = (...args) => {
      originalWarn(...args);
      addLog('warn', args);
    };

    console.info = (...args) => {
      originalInfo(...args);
      addLog('info', args);
    };

    return () => {
      console.log = originalLog;
      console.error = originalError;
      console.warn = originalWarn;
      console.info = originalInfo;
    };
  }, [visible]);

  // Auto-scroll to bottom of logs
  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logs]);

  const addLog = (type: LogType, args: any[]) => {
    const logContent = args.map(arg =>
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');

    setLogs(prev => [...prev, { type, content: logContent, time: new Date().toISOString() }].slice(-100));
  };

  // Handle tap to show debug panel
  const handleTap = () => {
    setTapCount(prev => {
      if (prev >= 4) {
        setVisible(true);
        return 0;
      }
      return prev + 1;
    });

    // Reset tap count after 2 seconds
    setTimeout(() => setTapCount(0), 2000);
  };

  // Clear logs
  const clearLogs = () => setLogs([]);

  // Test GetStream connection
  const testGetStream = () => {
    console.log('[DEBUG] Testing GetStream connection...');

    // Log environment info
    console.log('[DEBUG] Environment:', {
      isPWA: window.matchMedia('(display-mode: standalone)').matches,
      isOnline: navigator.onLine,
      userAgent: navigator.userAgent,
      apiKey: (window as any).env?.VITE_GETSTREAM_API_KEY || 'not found',
      origin: window.location.origin,
      pathname: window.location.pathname
    });

    // Test API URL construction
    const getStreamApiUrl = (endpoint: string) => {
      const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
      const isPWA = window.matchMedia('(display-mode: standalone)').matches;
      const isProduction = process.env.NODE_ENV === 'production';

      let url;
      if (isPWA && isProduction) {
        url = `${window.location.origin}/api/getstream${normalizedEndpoint}`;
      } else {
        url = `/api/getstream${normalizedEndpoint}`;
      }

      console.log('[DEBUG] Constructed API URL:', url);
      return url;
    };

    // Test token endpoint
    fetch(getStreamApiUrl('/token'), {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ userId: 'debug-test' })
    })
    .then(res => {
      console.log('[DEBUG] Token response status:', res.status);
      return res.text();
    })
    .then(text => {
      try {
        const json = JSON.parse(text);
        console.log('[DEBUG] Token response:', json);
      } catch (e) {
        console.log('[DEBUG] Token response (text):', text);
      }
    })
    .catch(err => {
      console.error('[DEBUG] Token fetch error:', err.message);
    });
  };

  // Test PWA status
  const testPWA = () => {
    console.log('[DEBUG] Testing PWA status...');

    const isPWA = window.matchMedia('(display-mode: standalone)').matches;
    console.log('[DEBUG] Running as PWA:', isPWA);

    console.log('[DEBUG] Service Worker:', {
      supported: 'serviceWorker' in navigator,
      controller: !!navigator.serviceWorker.controller,
    });

    console.log('[DEBUG] Window Environment:', {
      windowEnv: 'REDACTED_FOR_SECURITY',
      windowEnvGetStreamKey: (window as any).env?.VITE_GETSTREAM_API_KEY ? '[PRESENT]' : '[NOT_SET]',
      importMetaEnv: import.meta.env.VITE_GETSTREAM_API_KEY ? '[PRESENT]' : '[NOT_SET]'
    });
  };

  if (!visible) {
    return (
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: 50,
          height: 50,
          zIndex: 9999,
          opacity: 0,
          pointerEvents: 'auto'
        }}
        onClick={handleTap}
        onTouchStart={handleTap}
      />
    );
  }

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.9)',
      color: 'white',
      zIndex: 9999,
      padding: 10,
      display: 'flex',
      flexDirection: 'column'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 10 }}>
        <h3 style={{ margin: 0 }}>PWA Debug Panel</h3>
        <div>
          <button onClick={testPWA} style={{ marginRight: 10, padding: '4px 8px' }}>
            Test PWA
          </button>
          <button onClick={testGetStream} style={{ marginRight: 10, padding: '4px 8px' }}>
            Test GetStream
          </button>
          <button onClick={clearLogs} style={{ marginRight: 10, padding: '4px 8px' }}>
            Clear
          </button>
          <button onClick={() => setVisible(false)} style={{ padding: '4px 8px' }}>
            Close
          </button>
        </div>
      </div>

      <div
        ref={logContainerRef}
        style={{
          flex: 1,
          overflow: 'auto',
          fontSize: '12px',
          fontFamily: 'monospace',
          backgroundColor: '#111',
          padding: 10
        }}
      >
        {logs.map((log, i) => (
          <div key={i} style={{
            color:
              log.type === 'error' ? '#ff6b6b' :
              log.type === 'warn' ? '#feca57' :
              log.type === 'info' ? '#1dd1a1' : '#54a0ff',
            marginBottom: 5,
            borderBottom: '1px solid #333',
            paddingBottom: 5
          }}>
            <span style={{ opacity: 0.7, marginRight: 5 }}>
              {log.time.split('T')[1].split('.')[0]}
            </span>
            <pre style={{ margin: 0, whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
              {log.content}
            </pre>
          </div>
        ))}
      </div>
    </div>
  );
};

/**
 * Higher-order component to add debug panel to any component
 * @param Component The component to wrap with the debug panel
 */
export const withDebugPanel = <P extends object>(Component: React.ComponentType<P>): React.FC<P> => {
  return (props: P) => (
    <>
      <Component {...props} />
      <DebugPanel />
    </>
  );
};

export default DebugPanel;
