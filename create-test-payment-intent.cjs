// Script to create a test payment intent
require('dotenv').config();
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

async function createTestPaymentIntent() {
  try {
    console.log('Creating test payment intent...');
    
    // Create a test payment intent with minimal parameters
    const paymentIntent = await stripe.paymentIntents.create({
      amount: 2000, // £20.00
      currency: 'gbp',
      payment_method_types: ['card'],
      // No transfer_data or application_fee_amount for this test
    });
    
    console.log('Test Payment Intent Created:');
    console.log('---------------------------');
    console.log(`ID: ${paymentIntent.id}`);
    console.log(`Client Secret: ${paymentIntent.client_secret}`);
    console.log(`Status: ${paymentIntent.status}`);
    console.log(`Amount: ${paymentIntent.amount / 100} ${paymentIntent.currency.toUpperCase()}`);
    console.log(`Payment Method Types: ${paymentIntent.payment_method_types.join(', ')}`);
    
    // Instructions for testing
    console.log('\nTo test this payment intent:');
    console.log('1. Use the client secret in your Stripe Elements initialization');
    console.log('2. This is a regular payment intent (not Connect), so it should work with standard Stripe Elements');
    console.log('3. If this works, the issue is likely with the Connect payment intent parameters');
    
  } catch (error) {
    console.error('Error creating test payment intent:', error);
  }
}

createTestPaymentIntent();
