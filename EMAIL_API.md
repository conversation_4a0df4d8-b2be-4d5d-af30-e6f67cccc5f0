# Email API Documentation

This document explains how the email sending API works in Classtasker.

## Overview

Classtasker uses a Vercel API route to send emails via SMTP. The API route is located at `/api/send-email` and handles:

- User invitations
- Notifications
- Password resets
- Test emails

## How It Works

1. The frontend calls the `/api/send-email` API route
2. The API route uses nodemailer to send emails via SMTP
3. The API returns a success or error response

## API Request Format

```json
{
  "config": {
    "provider": "smtp",
    "fromEmail": "<EMAIL>",
    "fromName": "Classtasker",
    "smtpHost": "server326.web-hosting.com",
    "smtpPort": 465,
    "smtpUsername": "<EMAIL>",
    "smtpPassword": "your-password",
    "smtpSecure": true
  },
  "params": {
    "to": "<EMAIL>",
    "subject": "Email Subject",
    "body": "<h1>HTML Email Content</h1><p>This is the email body.</p>"
  }
}
```

## API Response Format

### Success Response

```json
{
  "success": true,
  "message": "Email <NAME_EMAIL> using smtp provider. Message ID: <message-id>"
}
```

### Error Response

```json
{
  "error": "Error message describing what went wrong"
}
```

## SMTP Configuration

The default SMTP configuration is:

- **SMTP Host**: server326.web-hosting.com
- **SMTP Port**: 465
- **SMTP Username**: <EMAIL>
- **SMTP Password**: (set in the UI)
- **SSL/TLS**: Enabled (true)

## Troubleshooting

### Common Issues

1. **Connection Refused**:
   - Check if the SMTP host is correct
   - Verify the port number (common ports: 25, 465, 587)
   - Make sure your network allows outgoing connections on that port

2. **Authentication Failed**:
   - Double-check your username and password
   - Make sure your account has SMTP access enabled

3. **SSL/TLS Issues**:
   - Try toggling the SSL/TLS option
   - Port 465 typically requires SSL enabled (smtpSecure: true)
   - Port 587 typically uses STARTTLS (smtpSecure: false)

### Debugging

To see detailed logs:

1. Go to the Vercel dashboard
2. Navigate to your project
3. Go to "Functions" tab
4. Look for the `/api/send-email` function
5. Check the logs for detailed information

## Local Development

For local development, you can test the API by:

1. Running the development server: `npm run dev`
2. Sending a test email from the Email Configuration page
3. Checking the console logs for detailed information
