// <PERSON><PERSON>t to create a test invitation with a unique email
import { createClient } from '@supabase/supabase-js';
import { randomUUID } from 'crypto';

// SECURITY: Create Supabase client with service role key for admin access - NEVER hardcode keys
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Validate that the service role key is present
if (!supabaseServiceKey) {
  console.error('SECURITY ERROR: SUPABASE_SERVICE_ROLE_KEY not found in environment variables');
  console.error('This script requires the service role key to function');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createTestInvitation() {
  try {
    // Generate a unique token
    const token = randomUUID();

    // Generate a unique email
    const uniqueEmail = `test.${Date.now()}@example.com`;

    // Get the first organization
    const { data: orgs, error: orgsError } = await supabase
      .from('organizations')
      .select('id')
      .limit(1);

    if (orgsError || !orgs || orgs.length === 0) {
      console.error('Error fetching organizations:', orgsError);
      return;
    }

    const organizationId = orgs[0].id;

    // Get the first admin user
    const { data: { users }, error: usersError } = await supabase.auth.admin.listUsers();

    if (usersError || !users || users.length === 0) {
      console.error('Error fetching users:', usersError);
      return;
    }

    const adminUser = users[0];

    // Create the invitation
    const { data, error } = await supabase
      .from('user_invitations')
      .insert({
        email: uniqueEmail,
        organization_id: organizationId,
        role: 'teacher',
        invited_by: adminUser.id,
        token: token,
        status: 'pending',
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating invitation:', error);
      return;
    }

    console.log('Created test invitation:');
    console.log(data);

    // Generate the invitation URL
    const invitationUrl = `http://localhost:8082/invitation/accept?token=${token}`;
    console.log('\nInvitation URL:');
    console.log(invitationUrl);

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

createTestInvitation();
