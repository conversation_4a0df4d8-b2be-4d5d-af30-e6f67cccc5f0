import React, { useState } from 'react';
import PWAMobileLayout from '@/components/pwa/PWAMobileLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, FileText, Shield } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const PWATermsAndPrivacy = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('terms');

  return (
    <PWAMobileLayout>
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center mb-4">
          <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-lg font-semibold ml-2">Terms & Privacy</h1>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="terms" className="flex items-center">
              <FileText className="h-4 w-4 mr-1" />
              Terms
            </TabsTrigger>
            <TabsTrigger value="privacy" className="flex items-center">
              <Shield className="h-4 w-4 mr-1" />
              Privacy
            </TabsTrigger>
          </TabsList>

          <TabsContent value="terms">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-classtasker-blue">Terms of Service</CardTitle>
                <p className="text-sm text-gray-600">Last updated: {new Date().toLocaleDateString('en-GB', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
              </CardHeader>
              <CardContent className="space-y-4 text-sm">
                <section>
                  <h3 className="font-semibold mb-2">1. Acceptance of Terms</h3>
                  <p className="text-gray-700 leading-relaxed">
                    By using ClassTasker, you accept and agree to be bound by these terms.
                  </p>
                </section>

                <section>
                  <h3 className="font-semibold mb-2">2. Service Description</h3>
                  <p className="text-gray-700 leading-relaxed mb-2">
                    ClassTasker connects schools with maintenance professionals:
                  </p>
                  <ul className="list-disc pl-4 space-y-1 text-gray-700">
                    <li>Task posting and management</li>
                    <li>Supplier marketplace</li>
                    <li>Communication tools</li>
                    <li>Payment processing</li>
                  </ul>
                </section>

                <section>
                  <h3 className="font-semibold mb-2">3. User Responsibilities</h3>
                  <ul className="list-disc pl-4 space-y-1 text-gray-700">
                    <li>Provide accurate information</li>
                    <li>Keep account secure</li>
                    <li>Use service lawfully</li>
                    <li>Respect other users</li>
                  </ul>
                </section>

                <section>
                  <h3 className="font-semibold mb-2">4. Payment & Billing</h3>
                  <ul className="list-disc pl-4 space-y-1 text-gray-700">
                    <li>Fees in British Pounds (£)</li>
                    <li>Non-refundable unless stated</li>
                    <li>30 days notice for price changes</li>
                  </ul>
                </section>

                <section>
                  <h3 className="font-semibold mb-2">5. Limitation of Liability</h3>
                  <p className="text-gray-700 leading-relaxed">
                    ClassTasker is not liable for indirect or consequential damages 
                    from your use of the service.
                  </p>
                </section>

                <section>
                  <h3 className="font-semibold mb-2">6. Governing Law</h3>
                  <p className="text-gray-700 leading-relaxed">
                    These terms are governed by English law and subject to 
                    English court jurisdiction.
                  </p>
                </section>

                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-gray-700 text-xs">
                    <strong>Contact:</strong> <EMAIL>
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="privacy">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-classtasker-blue">Privacy Policy</CardTitle>
                <p className="text-sm text-gray-600">Last updated: {new Date().toLocaleDateString('en-GB', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
              </CardHeader>
              <CardContent className="space-y-4 text-sm">
                <section>
                  <h3 className="font-semibold mb-2">1. Information We Collect</h3>
                  <p className="text-gray-700 leading-relaxed mb-2">
                    We collect information you provide and usage data:
                  </p>
                  <ul className="list-disc pl-4 space-y-1 text-gray-700">
                    <li>Account and profile information</li>
                    <li>School/organization details</li>
                    <li>Usage and device information</li>
                    <li>Communication data</li>
                  </ul>
                </section>

                <section>
                  <h3 className="font-semibold mb-2">2. How We Use Information</h3>
                  <ul className="list-disc pl-4 space-y-1 text-gray-700">
                    <li>Provide and improve services</li>
                    <li>Process transactions</li>
                    <li>Send important updates</li>
                    <li>Ensure security and prevent fraud</li>
                  </ul>
                </section>

                <section>
                  <h3 className="font-semibold mb-2">3. Information Sharing</h3>
                  <p className="text-gray-700 leading-relaxed mb-2">
                    We share information only when necessary:
                  </p>
                  <ul className="list-disc pl-4 space-y-1 text-gray-700">
                    <li>With other users (profile info)</li>
                    <li>Service providers who assist us</li>
                    <li>Legal authorities when required</li>
                  </ul>
                </section>

                <section>
                  <h3 className="font-semibold mb-2">4. Your Rights (GDPR)</h3>
                  <ul className="list-disc pl-4 space-y-1 text-gray-700">
                    <li>Access your data</li>
                    <li>Correct inaccuracies</li>
                    <li>Request deletion</li>
                    <li>Data portability</li>
                    <li>Withdraw consent</li>
                  </ul>
                </section>

                <section>
                  <h3 className="font-semibold mb-2">5. Data Security</h3>
                  <p className="text-gray-700 leading-relaxed">
                    We use appropriate security measures to protect your information, 
                    though no system is completely secure.
                  </p>
                </section>

                <section>
                  <h3 className="font-semibold mb-2">6. Cookies</h3>
                  <p className="text-gray-700 leading-relaxed">
                    We use cookies to improve functionality and analyze usage. 
                    You can control cookies in your browser settings.
                  </p>
                </section>

                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-gray-700 text-xs">
                    <strong>Contact:</strong> <EMAIL><br />
                    <strong>DPO:</strong> <EMAIL>
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="mt-4 space-y-2">
          <Button 
            variant="outline" 
            className="w-full" 
            onClick={() => navigate('/help')}
          >
            Help Center
          </Button>
          <Button 
            variant="outline" 
            className="w-full" 
            onClick={() => navigate('/contact')}
          >
            Contact Support
          </Button>
        </div>
      </div>
    </PWAMobileLayout>
  );
};

export default PWATermsAndPrivacy;
