import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { useUser } from '@/hooks/useUser';
import { useProfile } from '@/hooks/useProfile';
import { Task } from '@/types';

interface TaskRenderingDebugProps {
  task: Task;
}

const TaskRenderingDebug: React.FC<TaskRenderingDebugProps> = ({ task }) => {
  const { user } = useUser();
  const { profile } = useProfile();

  if (!user || !profile || !task) {
    return null;
  }

  // Calculate all the conditions that affect rendering
  const isInternalTask = task.visibility === 'internal';
  const isMaintenance = profile.role === 'maintenance';
  const isAdmin = profile.role === 'admin';
  const isTaskOwner = task.user_id === user.id;
  const isAssignedStaff = String(task.assigned_to) === String(user.id);
  
  // Calculate the rendering conditions
  const internalTaskActionsCondition = 
    (isInternalTask && (isAdmin || isTaskOwner)) ||
    (isInternalTask && isMaintenance && isAssignedStaff) ||
    (isMaintenance && isAssignedStaff);

  return (
    <Card className="mb-4 bg-yellow-50 border-yellow-200">
      <CardHeader>
        <CardTitle className="text-sm">Task Rendering Debug</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-xs space-y-1">
          <p><strong>Task Info:</strong></p>
          <p>ID: {task.id}</p>
          <p>Title: {task.title}</p>
          <p>Status: {task.status}</p>
          <p>Visibility: {task.visibility}</p>
          <p>Assigned To: {task.assigned_to || 'None'}</p>
          
          <p className="mt-2"><strong>User Info:</strong></p>
          <p>User ID: {user.id}</p>
          <p>Role: {profile.role || 'None'}</p>
          
          <p className="mt-2"><strong>Conditions:</strong></p>
          <p>Is Internal Task: {isInternalTask ? '✅' : '❌'}</p>
          <p>Is Maintenance Role: {isMaintenance ? '✅' : '❌'}</p>
          <p>Is Admin: {isAdmin ? '✅' : '❌'}</p>
          <p>Is Task Owner: {isTaskOwner ? '✅' : '❌'}</p>
          <p>Is Assigned Staff: {isAssignedStaff ? '✅' : '❌'}</p>
          
          <p className="mt-2"><strong>Rendering Results:</strong></p>
          <p>Should Show Internal Task Actions: {internalTaskActionsCondition ? '✅' : '❌'}</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default TaskRenderingDebug;
