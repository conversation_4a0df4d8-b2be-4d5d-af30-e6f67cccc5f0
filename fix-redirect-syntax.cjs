// Script to fix the redirect syntax error in EnhancedPaymentProcessor.tsx
const fs = require('fs');
const path = require('path');

// Paths
const processorPath = path.join(__dirname, 'src', 'components', 'stripe', 'EnhancedPaymentProcessor.tsx');
const backupPath = path.join(__dirname, 'src', 'components', 'stripe', 'EnhancedPaymentProcessor.tsx.bak4');

// Create a backup of the original file
console.log('Creating backup of EnhancedPaymentProcessor.tsx...');
fs.copyFileSync(processorPath, backupPath);
console.log(`Backup created at ${backupPath}`);

// Read the file content
console.log('Reading EnhancedPaymentProcessor.tsx...');
let content = fs.readFileSync(processorPath, 'utf8');

// Fix the syntax error with the redirect property
if (content.includes('// Save payment method for future use\n        redirect: \'if_required\',')) {
  console.log('Fixing redirect syntax error...');
  
  // Move the redirect property inside the confirmParams object
  content = content.replace(
    '// Save payment method for future use\n        redirect: \'if_required\',',
    '// Save payment method for future use\n          redirect: \'if_required\','
  );
}

// Write the modified content back to the file
fs.writeFileSync(processorPath, content, 'utf8');
console.log('Fix applied successfully!');

console.log('\nChanges made:');
console.log('- Fixed syntax error by moving the redirect property inside the confirmParams object');

console.log('\nPlease refresh the page and try the payment again.');
console.log('\nIf you need to restore the original file, run:');
console.log(`cp ${backupPath} ${processorPath}`);