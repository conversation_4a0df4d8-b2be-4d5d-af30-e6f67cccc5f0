// <PERSON>ript to create a Stripe Connect Express account
import dotenv from 'dotenv';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

// Initialize Stripe with the secret key
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;

if (!stripeSecretKey) {
  console.error('Missing Stripe secret key. Check your environment variables.');
  process.exit(1);
}

// Initialize Stripe with the secret key
const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2025-03-31.basil',
});

// Initialize Supabase client with service role key
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or service role key. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// The user ID to create an account for
const userId = process.argv[2] || '4288cd97-e3ed-4e1d-8d22-abdc0d3f28bd';

async function createStripeAccount() {
  try {
    console.log(`Creating Stripe Connect account for user ${userId}...`);
    
    // Check if user already has an account
    const { data: existingAccount, error: fetchError } = await supabase
      .from('stripe_accounts')
      .select('*')
      .eq('user_id', userId)
      .neq('account_status', 'deleted')
      .maybeSingle();
    
    if (fetchError) {
      console.error('Error checking for existing account:', fetchError);
      process.exit(1);
    }
    
    if (existingAccount) {
      console.log(`User ${userId} already has a Stripe account in the database:`, existingAccount);
      console.log('Deleting the existing account from the database...');
      
      // Delete the account from the database
      const { error: deleteError } = await supabase
        .from('stripe_accounts')
        .delete()
        .eq('account_id', existingAccount.account_id);
      
      if (deleteError) {
        console.error('Error deleting account from database:', deleteError);
        process.exit(1);
      }
      
      console.log('Existing account deleted from the database.');
      
      // Update the user's profile to remove the Stripe account ID
      const { error: profileError } = await supabase
        .from('profiles')
        .update({ stripe_account_id: null })
        .eq('id', userId);
      
      if (profileError) {
        console.error('Error updating user profile:', profileError);
      } else {
        console.log('Removed Stripe account ID from user profile');
      }
    }
    
    // Get user profile for business information
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (profileError) {
      console.error('Error fetching user profile:', profileError);
      process.exit(1);
    }
    
    // Create a Stripe Connect Express account
    console.log('Creating Stripe Connect Express account...');
    
    const account = await stripe.accounts.create({
      type: 'express',
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
      business_type: 'individual',
      metadata: {
        user_id: userId,
      },
    });
    
    console.log(`Created Stripe account ${account.id} for user ${userId}`);
    
    // Store the account in our database
    const { data, error } = await supabase
      .from('stripe_accounts')
      .insert([{
        user_id: userId,
        account_id: account.id,
        account_type: 'express',
        charges_enabled: account.charges_enabled,
        payouts_enabled: account.payouts_enabled,
        account_status: 'pending',
      }])
      .select();
    
    if (error) {
      console.error('Error storing Stripe account:', error);
      process.exit(1);
    }
    
    console.log('Stored account in database:', data[0]);
    
    // Update the user's profile with the Stripe account ID
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ stripe_account_id: account.id })
      .eq('id', userId);
    
    if (updateError) {
      console.error('Error updating user profile:', updateError);
    } else {
      console.log(`Updated user profile with Stripe account ID: ${account.id}`);
    }
    
    // Generate an onboarding link
    await generateOnboardingLink(account.id);
  } catch (error) {
    console.error('Error creating Stripe account:', error);
    process.exit(1);
  }
}

async function generateOnboardingLink(accountId) {
  try {
    console.log(`Generating onboarding link for account ${accountId}...`);
    
    // Log the Stripe API key mode
    console.log(`Using Stripe API key: ${stripeSecretKey.startsWith('sk_test_') ? 'TEST MODE' : 'LIVE MODE'}`);
    
    // Use test mode URLs for test mode accounts
    const refreshUrl = stripeSecretKey.startsWith('sk_test_')
      ? 'https://dashboard.stripe.com/test/connect/express/refresh'
      : 'http://localhost:8082/stripe-connect';
    
    const returnUrl = stripeSecretKey.startsWith('sk_test_')
      ? 'https://dashboard.stripe.com/test/connect/express/return'
      : 'http://localhost:8082/stripe-connect';
    
    console.log(`Using refresh URL: ${refreshUrl}`);
    console.log(`Using return URL: ${returnUrl}`);
    
    const accountLink = await stripe.accountLinks.create({
      account: accountId,
      refresh_url: refreshUrl,
      return_url: returnUrl,
      type: 'account_onboarding',
    });
    
    console.log('Onboarding link generated:', accountLink.url);
    console.log('You can use this link to onboard the account.');
    
    // Open the link in the default browser
    const { exec } = await import('child_process');
    exec(`start ${accountLink.url}`);
  } catch (error) {
    console.error('Error generating onboarding link:', error);
  }
}

// Execute the function
createStripeAccount();