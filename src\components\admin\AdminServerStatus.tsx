import { useState, useEffect } from 'react';
import { AlertCircle, CheckCircle } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

const AdminServerStatus = () => {
  const [isServerRunning, setIsServerRunning] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkServerStatus = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('http://localhost:3001/', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        setIsServerRunning(response.ok);
      } catch (error) {
        console.error('Error checking admin server status:', error);
        setIsServerRunning(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkServerStatus();

    // Check server status every 30 seconds
    const intervalId = setInterval(checkServerStatus, 30000);

    return () => clearInterval(intervalId);
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center">
        <div className="h-2 w-2 rounded-full bg-gray-300 animate-pulse mr-1"></div>
        <span className="text-xs text-gray-500">Checking...</span>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center cursor-help">
            {isServerRunning ? (
              <>
                <CheckCircle className="h-3 w-3 text-green-500 mr-1" />
                <span className="text-xs text-green-600">Admin Server Online</span>
              </>
            ) : (
              <>
                <AlertCircle className="h-3 w-3 text-red-500 mr-1" />
                <span className="text-xs text-red-600">Admin Server Offline</span>
              </>
            )}
          </div>
        </TooltipTrigger>
        <TooltipContent side="right">
          {isServerRunning 
            ? 'The admin server is running. All system-wide admin features are available.'
            : 'The admin server is not running. Some system-wide admin features may be unavailable.'}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default AdminServerStatus;
