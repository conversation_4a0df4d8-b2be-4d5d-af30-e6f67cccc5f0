<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Send-Email Function Direct</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .log { max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🧪 Test Send-Email Function Direct</h1>
    <p>This tests the send-email Edge Function directly with the exact format it expects.</p>

    <div class="test-section">
        <h3>Test 1: Basic Email Test</h3>
        <button onclick="testBasicEmail()">Test Basic Email</button>
        <div id="basic-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Security Alert Format</h3>
        <button onclick="testSecurityAlert()">Test Security Alert Email</button>
        <div id="security-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Debug Mode</h3>
        <button onclick="testDebugMode()">Test Debug Mode (No Real Email)</button>
        <div id="debug-result"></div>
    </div>

    <div class="test-section">
        <h3>📋 Test Logs</h3>
        <button onclick="clearLogs()">Clear Logs</button>
        <div id="logs" class="log"></div>
    </div>

    <script>
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logs.scrollTop = logs.scrollHeight;
            console.log(message);
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        async function testBasicEmail() {
            const resultDiv = document.getElementById('basic-result');
            resultDiv.innerHTML = '<p>Testing basic email...</p>';
            
            try {
                log('🧪 Testing basic email with send-email function');
                
                const emailRequest = {
                    config: {
                        provider: 'resend',
                        fromEmail: '<EMAIL>',
                        fromName: 'ClassTasker Test'
                    },
                    params: {
                        to: '<EMAIL>',
                        subject: 'Test Email from ClassTasker',
                        body: '<h2>Test Email</h2><p>This is a test email from the send-email function.</p><p>If you receive this, the function is working correctly!</p>'
                    }
                };

                log('📤 Sending request to send-email function...');
                log('Request payload: ' + JSON.stringify(emailRequest, null, 2));

                const response = await fetch('https://qcnotlojmyvpqbbgoxbc.supabase.co/functions/v1/send-email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.nZic-dTJpnWJbBKUqZGz5O_9wVhPJhqq8vNJhJqJhJo'
                    },
                    body: JSON.stringify(emailRequest)
                });

                log(`📨 Response status: ${response.status} ${response.statusText}`);

                const responseData = await response.json();
                log('📨 Response data: ' + JSON.stringify(responseData, null, 2));

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Success!</h4>
                            <p>Email sent successfully!</p>
                            <pre>${JSON.stringify(responseData, null, 2)}</pre>
                        </div>
                    `;
                    log('✅ Basic email test PASSED');
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Failed!</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(responseData, null, 2)}</pre>
                        </div>
                    `;
                    log('❌ Basic email test FAILED: ' + JSON.stringify(responseData));
                }

            } catch (error) {
                log('💥 Basic email test ERROR: ' + error.message);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>💥 Error!</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testSecurityAlert() {
            const resultDiv = document.getElementById('security-result');
            resultDiv.innerHTML = '<p>Testing security alert email...</p>';
            
            try {
                log('🚨 Testing security alert email format');
                
                const emailRequest = {
                    config: {
                        provider: 'resend',
                        fromEmail: '<EMAIL>',
                        fromName: 'ClassTasker Security'
                    },
                    params: {
                        to: '<EMAIL>',
                        subject: '🚨 CRITICAL Security Alert - Test Alert',
                        body: `
                            <h2>Critical Security Alert</h2>
                            <p>A critical security event has been detected on ClassTasker:</p>
                            
                            <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
                                <tr style="background-color: #f8f9fa;">
                                    <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Event Type:</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">suspicious_activity</td>
                                </tr>
                                <tr>
                                    <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Severity:</td>
                                    <td style="padding: 10px; border: 1px solid #ddd; color: red; font-weight: bold;">CRITICAL</td>
                                </tr>
                                <tr style="background-color: #f8f9fa;">
                                    <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Action:</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">Test security alert</td>
                                </tr>
                            </table>
                            
                            <p>This is a test of the security alert system.</p>
                        `
                    }
                };

                log('📤 Sending security alert email...');
                log('Request payload: ' + JSON.stringify(emailRequest, null, 2));

                const response = await fetch('https://qcnotlojmyvpqbbgoxbc.supabase.co/functions/v1/send-email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.nZic-dTJpnWJbBKUqZGz5O_9wVhPJhqq8vNJhJqJhJo'
                    },
                    body: JSON.stringify(emailRequest)
                });

                log(`📨 Response status: ${response.status} ${response.statusText}`);

                const responseData = await response.json();
                log('📨 Response data: ' + JSON.stringify(responseData, null, 2));

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Success!</h4>
                            <p>Security alert email sent successfully!</p>
                            <pre>${JSON.stringify(responseData, null, 2)}</pre>
                        </div>
                    `;
                    log('✅ Security alert email test PASSED');
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Failed!</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(responseData, null, 2)}</pre>
                        </div>
                    `;
                    log('❌ Security alert email test FAILED: ' + JSON.stringify(responseData));
                }

            } catch (error) {
                log('💥 Security alert email test ERROR: ' + error.message);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>💥 Error!</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testDebugMode() {
            const resultDiv = document.getElementById('debug-result');
            resultDiv.innerHTML = '<p>Testing debug mode...</p>';
            
            try {
                log('🐛 Testing debug mode (no real email sent)');
                
                const emailRequest = {
                    config: {
                        provider: 'none', // This triggers debug mode
                        fromEmail: '<EMAIL>',
                        fromName: 'ClassTasker Debug'
                    },
                    params: {
                        to: '<EMAIL>',
                        subject: 'Debug Mode Test',
                        body: '<p>This is a debug mode test - no real email should be sent.</p>'
                    }
                };

                log('📤 Sending debug mode request...');
                log('Request payload: ' + JSON.stringify(emailRequest, null, 2));

                const response = await fetch('https://qcnotlojmyvpqbbgoxbc.supabase.co/functions/v1/send-email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.nZic-dTJpnWJbBKUqZGz5O_9wVhPJhqq8vNJhJqJhJo'
                    },
                    body: JSON.stringify(emailRequest)
                });

                log(`📨 Response status: ${response.status} ${response.statusText}`);

                const responseData = await response.json();
                log('📨 Response data: ' + JSON.stringify(responseData, null, 2));

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Success!</h4>
                            <p>Debug mode working correctly!</p>
                            <pre>${JSON.stringify(responseData, null, 2)}</pre>
                        </div>
                    `;
                    log('✅ Debug mode test PASSED');
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Failed!</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(responseData, null, 2)}</pre>
                        </div>
                    `;
                    log('❌ Debug mode test FAILED: ' + JSON.stringify(responseData));
                }

            } catch (error) {
                log('💥 Debug mode test ERROR: ' + error.message);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>💥 Error!</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // Auto-clear logs on page load
        window.onload = function() {
            log('🚀 Send-Email Function Direct Test Page Loaded');
            log('Ready to test the send-email Edge Function directly');
        };
    </script>
</body>
</html>
