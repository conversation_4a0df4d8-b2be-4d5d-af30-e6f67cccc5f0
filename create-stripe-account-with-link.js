// <PERSON>ript to create a Stripe account and generate an onboarding link
import dotenv from 'dotenv';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

// Initialize Stripe with the secret key
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;

if (!stripeSecretKey) {
  console.error('Missing Stripe secret key. Check your environment variables.');
  process.exit(1);
}

// Initialize Stripe with the secret key
const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2025-03-31.basil',
});

// Initialize Supabase client with service role key
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or service role key. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// The user ID to create an account for
const userId = process.argv[2] || '4288cd97-e3ed-4e1d-8d22-abdc0d3f28bd';

async function createStripeAccountWithLink() {
  try {
    console.log(`Creating Stripe account for user ${userId}...`);
    
    // Step 1: Delete any existing accounts for this user
    console.log('Step 1: Deleting any existing accounts...');
    
    const { data: existingAccounts, error: fetchError } = await supabase
      .from('stripe_accounts')
      .select('*')
      .eq('user_id', userId);
    
    if (fetchError) {
      console.error('Error fetching existing accounts:', fetchError);
      process.exit(1);
    }
    
    console.log(`Found ${existingAccounts.length} existing accounts.`);
    
    for (const account of existingAccounts) {
      console.log(`Deleting account ${account.account_id} from the database...`);
      
      const { error: deleteError } = await supabase
        .from('stripe_accounts')
        .delete()
        .eq('id', account.id);
      
      if (deleteError) {
        console.error('Error deleting account from database:', deleteError);
      } else {
        console.log(`Deleted account ${account.account_id} from the database.`);
      }
      
      // Try to delete the account from Stripe as well
      try {
        const deletedAccount = await stripe.accounts.del(account.account_id);
        console.log(`Deleted account ${account.account_id} from Stripe:`, deletedAccount.deleted);
      } catch (stripeError) {
        console.error(`Error deleting account ${account.account_id} from Stripe:`, stripeError.message);
      }
    }
    
    // Step 2: Update the user's profile to remove the Stripe account ID
    console.log('Step 2: Updating user profile...');
    
    const { error: profileError } = await supabase
      .from('profiles')
      .update({ stripe_account_id: null })
      .eq('id', userId);
    
    if (profileError) {
      console.error('Error updating user profile:', profileError);
    } else {
      console.log('Removed Stripe account ID from user profile.');
    }
    
    // Step 3: Create a new account in Stripe
    console.log('Step 3: Creating a new account in Stripe...');
    
    const newAccount = await stripe.accounts.create({
      type: 'express',
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
      business_type: 'individual',
      metadata: {
        user_id: userId,
      },
    });
    
    console.log(`Created new account in Stripe: ${newAccount.id}`);
    
    // Step 4: Store the new account in the database
    console.log('Step 4: Storing the new account in the database...');
    
    const { data: newDbAccount, error: insertError } = await supabase
      .from('stripe_accounts')
      .insert([{
        user_id: userId,
        account_id: newAccount.id,
        account_type: 'express',
        charges_enabled: newAccount.charges_enabled,
        payouts_enabled: newAccount.payouts_enabled,
        account_status: 'pending',
      }])
      .select();
    
    if (insertError) {
      console.error('Error storing new account in database:', insertError);
      process.exit(1);
    }
    
    console.log('Stored new account in database:', newDbAccount[0]);
    
    // Step 5: Update the user's profile with the new Stripe account ID
    console.log('Step 5: Updating user profile with new account ID...');
    
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ stripe_account_id: newAccount.id })
      .eq('id', userId);
    
    if (updateError) {
      console.error('Error updating user profile:', updateError);
    } else {
      console.log(`Updated user profile with new Stripe account ID: ${newAccount.id}`);
    }
    
    // Step 6: Generate an onboarding link
    console.log('Step 6: Generating an onboarding link...');
    
    try {
      const accountLink = await stripe.accountLinks.create({
        account: newAccount.id,
        refresh_url: 'https://example.com/refresh',
        return_url: 'https://example.com/return',
        type: 'account_onboarding',
      });
      
      console.log(`Onboarding link generated: ${accountLink.url}`);
      
      // Open the link in the default browser
      const { exec } = await import('child_process');
      exec(`start ${accountLink.url}`);
      
      console.log('\nStripe account creation completed successfully.');
      console.log(`New account ID: ${newAccount.id}`);
      console.log(`Onboarding link: ${accountLink.url}`);
    } catch (error) {
      console.error('Error generating onboarding link:', error.message);
      
      if (error.message.includes('Livemode requests must always be redirected via HTTPS')) {
        console.error('\nERROR: The API key is in live mode, but the redirect URLs are not HTTPS.');
        console.error('This suggests that the API key being used is a live mode key, even though the environment variable is set to a test mode key.');
        console.error('Check if there are any other environment variables or system variables that might be overriding the one in the .env file.');
      }
    }
  } catch (error) {
    console.error('Error creating Stripe account:', error);
  }
}

// Execute the function
createStripeAccountWithLink();