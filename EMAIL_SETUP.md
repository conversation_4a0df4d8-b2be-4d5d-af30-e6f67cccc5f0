# Email Functionality Setup

This document explains how to set up and deploy the email functionality for Classtasker.

## Overview

Classtasker uses SMTP to send emails for:
- User invitations
- Notifications
- Password resets
- Test emails

The email sending is handled by a Supabase Edge Function, which allows us to use server-side code to send emails securely.

## SMTP Configuration

The default SMTP configuration is:

- **SMTP Host**: server326.web-hosting.com
- **SMTP Port**: 465
- **SMTP Username**: <EMAIL>
- **SMTP Password**: (set in the UI)
- **SSL/TLS**: Enabled

## Deployment Steps

### 1. Deploy the Supabase Edge Function

To deploy the Edge Function that handles email sending:

```bash
# Install Supabase CLI if you haven't already
npm install -g supabase

# Login to Supabase
supabase login

# Deploy the function
./deploy-email-function.sh
```

### 2. Update the API Endpoint

After deploying the Edge Function, update the API endpoint in `src/services/emailSender.ts`:

```typescript
// Replace this URL with your actual Supabase Edge Function URL
response = await fetch('https://your-project-ref.supabase.co/functions/v1/send-email', {
  // ...
});
```

### 3. Test the Email Functionality

1. Go to the Email Configuration page (`/email-config`)
2. Enter your SMTP password
3. Click "Save and Test" to verify the configuration

## Troubleshooting

### Common Issues

1. **Connection Refused**:
   - Check if the SMTP host is correct
   - Verify the port number (common ports: 25, 465, 587)
   - Make sure your network allows outgoing connections on that port

2. **Authentication Failed**:
   - Double-check your username and password
   - For Gmail, you may need to use an App Password instead of your regular password
   - Make sure your account has SMTP access enabled

3. **SSL/TLS Issues**:
   - Try toggling the SSL/TLS option
   - Port 465 typically requires SSL enabled
   - Port 587 typically uses STARTTLS (SSL should be disabled)

### Debugging

To enable detailed logging:

1. Go to the Supabase dashboard
2. Navigate to Edge Functions
3. Select the `send-email` function
4. Click on "Logs" to see detailed execution logs

## Local Development

For local development, the system will simulate email sending. You'll see success messages, but no actual emails will be sent.

To test with real emails:

1. Deploy the Edge Function as described above
2. Update the API endpoint in `src/services/emailSender.ts`
3. Use the Email Configuration page to set up your SMTP credentials
