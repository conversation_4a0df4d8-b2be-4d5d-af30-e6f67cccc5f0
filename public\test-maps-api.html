<!DOCTYPE html>
<html>
<head>
  <title>Google Maps API Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }
    h1 {
      color: #333;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
    }
    .controls {
      margin: 20px 0;
      padding: 10px;
      background: #f5f5f5;
      border: 1px solid #ddd;
    }
    .map-container {
      height: 400px;
      width: 100%;
      border: 1px solid #ddd;
      margin-bottom: 20px;
    }
    .log-container {
      margin-top: 20px;
      padding: 10px;
      background: #f5f5f5;
      border: 1px solid #ddd;
      max-height: 200px;
      overflow-y: auto;
    }
    .log-entry {
      margin: 5px 0;
      padding: 5px;
      border-bottom: 1px solid #eee;
    }
    .error {
      color: red;
    }
    .success {
      color: green;
    }
    button {
      padding: 8px 16px;
      margin-right: 10px;
      background: #4285f4;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background: #3367d6;
    }
    input {
      padding: 8px;
      margin-right: 10px;
      width: 300px;
    }
  </style>
  
  <!-- Environment Variables -->
  <script>
    window.env = {
      VITE_GOOGLE_MAPS_API_KEY: "AIzaSyADP5PrGRFA7BDgenK26HrU66VITsHWL58"
    };
  </script>
</head>
<body>
  <div class="container">
    <h1>Google Maps API Test</h1>
    
    <div class="controls">
      <label for="api-key">API Key:</label>
      <input type="text" id="api-key" placeholder="Enter Google Maps API Key">
      <button id="set-key">Set Key</button>
    </div>
    
    <div class="controls">
      <button id="test-loaded">Test If Loaded</button>
      <button id="test-load-api">Load API</button>
      <button id="test-geocode">Test Geocoding</button>
      <button id="test-create-map">Create Map</button>
      <button id="run-all-tests">Run All Tests</button>
    </div>
    
    <div id="map-container" class="map-container"></div>
    
    <div class="log-container">
      <h3>Log</h3>
      <div id="log"></div>
    </div>
  </div>
  
  <script>
    // Log function
    function log(message, isError = false) {
      const logElement = document.getElementById('log');
      const entry = document.createElement('div');
      entry.className = `log-entry ${isError ? 'error' : 'success'}`;
      entry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
      logElement.appendChild(entry);
      console.log(message);
    }
    
    // Get API key
    function getApiKey() {
      const inputKey = document.getElementById('api-key').value;
      return inputKey || window.env.VITE_GOOGLE_MAPS_API_KEY;
    }
    
    // Set up event listeners
    document.getElementById('set-key').addEventListener('click', function() {
      const key = document.getElementById('api-key').value;
      if (key) {
        window.env.VITE_GOOGLE_MAPS_API_KEY = key;
        log(`API key set: ${key.substring(0, 5)}...`);
      } else {
        log('Please enter an API key', true);
      }
    });
    
    document.getElementById('test-loaded').addEventListener('click', function() {
      log('Testing if Google Maps API is loaded...');
      if (window.google && window.google.maps) {
        log('Google Maps API is loaded');
      } else {
        log('Google Maps API is not loaded', true);
      }
    });
    
    document.getElementById('test-load-api').addEventListener('click', function() {
      const apiKey = getApiKey();
      log(`Loading Google Maps API with key: ${apiKey.substring(0, 5)}...`);
      
      // Create a callback function
      const callbackName = 'googleMapsLoaded' + Date.now();
      window[callbackName] = function() {
        log('Google Maps API loaded successfully');
        delete window[callbackName];
      };
      
      // Create script element
      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=${callbackName}`;
      script.async = true;
      script.defer = true;
      script.onerror = function() {
        log('Failed to load Google Maps API', true);
      };
      
      document.head.appendChild(script);
    });
    
    document.getElementById('test-geocode').addEventListener('click', function() {
      const apiKey = getApiKey();
      const location = 'Woking, UK';
      
      log(`Testing geocoding for location: ${location}`);
      
      fetch(`https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(location)}&key=${apiKey}`)
        .then(response => response.json())
        .then(data => {
          log(`Geocoding response status: ${data.status}`);
          
          if (data.status === 'OK') {
            const coords = data.results[0].geometry.location;
            log(`Successfully geocoded "${location}": ${JSON.stringify(coords)}`);
          } else {
            log(`Failed to geocode "${location}": ${data.status}`, true);
          }
        })
        .catch(error => {
          log(`Error geocoding "${location}": ${error}`, true);
        });
    });
    
    document.getElementById('test-create-map').addEventListener('click', function() {
      if (!window.google || !window.google.maps) {
        log('Google Maps API is not loaded. Please load the API first.', true);
        return;
      }
      
      log('Creating map...');
      
      try {
        const mapContainer = document.getElementById('map-container');
        const map = new google.maps.Map(mapContainer, {
          center: { lat: 51.5074, lng: -0.1278 }, // London
          zoom: 8
        });
        
        log('Map created successfully');
        
        // Add a marker
        new google.maps.Marker({
          position: { lat: 51.5074, lng: -0.1278 },
          map: map,
          title: 'London'
        });
      } catch (error) {
        log(`Error creating map: ${error}`, true);
      }
    });
    
    document.getElementById('run-all-tests').addEventListener('click', function() {
      const apiKey = getApiKey();
      
      log('Running all tests...');
      
      // First check if API is loaded
      if (window.google && window.google.maps) {
        log('Google Maps API is already loaded');
        runGeocodeAndMapTests(apiKey);
      } else {
        log('Google Maps API is not loaded, loading now...');
        
        // Create a callback function
        const callbackName = 'googleMapsLoaded' + Date.now();
        window[callbackName] = function() {
          log('Google Maps API loaded successfully');
          delete window[callbackName];
          runGeocodeAndMapTests(apiKey);
        };
        
        // Create script element
        const script = document.createElement('script');
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=${callbackName}`;
        script.async = true;
        script.defer = true;
        script.onerror = function() {
          log('Failed to load Google Maps API', true);
        };
        
        document.head.appendChild(script);
      }
    });
    
    function runGeocodeAndMapTests(apiKey) {
      const location = 'Woking, UK';
      
      log(`Testing geocoding for location: ${location}`);
      
      fetch(`https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(location)}&key=${apiKey}`)
        .then(response => response.json())
        .then(data => {
          log(`Geocoding response status: ${data.status}`);
          
          if (data.status === 'OK') {
            const coords = data.results[0].geometry.location;
            log(`Successfully geocoded "${location}": ${JSON.stringify(coords)}`);
            
            // Create map with the geocoded coordinates
            createMapWithCoords(coords);
          } else {
            log(`Failed to geocode "${location}": ${data.status}`, true);
          }
        })
        .catch(error => {
          log(`Error geocoding "${location}": ${error}`, true);
        });
    }
    
    function createMapWithCoords(coords) {
      log(`Creating map with coordinates: ${JSON.stringify(coords)}`);
      
      try {
        const mapContainer = document.getElementById('map-container');
        const map = new google.maps.Map(mapContainer, {
          center: coords,
          zoom: 12
        });
        
        log('Map created successfully');
        
        // Add a marker at the coordinates
        new google.maps.Marker({
          position: coords,
          map: map,
          title: 'Location'
        });
      } catch (error) {
        log(`Error creating map: ${error}`, true);
      }
    }
    
    // Auto-fill API key from environment
    window.onload = function() {
      const apiKey = window.env.VITE_GOOGLE_MAPS_API_KEY;
      if (apiKey) {
        document.getElementById('api-key').value = apiKey;
        log(`API key loaded from environment: ${apiKey.substring(0, 5)}...`);
      }
    };
  </script>
</body>
</html>