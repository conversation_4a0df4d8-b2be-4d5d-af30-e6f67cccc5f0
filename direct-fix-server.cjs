// Direct fix for the server-side payment method options
const fs = require('fs');
const path = require('path');

// Path to the server file
const serverPath = path.join(__dirname, 'server', 'api', 'stripe-connect.js');
const backupPath = path.join(__dirname, 'server', 'api', 'stripe-connect.js.direct-fix');

// Create a backup
console.log('Creating backup of stripe-connect.js...');
fs.copyFileSync(serverPath, backupPath);
console.log(`Backup created at ${backupPath}`);

// Read the file content
console.log('Reading stripe-connect.js...');
let content = fs.readFileSync(serverPath, 'utf8');

// Completely remove the payment_method_options section
console.log('Removing payment_method_options from server code...');

// Find the section where payment method options are configured
const paymentMethodOptionsSection = content.match(/\/\/ Configure payment method options[\s\S]*?const paymentIntentParams/);

if (paymentMethodOptionsSection) {
  // Extract the section
  const section = paymentMethodOptionsSection[0];
  
  // Create a replacement that skips all the payment method options
  const replacement = `// Configure payment method options
    // Removed all setup_future_usage options as they were causing API errors
    const paymentMethodOptions = {};
    
    // Create the payment intent with payment method options
    const paymentIntentParams`;
  
  // Replace the section
  content = content.replace(section, replacement);
  
  // Write the modified content back to the file
  fs.writeFileSync(serverPath, content, 'utf8');
  console.log('Server-side fix applied successfully!');
  
  console.log('\nChanges made:');
  console.log('- Completely removed all payment method options configuration that was causing errors');
  
  console.log('\nPlease restart the server for the changes to take effect.');
} else {
  console.log('Could not find the payment method options section in the file.');
  console.log('Please apply the fix manually:');
  console.log('1. Open server/api/stripe-connect.js');
  console.log('2. Find the section that configures payment_method_options');
  console.log('3. Replace it with an empty object: const paymentMethodOptions = {};');
}

console.log('\nIf you need to restore the original file, run:');
console.log(`cp ${backupPath} ${serverPath}`);