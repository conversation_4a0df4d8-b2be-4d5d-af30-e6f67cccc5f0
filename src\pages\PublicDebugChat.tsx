import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Loader2, Bug, ArrowRight, RefreshCw } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import DebugChatTest from '@/components/debug/DebugChatTest';

interface Task {
  id: string;
  title: string;
  status: string;
  user_id: string;
}

interface User {
  id: string;
  email: string;
  role: string;
}

const PublicDebugChatPage = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [schoolUsers, setSchoolUsers] = useState<User[]>([]);
  const [supplierUsers, setSupplierUsers] = useState<User[]>([]);
  const [selectedTask, setSelectedTask] = useState<string>('');
  const [selectedSchoolUser, setSelectedSchoolUser] = useState<string>('');
  const [selectedSupplierUser, setSelectedSupplierUser] = useState<string>('');
  const [customTaskId, setCustomTaskId] = useState<string>('');
  const [customSchoolId, setCustomSchoolId] = useState<string>('');
  const [customSupplierId, setCustomSupplierId] = useState<string>('');
  const [useCustomIds, setUseCustomIds] = useState(false);

  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();

  // Fetch data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setIsLoading(true);
    try {
      console.log("Fetching data for debug chat test...");

      // Fetch tasks
      const { data: tasksData, error: tasksError } = await supabase
        .from('tasks')
        .select('id, title, status, user_id')
        .order('created_at', { ascending: false })
        .limit(10);

      if (tasksError) {
        console.error('Error fetching tasks:', tasksError);
        toast({
          variant: "destructive",
          title: "Error fetching tasks",
          description: tasksError.message,
        });
      } else {
        console.log(`Fetched ${tasksData?.length || 0} tasks`);
        setTasks(tasksData || []);
        if (tasksData && tasksData.length > 0) {
          setSelectedTask(tasksData[0].id);
        }
      }

      // Fetch school users
      console.log('Fetching school users...');
      const { data: schoolData, error: schoolError } = await supabase
        .from('profiles')
        .select('id, email, role, account_type')
        .limit(20);

      if (schoolError) {
        console.error('Error fetching school users:', schoolError);
      } else {
        console.log('All profiles fetched:', schoolData);

        // Filter for school users
        const schoolUsers = schoolData?.filter(user => user.account_type === 'school') || [];
        console.log(`Filtered ${schoolUsers.length} school users from ${schoolData?.length || 0} profiles`);

        setSchoolUsers(schoolUsers);
        if (schoolUsers.length > 0) {
          setSelectedSchoolUser(schoolUsers[0].id);
        } else {
          console.warn('No school users found in the database');
        }
      }

      // Fetch supplier users
      console.log('Fetching supplier users...');
      const { data: supplierData, error: supplierError } = await supabase
        .from('profiles')
        .select('id, email, role, account_type')
        .limit(20);

      if (supplierError) {
        console.error('Error fetching supplier users:', supplierError);
      } else {
        // Filter for supplier users
        const supplierUsers = supplierData?.filter(user => user.account_type === 'supplier') || [];
        console.log(`Filtered ${supplierUsers.length} supplier users from ${supplierData?.length || 0} profiles`);

        setSupplierUsers(supplierUsers);
        if (supplierUsers.length > 0) {
          setSelectedSupplierUser(supplierUsers[0].id);
        } else {
          console.warn('No supplier users found in the database');
        }
      }
    } catch (error) {
      console.error('Unexpected error:', error);
      toast({
        variant: "destructive",
        title: "Error loading data",
        description: "An unexpected error occurred while loading data.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const refreshData = () => {
    // Re-fetch all data
    fetchData();
  };

  const getTaskOwner = (taskId: string) => {
    const task = tasks.find(t => t.id === taskId);
    return task ? task.user_id : '';
  };

  const getEffectiveTaskId = () => {
    return useCustomIds ? customTaskId : selectedTask;
  };

  const getEffectiveSchoolId = () => {
    return useCustomIds ? customSchoolId : selectedSchoolUser;
  };

  const getEffectiveSupplierUserId = () => {
    return useCustomIds ? customSupplierId : selectedSupplierUser;
  };

  const viewTask = () => {
    const taskId = getEffectiveTaskId();
    if (taskId) {
      navigate(`/tasks/${taskId}?messages=true`);
    }
  };

  return (
    <MainLayout>
      <div className="container mx-auto py-6 px-4">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold flex items-center">
            <Bug className="mr-2 h-6 w-6" /> Public Debug Chat Test
          </h1>
          <Button variant="outline" onClick={refreshData}>
            <RefreshCw className="mr-2 h-4 w-4" /> Refresh Data
          </Button>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          </div>
        ) : (
          <div className="space-y-6">
            {!user && (
              <Card className="bg-amber-50 border-amber-200">
                <CardContent className="p-4">
                  <p className="text-amber-800">
                    <strong>Note:</strong> You are not currently logged in. Some features may be limited.
                  </p>
                </CardContent>
              </Card>
            )}

            <Card>
              <CardHeader>
                <CardTitle>Select Test Parameters</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Label htmlFor="useCustomIds" className="flex items-center cursor-pointer">
                      <Input
                        id="useCustomIds"
                        type="checkbox"
                        className="mr-2 h-4 w-4"
                        checked={useCustomIds}
                        onChange={(e) => setUseCustomIds(e.target.checked)}
                      />
                      Use custom IDs
                    </Label>
                  </div>

                  {useCustomIds ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="customTaskId">Custom Task ID</Label>
                          <Input
                            id="customTaskId"
                            value={customTaskId}
                            onChange={(e) => setCustomTaskId(e.target.value)}
                            placeholder="Enter task UUID"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="customSchoolId">Custom School User ID</Label>
                          <Input
                            id="customSchoolId"
                            value={customSchoolId}
                            onChange={(e) => setCustomSchoolId(e.target.value)}
                            placeholder="Enter school user UUID"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="customSupplierId">Custom Supplier User ID</Label>
                          <Input
                            id="customSupplierId"
                            value={customSupplierId}
                            onChange={(e) => setCustomSupplierId(e.target.value)}
                            placeholder="Enter supplier user UUID"
                          />
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="taskSelect">Select Task</Label>
                          <Select value={selectedTask} onValueChange={setSelectedTask}>
                            <SelectTrigger id="taskSelect">
                              <SelectValue placeholder="Select a task" />
                            </SelectTrigger>
                            <SelectContent>
                              {tasks.map((task) => (
                                <SelectItem key={task.id} value={task.id}>
                                  {task.title} ({task.status})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="schoolSelect">Select School User</Label>
                          <Select value={selectedSchoolUser} onValueChange={setSelectedSchoolUser}>
                            <SelectTrigger id="schoolSelect">
                              <SelectValue placeholder="Select a school user" />
                            </SelectTrigger>
                            <SelectContent>
                              {schoolUsers.map((user) => (
                                <SelectItem key={user.id} value={user.id}>
                                  {user.email}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="supplierSelect">Select Supplier User</Label>
                          <Select value={selectedSupplierUser} onValueChange={setSelectedSupplierUser}>
                            <SelectTrigger id="supplierSelect">
                              <SelectValue placeholder="Select a supplier user" />
                            </SelectTrigger>
                            <SelectContent>
                              {supplierUsers.map((user) => (
                                <SelectItem key={user.id} value={user.id}>
                                  {user.email}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>
                  )}

                  <Separator />

                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm font-medium">
                        Selected Task ID: <span className="font-mono">{getEffectiveTaskId() || 'None'}</span>
                      </p>
                      <p className="text-sm font-medium">
                        School User ID: <span className="font-mono">{getEffectiveSchoolId() || 'None'}</span>
                      </p>
                      <p className="text-sm font-medium">
                        Supplier User ID: <span className="font-mono">{getEffectiveSupplierUserId() || 'None'}</span>
                      </p>
                    </div>
                    <Button onClick={viewTask} disabled={!getEffectiveTaskId()}>
                      View Task <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {getEffectiveSchoolId() && getEffectiveSupplierUserId() && (
              <DebugChatTest
                taskId={getEffectiveTaskId()}
                schoolUserId={getEffectiveSchoolId()}
                supplierUserId={getEffectiveSupplierUserId()}
                allowDirectMessages={true}
              />
            )}
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default PublicDebugChatPage;
