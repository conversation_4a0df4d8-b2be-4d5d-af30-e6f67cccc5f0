import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from '@/hooks/use-toast';
import { debugAcceptOffer } from '@/utils/debug-offer-acceptance';
import { Link } from 'react-router-dom';

// Define types for our data
type Task = {
  id: string;
  title: string;
  description: string;
  budget: number;
  location: string;
  category: string;
  status: string;
  due_date: string;
  created_at: string;
  offers_count: number;
  user_id: string;
};

type Offer = {
  id: string;
  task_id: string;
  user_id: string;
  amount: number;
  message: string;
  status: string; // 'pending', 'accepted', 'rejected'
  created_at: string;
};

type StatusCount = {
  status: string;
  count: number;
};

/**
 * Public Debug page that doesn't require authentication
 */
const PublicDebug = () => {
  const [connectionStatus, setConnectionStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [taskCount, setTaskCount] = useState<number | null>(null);
  const [sampleTasks, setSampleTasks] = useState<Task[]>([]);
  const [taskStatusCounts, setTaskStatusCounts] = useState<StatusCount[]>([]);
  const [offers, setOffers] = useState<Offer[]>([]);
  const [offerStatusCounts, setOfferStatusCounts] = useState<StatusCount[]>([]);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Debug offer acceptance
  const [debugTaskId, setDebugTaskId] = useState('');
  const [debugOfferId, setDebugOfferId] = useState('');
  const [isTestingAcceptance, setIsTestingAcceptance] = useState(false);
  const [acceptanceResult, setAcceptanceResult] = useState<any>(null);

  const testConnection = async () => {
    setConnectionStatus('loading');
    setErrorMessage(null);

    try {
      // Test a simple query - just get the count
      const { count, error } = await supabase
        .from('tasks')
        .select('*', { count: 'exact', head: true });

      if (error) {
        console.error('Error connecting to Supabase:', error);
        setConnectionStatus('error');
        setErrorMessage(error.message);
        return;
      }

      setTaskCount(count);
      setConnectionStatus('success');

      // Fetch tasks with all fields
      const { data: tasksData, error: tasksError } = await supabase
        .from('tasks')
        .select('*')
        .order('created_at', { ascending: false });

      if (tasksError) {
        console.error('Error fetching tasks:', tasksError);
        setErrorMessage(tasksError.message);
        return;
      }

      setSampleTasks(tasksData || []);

      // Get task status counts
      const taskStatusMap = {};
      tasksData?.forEach(task => {
        taskStatusMap[task.status] = (taskStatusMap[task.status] || 0) + 1;
      });

      const statusCounts = Object.entries(taskStatusMap).map(([status, count]) => ({
        status,
        count: count as number
      }));

      setTaskStatusCounts(statusCounts);

      // Fetch offers data
      const { data: offersData, error: offersError } = await supabase
        .from('offers')
        .select('*')
        .order('created_at', { ascending: false });

      if (offersError) {
        console.error('Error fetching offers:', offersError);
        // Don't set error message here, just log it
      } else {
        setOffers(offersData || []);

        // Get offer status counts
        const offerStatusMap = {};
        offersData?.forEach(offer => {
          offerStatusMap[offer.status] = (offerStatusMap[offer.status] || 0) + 1;
        });

        const offerStatusCounts = Object.entries(offerStatusMap).map(([status, count]) => ({
          status,
          count: count as number
        }));

        setOfferStatusCounts(offerStatusCounts);
      }
    } catch (err: any) {
      console.error('Unexpected error:', err);
      setConnectionStatus('error');
      setErrorMessage(err.message || 'An unexpected error occurred');
    }
  };

  // Function to test offer acceptance
  const testOfferAcceptance = async () => {
    if (!debugTaskId || !debugOfferId) {
      toast({
        variant: "destructive",
        title: "Missing information",
        description: "Please provide both Task ID and Offer ID"
      });
      return;
    }

    setIsTestingAcceptance(true);
    setAcceptanceResult(null);

    try {
      const result = await debugAcceptOffer(debugTaskId, debugOfferId);
      setAcceptanceResult(result);

      if (result.success) {
        toast({
          title: "Test successful",
          description: "Offer acceptance test completed successfully"
        });
      } else {
        toast({
          variant: "destructive",
          title: "Test failed",
          description: result.message || "Offer acceptance test failed"
        });
      }

      // Refresh data after test
      testConnection();
    } catch (error) {
      console.error("Error in testOfferAcceptance:", error);
      setAcceptanceResult({ success: false, error });
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred during the test"
      });
    } finally {
      setIsTestingAcceptance(false);
    }
  };

  useEffect(() => {
    testConnection();
  }, []);

  // Helper function to render status badge
  const renderStatusBadge = (status) => {
    let color = "";
    switch (status) {
      case 'open':
        color = "bg-blue-100 text-blue-800";
        break;
      case 'in_progress':
        color = "bg-yellow-100 text-yellow-800";
        break;
      case 'completed':
        color = "bg-green-100 text-green-800";
        break;
      case 'cancelled':
        color = "bg-red-100 text-red-800";
        break;
      case 'accepted':
        color = "bg-green-100 text-green-800";
        break;
      case 'rejected':
        color = "bg-red-100 text-red-800";
        break;
      case 'pending':
        color = "bg-yellow-100 text-yellow-800";
        break;
      case 'awaiting':
        color = "bg-yellow-100 text-yellow-800";
        break;
      case 'assigned':
        color = "bg-blue-100 text-blue-800";
        break;
      default:
        color = "bg-gray-100 text-gray-800";
    }
    return <Badge className={color}>{status}</Badge>;
  };

  // Format date helper
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Simple header */}
      <header className="bg-white shadow-sm py-4">
        <div className="container mx-auto px-4 flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <div className="w-9 h-9 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold text-xl">C</div>
            <span className="text-xl font-bold">Classtasker Debug</span>
          </div>
          <Link to="/" className="text-blue-600 hover:underline">
            Back to Home
          </Link>
        </div>
      </header>

      <div className="container mx-auto py-8 px-4">
        <h1 className="text-2xl font-bold mb-6">Database Debug Tools</h1>

        {/* Connection Status Card */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Supabase Connection Status</CardTitle>
          </CardHeader>
          <CardContent>
            {connectionStatus === 'loading' && (
              <div className="text-blue-500">Testing connection...</div>
            )}

            {connectionStatus === 'success' && (
              <div className="space-y-4">
                <div className="text-green-500 font-medium">✓ Connection successful!</div>

                {taskCount !== null && (
                  <div>Total tasks in database: {taskCount}</div>
                )}
              </div>
            )}

            {connectionStatus === 'error' && (
              <div className="space-y-2">
                <div className="text-red-500 font-medium">✗ Connection failed</div>
                {errorMessage && (
                  <div className="text-sm bg-red-50 p-2 rounded border border-red-200">
                    {errorMessage}
                  </div>
                )}
              </div>
            )}

            <Button
              onClick={testConnection}
              className="mt-4"
              disabled={connectionStatus === 'loading'}
            >
              Test Connection Again
            </Button>
          </CardContent>
        </Card>

        {/* Debug Offer Acceptance */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Debug Offer Acceptance</CardTitle>
            <CardDescription>Test accepting an offer directly</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div className="space-y-2">
                <Label htmlFor="debug-task-id">Task ID</Label>
                <Input
                  id="debug-task-id"
                  value={debugTaskId}
                  onChange={(e) => setDebugTaskId(e.target.value)}
                  placeholder="Enter task ID"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="debug-offer-id">Offer ID</Label>
                <Input
                  id="debug-offer-id"
                  value={debugOfferId}
                  onChange={(e) => setDebugOfferId(e.target.value)}
                  placeholder="Enter offer ID"
                />
              </div>
            </div>

            <Button
              onClick={testOfferAcceptance}
              disabled={isTestingAcceptance || !debugTaskId || !debugOfferId}
              className="mb-4"
            >
              {isTestingAcceptance ? 'Testing...' : 'Test Offer Acceptance'}
            </Button>

            {acceptanceResult && (
              <div className="mt-4 p-4 border rounded bg-slate-50 overflow-auto max-h-60">
                <h3 className="font-medium mb-2">
                  {acceptanceResult.success ? (
                    <span className="text-green-600">Success</span>
                  ) : (
                    <span className="text-red-600">Failed at step {acceptanceResult.step}</span>
                  )}
                </h3>
                <pre className="text-xs whitespace-pre-wrap">
                  {JSON.stringify(acceptanceResult, null, 2)}
                </pre>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Data Tabs */}
        {connectionStatus === 'success' && (
          <Tabs defaultValue="tasks" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="tasks">Tasks</TabsTrigger>
              <TabsTrigger value="offers">Offers</TabsTrigger>
              <TabsTrigger value="status">Status Counts</TabsTrigger>
            </TabsList>

            {/* Tasks Tab */}
            <TabsContent value="tasks" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Tasks</CardTitle>
                  <CardDescription>All tasks in the database ({sampleTasks.length})</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="rounded-md border overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[100px]">ID</TableHead>
                          <TableHead>Title</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Budget</TableHead>
                          <TableHead>Due Date</TableHead>
                          <TableHead>Created</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {sampleTasks.map((task) => (
                          <TableRow key={task.id}>
                            <TableCell className="font-mono text-xs">
                              <div className="flex items-center gap-2">
                                <span>{task.id.substring(0, 8)}...</span>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 px-2 text-xs"
                                  onClick={() => setDebugTaskId(task.id)}
                                >
                                  Use
                                </Button>
                              </div>
                            </TableCell>
                            <TableCell>{task.title}</TableCell>
                            <TableCell>{renderStatusBadge(task.status)}</TableCell>
                            <TableCell>${task.budget}</TableCell>
                            <TableCell>{formatDate(task.due_date)}</TableCell>
                            <TableCell>{formatDate(task.created_at)}</TableCell>
                          </TableRow>
                        ))}
                        {sampleTasks.length === 0 && (
                          <TableRow>
                            <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                              No tasks found
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Offers Tab */}
            <TabsContent value="offers" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Offers</CardTitle>
                  <CardDescription>All offers in the database ({offers.length})</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="rounded-md border overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[100px]">ID</TableHead>
                          <TableHead>Task ID</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Created</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {offers.map((offer) => (
                          <TableRow key={offer.id}>
                            <TableCell className="font-mono text-xs">
                              <div className="flex items-center gap-2">
                                <span>{offer.id.substring(0, 8)}...</span>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 px-2 text-xs"
                                  onClick={() => setDebugOfferId(offer.id)}
                                >
                                  Use
                                </Button>
                              </div>
                            </TableCell>
                            <TableCell className="font-mono text-xs">
                              <div className="flex items-center gap-2">
                                <span>{offer.task_id.substring(0, 8)}...</span>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 px-2 text-xs"
                                  onClick={() => setDebugTaskId(offer.task_id)}
                                >
                                  Use
                                </Button>
                              </div>
                            </TableCell>
                            <TableCell>${offer.amount}</TableCell>
                            <TableCell>{renderStatusBadge(offer.status)}</TableCell>
                            <TableCell>{formatDate(offer.created_at)}</TableCell>
                          </TableRow>
                        ))}
                        {offers.length === 0 && (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center py-4 text-muted-foreground">
                              No offers found
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Status Counts Tab */}
            <TabsContent value="status" className="mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Task Status Counts */}
                <Card>
                  <CardHeader>
                    <CardTitle>Task Status Counts</CardTitle>
                    <CardDescription>Distribution of task statuses</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="rounded-md border overflow-hidden">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Status</TableHead>
                            <TableHead>Count</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {taskStatusCounts.map((item) => (
                            <TableRow key={item.status}>
                              <TableCell>{renderStatusBadge(item.status)}</TableCell>
                              <TableCell>{item.count}</TableCell>
                            </TableRow>
                          ))}
                          {taskStatusCounts.length === 0 && (
                            <TableRow>
                              <TableCell colSpan={2} className="text-center py-4 text-muted-foreground">
                                No task status data
                              </TableCell>
                            </TableRow>
                          )}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>

                {/* Offer Status Counts */}
                <Card>
                  <CardHeader>
                    <CardTitle>Offer Status Counts</CardTitle>
                    <CardDescription>Distribution of offer statuses</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="rounded-md border overflow-hidden">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Status</TableHead>
                            <TableHead>Count</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {offerStatusCounts.map((item) => (
                            <TableRow key={item.status}>
                              <TableCell>{renderStatusBadge(item.status)}</TableCell>
                              <TableCell>{item.count}</TableCell>
                            </TableRow>
                          ))}
                          {offerStatusCounts.length === 0 && (
                            <TableRow>
                              <TableCell colSpan={2} className="text-center py-4 text-muted-foreground">
                                No offer status data
                              </TableCell>
                            </TableRow>
                          )}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </div>
  );
};

export default PublicDebug;
