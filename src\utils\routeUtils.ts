/**
 * Utility functions for route handling
 */

/**
 * List of routes that should bypass authentication redirects
 */
export const BYPASS_AUTH_REDIRECT_ROUTES = [


  // Admin routes
  '/set-admin-role',
  '/organization/setup',
  '/organization/users',
  '/admin/users',
  '/admin/tasks',
  '/invitation/accept',
  '/email-config',
  '/test-email',

  // Explorer routes
  '/route-explorer',

  // Main routes - allow normal navigation
  '/tasks',
  '/post-task',
  '/messages',
  '/payments',
  '/how-it-works',
  '/help',
  '/contact',
  '/profile',

  // Add the root path to prevent redirection from home page
  '/'
];

/**
 * Check if the current path should bypass authentication redirects
 * @param path Current path
 * @returns Boolean indicating if the path should bypass redirects
 */
export const shouldBypassAuthRedirect = (path: string): boolean => {
  // First check for exact matches
  if (BYPASS_AUTH_REDIRECT_ROUTES.includes(path)) {
    return true;
  }

  // Then check for path patterns (e.g., /tasks/123 should match /tasks)
  return BYPASS_AUTH_REDIRECT_ROUTES.some(route => {
    // Special case for profile paths like /profile/123
    if (route === '/profile' && path.startsWith('/profile/')) {
      return true;
    }

    // Special case for task paths like /tasks/123
    if (route === '/tasks' && path.startsWith('/tasks/')) {
      return true;
    }

    return path.startsWith(route);
  });
};
