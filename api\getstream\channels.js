/**
 * GetStream Channels API Route
 *
 * This API route creates and manages GetStream channels.
 * It's designed to work as a serverless function on Vercel.
 */

import { StreamChat } from 'stream-chat';

// Load environment variables
// For Vercel deployment, use GETSTREAM_API_KEY and GETSTREAM_API_SECRET
// For local development, use VITE_GETSTREAM_API_KEY and VITE_GETSTREAM_API_SECRET
const apiKey = process.env.GETSTREAM_API_KEY || process.env.VITE_GETSTREAM_API_KEY;
const apiSecret = process.env.GETSTREAM_API_SECRET || process.env.VITE_GETSTREAM_API_SECRET;

// Log environment variables for debugging
console.log('API Key available:', !!apiKey);
console.log('API Secret available:', !!apiSecret);

// Create a server-side client for GetStream
let serverClient;

// Initialize the server client if API key and secret are available
if (apiKey && apiSecret) {
  serverClient = StreamChat.getInstance(apiKey, apiSecret);
}

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version'
  );

  // Handle OPTIONS request (preflight)
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check if API key and secret are available
    if (!apiKey || !apiSecret || !serverClient) {
      console.error('Error: GetStream API key or secret is missing in environment variables.');
      return res.status(500).json({ error: 'Server configuration error' });
    }

    // Get the task ID, title, and members from the request body
    const { taskId, taskTitle, members } = req.body;

    if (!taskId || !taskTitle) {
      return res.status(400).json({ error: 'Task ID and title are required' });
    }

    console.log('Creating channel for task:', taskId);

    // Create a channel
    const channelId = `task-${taskId}`;

    // Use provided members or create an empty array
    const channelMembers = members && Array.isArray(members) ? members : [];

    // Create users if they don't exist
    if (channelMembers.length > 0) {
      try {
        // Create user objects for each member
        const userObjects = channelMembers.map(userId => ({
          id: userId,
          name: userId,
          role: 'user'
        }));

        // Upsert users to ensure they exist
        await serverClient.upsertUsers(userObjects);
        console.log(`Created ${userObjects.length} users for channel`);
      } catch (error) {
        console.error('Error creating users:', error);
        // Continue even if user creation fails
      }
    }

    try {
      // First try to get the channel if it exists
      const channel = serverClient.channel('messaging', channelId);
      await channel.query();

      // If channel exists, update members
      if (channelMembers.length > 0) {
        // Add members to the channel
        await channel.addMembers(channelMembers);
        console.log(`Added ${channelMembers.length} members to existing channel:`, channelId);
      }

      console.log('Channel already exists:', channelId);

      return res.status(200).json({
        channelId,
        channel: channel.id,
        members: channel.state.members,
        status: 'updated'
      });
    } catch (error) {
      // If channel doesn't exist, create it
      console.log('Channel does not exist, creating new channel');

      // Create a new channel
      const channel = serverClient.channel('messaging', channelId, {
        name: taskTitle,
        members: channelMembers,
        task_id: taskId,
        created_by_id: 'system' // Required for server-side auth
      });

      await channel.create();

      console.log('Channel created successfully:', channelId);

      return res.status(200).json({
        channelId,
        channel: channel.id,
        members: channel.state.members,
        status: 'created'
      });
    }
  } catch (error) {
    console.error('Error creating/updating channel:', error);
    return res.status(500).json({ error: 'Failed to create/update channel' });
  }
}
