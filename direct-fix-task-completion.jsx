// Direct fix for FixedTask.tsx
// Find line 487 and replace:
// {isTaskOwner && (
// With:
// {(isTaskOwner || isAdmin) && (

// The complete section should look like this:
/*
            {/* Task Completion Actions - Shown to task owners and admins for assigned tasks */}
            {(isTaskOwner || isAdmin) && (
              <>
                {/* Debug info */}
                {console.log('Task Completion Debug:', {
                  isTaskOwner,
                  isAdmin,
                  canManageCompletion: isTaskOwner || isAdmin,
                  taskStatus: task?.status,
                  hasAcceptedOffer: !!offers?.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted'),
                  acceptedOffer: offers?.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted'),
                  offers: offers?.map(o => ({ id: o.id, status: o.status, normalizedStatus: normalizeStatus(o.status) }))
                })}

                <TaskCompletionActions
                  task={task}
                  acceptedOffer={offers?.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted') || null}
                  onTaskUpdated={() => queryClient.invalidateQueries({ queryKey: ['task', id] })}
                />

                <TaskPaymentActions
                  task={task}
                  acceptedOffer={offers?.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted') || null}
                  onTaskUpdated={() => queryClient.invalidateQueries({ queryKey: ['task', id] })}
                />
              </>
            )}
*/

// To apply this fix, manually edit src/pages/FixedTask.tsx
// or run the apply-task-completion-fix.cjs script again