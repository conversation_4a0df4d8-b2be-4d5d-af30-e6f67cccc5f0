<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ClassTasker Debug</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2.38.4/dist/umd/supabase.min.js"></script>
  <style>
    .badge {
      display: inline-flex;
      align-items: center;
      border-radius: 9999px;
      padding: 0.25rem 0.75rem;
      font-size: 0.75rem;
      font-weight: 500;
    }
    .badge-blue { background-color: #dbeafe; color: #1e40af; }
    .badge-green { background-color: #dcfce7; color: #166534; }
    .badge-yellow { background-color: #fef9c3; color: #854d0e; }
    .badge-red { background-color: #fee2e2; color: #b91c1c; }
    .badge-gray { background-color: #f3f4f6; color: #374151; }

    .tab-content { display: none; }
    .tab-content.active { display: block; }

    .tab-button {
      padding: 0.5rem 1rem;
      border-radius: 0.25rem;
      font-weight: 500;
      cursor: pointer;
    }
    .tab-button.active {
      background-color: #2563eb;
      color: white;
    }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">
  <header class="bg-white shadow-sm py-4">
    <div class="container mx-auto px-4 flex justify-between items-center">
      <div class="flex items-center space-x-2">
        <div class="w-9 h-9 rounded-full bg-blue-600 text-white flex items-center justify-center font-bold text-xl">C</div>
        <span class="text-xl font-bold">ClassTasker Standalone Debug</span>
      </div>
      <a href="/" class="text-blue-600 hover:underline">Back to Home</a>
    </div>
  </header>

  <div class="container mx-auto py-8 px-4">
    <h1 class="text-2xl font-bold mb-6">Database Debug Tools</h1>

    <!-- Connection Status Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="p-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold">Supabase Connection Status</h2>
      </div>
      <div class="p-6">
        <div id="connection-status" class="text-blue-500">Testing connection...</div>
        <div id="connection-success" class="space-y-4 hidden">
          <div class="text-green-500 font-medium">✓ Connection successful!</div>
          <div id="task-count"></div>
        </div>
        <div id="connection-error" class="space-y-2 hidden">
          <div class="text-red-500 font-medium">✗ Connection failed</div>
          <div id="error-message" class="text-sm bg-red-50 p-2 rounded border border-red-200"></div>
        </div>
        <button id="test-connection-btn" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
          Test Connection Again
        </button>
      </div>
    </div>

    <!-- Debug Offer Acceptance -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="p-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold">Debug Offer Acceptance</h2>
        <p class="text-gray-500 text-sm">Test accepting an offer directly</p>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div class="space-y-2">
            <label for="debug-task-id" class="block font-medium text-gray-700">Task ID</label>
            <input
              id="debug-task-id"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="Enter task ID"
            />
          </div>
          <div class="space-y-2">
            <label for="debug-offer-id" class="block font-medium text-gray-700">Offer ID</label>
            <input
              id="debug-offer-id"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="Enter offer ID"
            />
          </div>
        </div>

        <button
          id="test-offer-btn"
          class="mb-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        >
          Test Offer Acceptance
        </button>

        <div id="acceptance-result" class="mt-4 p-4 border rounded bg-slate-50 overflow-auto max-h-60 hidden">
          <h3 id="result-status" class="font-medium mb-2"></h3>
          <pre id="result-data" class="text-xs whitespace-pre-wrap"></pre>
        </div>
      </div>
    </div>

    <!-- Data Tabs -->
    <div id="data-tabs" class="hidden">
      <div class="flex space-x-2 mb-4">
        <button class="tab-button active" data-tab="tasks">Tasks</button>
        <button class="tab-button" data-tab="offers">Offers</button>
        <button class="tab-button" data-tab="status">Status Counts</button>
      </div>

      <!-- Tasks Tab -->
      <div id="tasks-tab" class="tab-content active">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold">Tasks</h2>
            <p id="tasks-count" class="text-gray-500 text-sm">All tasks in the database (0)</p>
          </div>
          <div class="p-6">
            <div class="rounded-md border overflow-hidden">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Budget</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                  </tr>
                </thead>
                <tbody id="tasks-table-body" class="bg-white divide-y divide-gray-200">
                  <!-- Tasks will be inserted here -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Offers Tab -->
      <div id="offers-tab" class="tab-content">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold">Offers</h2>
            <p id="offers-count" class="text-gray-500 text-sm">All offers in the database (0)</p>
          </div>
          <div class="p-6">
            <div class="rounded-md border overflow-hidden">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Task ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                  </tr>
                </thead>
                <tbody id="offers-table-body" class="bg-white divide-y divide-gray-200">
                  <!-- Offers will be inserted here -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Status Counts Tab -->
      <div id="status-tab" class="tab-content">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Task Status Counts -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-4 border-b border-gray-200">
              <h2 class="text-lg font-semibold">Task Status Counts</h2>
              <p class="text-gray-500 text-sm">Distribution of task statuses</p>
            </div>
            <div class="p-6">
              <div class="rounded-md border overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Count</th>
                    </tr>
                  </thead>
                  <tbody id="task-status-table-body" class="bg-white divide-y divide-gray-200">
                    <!-- Task status counts will be inserted here -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Offer Status Counts -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-4 border-b border-gray-200">
              <h2 class="text-lg font-semibold">Offer Status Counts</h2>
              <p class="text-gray-500 text-sm">Distribution of offer statuses</p>
            </div>
            <div class="p-6">
              <div class="rounded-md border overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Count</th>
                    </tr>
                  </thead>
                  <tbody id="offer-status-table-body" class="bg-white divide-y divide-gray-200">
                    <!-- Offer status counts will be inserted here -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Debug Console -->
  <div id="debug-console" class="fixed bottom-0 left-0 right-0 bg-gray-900 text-white p-4 h-48 overflow-auto" style="display: none;">
    <div class="flex justify-between items-center mb-2">
      <h3 class="font-bold">Debug Console</h3>
      <button id="clear-console" class="px-2 py-1 bg-gray-700 text-xs rounded">Clear</button>
    </div>
    <div id="console-output" class="font-mono text-xs"></div>
  </div>
  <button id="toggle-console" class="fixed bottom-4 right-4 bg-gray-800 text-white p-2 rounded-full shadow-lg">🐞</button>

  <script>
    // Debug console functionality
    const debugConsole = document.getElementById('debug-console');
    const consoleOutput = document.getElementById('console-output');
    const toggleConsole = document.getElementById('toggle-console');
    const clearConsole = document.getElementById('clear-console');

    toggleConsole.addEventListener('click', () => {
      debugConsole.style.display = debugConsole.style.display === 'none' ? 'block' : 'none';
    });

    clearConsole.addEventListener('click', () => {
      consoleOutput.innerHTML = '';
    });

    // Override console.log and console.error
    const originalLog = console.log;
    const originalError = console.error;

    console.log = function(...args) {
      originalLog.apply(console, args);
      const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg).join(' ');
      const logEntry = document.createElement('div');
      logEntry.className = 'mb-1 pb-1 border-b border-gray-700';
      logEntry.innerHTML = `<span class="text-green-400">[LOG]</span> ${message}`;
      consoleOutput.appendChild(logEntry);
      consoleOutput.scrollTop = consoleOutput.scrollHeight;
    };

    console.error = function(...args) {
      originalError.apply(console, args);
      const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg).join(' ');
      const logEntry = document.createElement('div');
      logEntry.className = 'mb-1 pb-1 border-b border-gray-700';
      logEntry.innerHTML = `<span class="text-red-400">[ERROR]</span> ${message}`;
      consoleOutput.appendChild(logEntry);
      consoleOutput.scrollTop = consoleOutput.scrollHeight;
    };

    // Initialize Supabase client
    console.log("Initializing Supabase client...");
    const SUPABASE_URL = "https://qcnotlojmyvpqbbgoxbc.supabase.co";
    const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.8KEJub30NhSrbKdXhR0FlFQ0p4U7fNKqmjt0EMSl4ZM";

    // Check if supabase is defined
    if (typeof supabase === 'undefined') {
      console.error("Supabase client is not defined. The script might not have loaded correctly.");
      connectionStatus.classList.add('hidden');
      connectionError.classList.remove('hidden');
      errorMessage.textContent = "Supabase client is not defined. Please check the browser console for more details.";
    } else {
      console.log("Creating Supabase client with URL:", SUPABASE_URL);
      const supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
      window.supabaseClient = supabaseClient; // Store in window for debugging

    }

    // DOM elements
    const connectionStatus = document.getElementById('connection-status');
    const connectionSuccess = document.getElementById('connection-success');
    const connectionError = document.getElementById('connection-error');
    const taskCount = document.getElementById('task-count');
    const errorMessage = document.getElementById('error-message');
    const testConnectionBtn = document.getElementById('test-connection-btn');
    const dataTabs = document.getElementById('data-tabs');
    const tasksCount = document.getElementById('tasks-count');
    const offersCount = document.getElementById('offers-count');
    const tasksTableBody = document.getElementById('tasks-table-body');
    const offersTableBody = document.getElementById('offers-table-body');
    const taskStatusTableBody = document.getElementById('task-status-table-body');
    const offerStatusTableBody = document.getElementById('offer-status-table-body');
    const debugTaskId = document.getElementById('debug-task-id');
    const debugOfferId = document.getElementById('debug-offer-id');
    const testOfferBtn = document.getElementById('test-offer-btn');
    const acceptanceResult = document.getElementById('acceptance-result');
    const resultStatus = document.getElementById('result-status');
    const resultData = document.getElementById('result-data');

    // State
    let tasks = [];
    let offers = [];
    let taskStatusCounts = [];
    let offerStatusCounts = [];

    // Helper functions
    function formatDate(dateString) {
      if (!dateString) return 'N/A';
      return new Date(dateString).toLocaleString();
    }

    function getStatusBadgeClass(status) {
      switch (status) {
        case 'open': return 'badge-blue';
        case 'in_progress': return 'badge-yellow';
        case 'completed': return 'badge-green';
        case 'cancelled': return 'badge-red';
        case 'accepted': return 'badge-green';
        case 'rejected': return 'badge-red';
        case 'pending': return 'badge-yellow';
        case 'awaiting': return 'badge-yellow';
        case 'assigned': return 'badge-blue';
        default: return 'badge-gray';
      }
    }

    // Tab functionality
    document.querySelectorAll('.tab-button').forEach(button => {
      button.addEventListener('click', () => {
        // Remove active class from all buttons and content
        document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

        // Add active class to clicked button and corresponding content
        button.classList.add('active');
        document.getElementById(`${button.dataset.tab}-tab`).classList.add('active');
      });
    });

    // Test connection
    async function testConnection() {
      console.log("Starting connection test...");
      connectionStatus.classList.remove('hidden');
      connectionSuccess.classList.add('hidden');
      connectionError.classList.add('hidden');
      dataTabs.classList.add('hidden');

      // Check if supabase client is available
      if (typeof supabase === 'undefined' || !window.supabaseClient) {
        console.error("Supabase client is not available");
        connectionStatus.classList.add('hidden');
        connectionError.classList.remove('hidden');
        errorMessage.textContent = "Supabase client is not available. Please check the browser console for more details.";
        return;
      }

      const client = window.supabaseClient;
      console.log("Using Supabase client:", client);

      try {
        // First, try a simple health check
        console.log("Performing health check...");
        try {
          const { data: healthData, error: healthError } = await client.rpc('version');
          if (healthError) {
            console.error('Health check failed:', healthError);
          } else {
            console.log('Health check successful:', healthData);
          }
        } catch (healthCheckError) {
          console.error('Health check exception:', healthCheckError);
          // Continue despite health check error
        }

        // Test a simple query - just get the count
        console.log("Querying tasks count...");
        const { count, error } = await client
          .from('tasks')
          .select('*', { count: 'exact', head: true });

        console.log("Count query result:", { count, error });

        if (error) {
          console.error('Error connecting to Supabase:', error);
          connectionStatus.classList.add('hidden');
          connectionError.classList.remove('hidden');
          errorMessage.textContent = error.message;
          return;
        }

        connectionStatus.classList.add('hidden');
        connectionSuccess.classList.remove('hidden');
        taskCount.textContent = `Total tasks in database: ${count}`;

        // Fetch tasks with all fields
        console.log("Fetching tasks data...");
        const { data: tasksData, error: tasksError } = await client
          .from('tasks')
          .select('*')
          .order('created_at', { ascending: false });

        console.log("Tasks query result:", {
          dataReceived: !!tasksData,
          count: tasksData?.length || 0,
          error: tasksError
        });

        if (tasksError) {
          console.error('Error fetching tasks:', tasksError);
          return;
        }

        tasks = tasksData || [];
        console.log(`Received ${tasks.length} tasks`);

        // Get task status counts
        const taskStatusMap = {};
        tasks.forEach(task => {
          taskStatusMap[task.status] = (taskStatusMap[task.status] || 0) + 1;
        });

        taskStatusCounts = Object.entries(taskStatusMap).map(([status, count]) => ({
          status,
          count
        }));

        // Fetch offers data
        console.log("Fetching offers data...");
        const { data: offersData, error: offersError } = await client
          .from('offers')
          .select('*')
          .order('created_at', { ascending: false });

        console.log("Offers query result:", {
          dataReceived: !!offersData,
          count: offersData?.length || 0,
          error: offersError
        });

        if (offersError) {
          console.error('Error fetching offers:', offersError);
        } else {
          offers = offersData || [];
          console.log(`Received ${offers.length} offers`);

          // Get offer status counts
          const offerStatusMap = {};
          offers.forEach(offer => {
            offerStatusMap[offer.status] = (offerStatusMap[offer.status] || 0) + 1;
          });

          offerStatusCounts = Object.entries(offerStatusMap).map(([status, count]) => ({
            status,
            count
          }));
        }

        // Update UI
        console.log("Updating UI with data...");
        updateUI();
        dataTabs.classList.remove('hidden');
        console.log("Connection test completed successfully");
      } catch (err) {
        console.error('Unexpected error during connection test:', err);
        connectionStatus.classList.add('hidden');
        connectionError.classList.remove('hidden');
        errorMessage.textContent = err.message || 'An unexpected error occurred';
      }
    }

    // Update UI with data
    function updateUI() {
      // Update tasks table
      tasksCount.textContent = `All tasks in the database (${tasks.length})`;
      tasksTableBody.innerHTML = '';

      if (tasks.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td colspan="6" class="px-6 py-4 text-center text-gray-500">
            No tasks found
          </td>
        `;
        tasksTableBody.appendChild(row);
      } else {
        tasks.forEach(task => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center gap-2">
                <span class="font-mono text-xs">${task.id.substring(0, 8)}...</span>
                <button
                  class="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded"
                  onclick="document.getElementById('debug-task-id').value = '${task.id}'"
                >
                  Use
                </button>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">${task.title}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="badge ${getStatusBadgeClass(task.status)}">${task.status}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">$${task.budget}</td>
            <td class="px-6 py-4 whitespace-nowrap">${formatDate(task.due_date)}</td>
            <td class="px-6 py-4 whitespace-nowrap">${formatDate(task.created_at)}</td>
          `;
          tasksTableBody.appendChild(row);
        });
      }

      // Update offers table
      offersCount.textContent = `All offers in the database (${offers.length})`;
      offersTableBody.innerHTML = '';

      if (offers.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td colspan="5" class="px-6 py-4 text-center text-gray-500">
            No offers found
          </td>
        `;
        offersTableBody.appendChild(row);
      } else {
        offers.forEach(offer => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center gap-2">
                <span class="font-mono text-xs">${offer.id.substring(0, 8)}...</span>
                <button
                  class="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded"
                  onclick="document.getElementById('debug-offer-id').value = '${offer.id}'"
                >
                  Use
                </button>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center gap-2">
                <span class="font-mono text-xs">${offer.task_id.substring(0, 8)}...</span>
                <button
                  class="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded"
                  onclick="document.getElementById('debug-task-id').value = '${offer.task_id}'"
                >
                  Use
                </button>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">$${offer.amount}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="badge ${getStatusBadgeClass(offer.status)}">${offer.status}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">${formatDate(offer.created_at)}</td>
          `;
          offersTableBody.appendChild(row);
        });
      }

      // Update task status counts
      taskStatusTableBody.innerHTML = '';

      if (taskStatusCounts.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td colspan="2" class="px-6 py-4 text-center text-gray-500">
            No task status data
          </td>
        `;
        taskStatusTableBody.appendChild(row);
      } else {
        taskStatusCounts.forEach(item => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="badge ${getStatusBadgeClass(item.status)}">${item.status}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">${item.count}</td>
          `;
          taskStatusTableBody.appendChild(row);
        });
      }

      // Update offer status counts
      offerStatusTableBody.innerHTML = '';

      if (offerStatusCounts.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td colspan="2" class="px-6 py-4 text-center text-gray-500">
            No offer status data
          </td>
        `;
        offerStatusTableBody.appendChild(row);
      } else {
        offerStatusCounts.forEach(item => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="badge ${getStatusBadgeClass(item.status)}">${item.status}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">${item.count}</td>
          `;
          offerStatusTableBody.appendChild(row);
        });
      }
    }

    // Test offer acceptance
    async function testOfferAcceptance() {
      console.log("Starting offer acceptance test...");
      const taskId = debugTaskId.value.trim();
      const offerId = debugOfferId.value.trim();

      if (!taskId || !offerId) {
        alert('Please provide both Task ID and Offer ID');
        return;
      }

      // Check if supabase client is available
      if (typeof supabase === 'undefined' || !window.supabaseClient) {
        console.error("Supabase client is not available");
        alert("Supabase client is not available. Please check the browser console for more details.");
        return;
      }

      const client = window.supabaseClient;
      console.log("Using Supabase client for offer acceptance test:", client);

      testOfferBtn.textContent = 'Testing...';
      testOfferBtn.disabled = true;
      acceptanceResult.classList.add('hidden');

      try {
        // Step 1: Check if the offer exists and get its current status
        console.log("Step 1: Checking offer status");
        const { data: offerData, error: offerError } = await client
          .from('offers')
          .select('*')
          .eq('id', offerId)
          .single();

        console.log("Offer check result:", { dataReceived: !!offerData, error: offerError });

        if (offerError) {
          console.error('Error checking offer:', offerError);
          showResult(false, {
            success: false,
            step: 1,
            error: offerError,
            message: 'Failed to check offer status'
          });
          return;
        }

        console.log("Current offer data:", offerData);

        // Step 2: Check if the task exists and get its current status
        console.log("Step 2: Checking task status");
        const { data: taskData, error: taskError } = await client
          .from('tasks')
          .select('*')
          .eq('id', taskId)
          .single();

        console.log("Task check result:", { dataReceived: !!taskData, error: taskError });

        if (taskError) {
          console.error('Error checking task:', taskError);
          showResult(false, {
            success: false,
            step: 2,
            error: taskError,
            message: 'Failed to check task status'
          });
          return;
        }

        console.log("Current task data:", taskData);

        // Step 3: Try to update the offer status to 'accepted'
        console.log("Step 3: Updating offer status to 'accepted'");

        // First, let's try to get the offer again to double-check it exists
        console.log("Double-checking offer existence before update...");
        const { data: offerCheckData, error: offerCheckError } = await client
          .from('offers')
          .select('*')
          .eq('id', offerId);

        console.log("Offer existence check result:", {
          dataReceived: !!offerCheckData,
          count: offerCheckData?.length || 0,
          error: offerCheckError
        });

        if (offerCheckError || !offerCheckData || offerCheckData.length === 0) {
          console.error('Offer not found before update:', { error: offerCheckError, data: offerCheckData });
          showResult(false, {
            success: false,
            step: 3,
            error: offerCheckError || { message: 'Offer not found' },
            message: 'Offer not found before update'
          });
          return;
        }

        // Now try the update without using .single() to avoid the error
        const { data: updatedOfferArray, error: updateOfferError } = await client
          .from('offers')
          .update({ status: 'accepted' })
          .eq('id', offerId)
          .select();

        console.log("Offer update result:", {
          dataReceived: !!updatedOfferArray,
          count: updatedOfferArray?.length || 0,
          error: updateOfferError
        });

        if (updateOfferError) {
          console.error('Error updating offer status:', updateOfferError);
          showResult(false, {
            success: false,
            step: 3,
            error: updateOfferError,
            message: 'Failed to update offer status'
          });
          return;
        }

        // Check if we got any updated rows
        if (!updatedOfferArray || updatedOfferArray.length === 0) {
          console.error('No offers were updated');
          showResult(false, {
            success: false,
            step: 3,
            error: { message: 'No offers were updated' },
            message: 'Update operation succeeded but no offers were updated'
          });
          return;
        }

        const updatedOffer = updatedOfferArray[0];

        console.log("Updated offer data:", updatedOffer);

        // Step 4: Try to update the task status to 'assigned'
        console.log("Step 4: Updating task status to 'assigned'");

        // First, let's try to get the task again to double-check it exists
        console.log("Double-checking task existence before update...");
        const { data: taskCheckData, error: taskCheckError } = await client
          .from('tasks')
          .select('*')
          .eq('id', taskId);

        console.log("Task existence check result:", {
          dataReceived: !!taskCheckData,
          count: taskCheckData?.length || 0,
          error: taskCheckError
        });

        if (taskCheckError || !taskCheckData || taskCheckData.length === 0) {
          console.error('Task not found before update:', { error: taskCheckError, data: taskCheckData });
          showResult(false, {
            success: false,
            step: 4,
            error: taskCheckError || { message: 'Task not found' },
            message: 'Task not found before update'
          });
          return;
        }

        // Now try the update without using .single() to avoid the error
        const { data: updatedTaskArray, error: updateTaskError } = await client
          .from('tasks')
          .update({ status: 'assigned' })
          .eq('id', taskId)
          .select();

        console.log("Task update result:", {
          dataReceived: !!updatedTaskArray,
          count: updatedTaskArray?.length || 0,
          error: updateTaskError
        });

        if (updateTaskError) {
          console.error('Error updating task status:', updateTaskError);
          showResult(false, {
            success: false,
            step: 4,
            error: updateTaskError,
            message: 'Failed to update task status'
          });
          return;
        }

        // Check if we got any updated rows
        if (!updatedTaskArray || updatedTaskArray.length === 0) {
          console.error('No tasks were updated');
          showResult(false, {
            success: false,
            step: 4,
            error: { message: 'No tasks were updated' },
            message: 'Update operation succeeded but no tasks were updated'
          });
          return;
        }

        const updatedTask = updatedTaskArray[0];

        console.log("Updated task data:", updatedTask);

        // Step 5: Try to reject all other offers for this task
        console.log("Step 5: Rejecting all other offers for this task");
        const { data: rejectedOffers, error: rejectError } = await client
          .from('offers')
          .update({ status: 'rejected' })
          .eq('task_id', taskId)
          .neq('id', offerId)
          .select();

        console.log("Reject other offers result:", {
          dataReceived: !!rejectedOffers,
          count: rejectedOffers?.length || 0,
          error: rejectError
        });

        if (rejectError) {
          console.error('Warning - error rejecting other offers:', rejectError);
          // We continue despite this error as the main operation succeeded
        } else {
          console.log(`All other offers rejected: ${rejectedOffers?.length || 0} offers`);
        }

        showResult(true, {
          success: true,
          updatedOffer,
          updatedTask,
          rejectedOffers
        });

        // Refresh data
        console.log("Offer acceptance test completed successfully, refreshing data...");
        testConnection();
      } catch (error) {
        console.error('Exception in testOfferAcceptance:', error);
        showResult(false, {
          success: false,
          error,
          message: 'Exception occurred during offer acceptance'
        });
      } finally {
        testOfferBtn.textContent = 'Test Offer Acceptance';
        testOfferBtn.disabled = false;
      }
    }

    function showResult(success, data) {
      acceptanceResult.classList.remove('hidden');

      if (success) {
        resultStatus.innerHTML = '<span class="text-green-600">Success</span>';
      } else {
        resultStatus.innerHTML = `<span class="text-red-600">Failed at step ${data.step || 'unknown'}</span>`;
      }

      resultData.textContent = JSON.stringify(data, null, 2);
    }

    // Event listeners
    testConnectionBtn.addEventListener('click', testConnection);
    testOfferBtn.addEventListener('click', testOfferAcceptance);

    // Initial connection test
    testConnection();
  </script>
</body>
</html>
