import { supabase } from '@/integrations/supabase/client';
import { EmailConfig, EmailProvider, SchoolEmailConfig, SendEmailParams } from '@/types/email';

// Default configuration
const defaultConfig: EmailConfig = {
  provider: 'smtp',
  fromEmail: '<EMAIL>',
  fromName: 'Classtasker',
  smtpHost: 'server326.web-hosting.com',
  smtpPort: 465,
  smtpUsername: '<EMAIL>',
  smtpPassword: '', // This will need to be set in the UI
  smtpSecure: true
};

export const emailService = {
  /**
   * Get the current email configuration
   */
  async getConfig(): Promise<EmailConfig> {
    try {
      // In a real implementation, this would be stored in a database table
      // For now, we'll use user metadata of the current user
      const { data: userData, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;

      const emailConfig = userData.user?.user_metadata?.emailConfig as EmailConfig;
      return emailConfig || defaultConfig;
    } catch (error) {
      console.error('Error getting email config:', error);
      return defaultConfig;
    }
  },

  /**
   * Get school-specific email configuration
   */
  async getSchoolConfig(schoolId: string): Promise<SchoolEmailConfig | null> {
    try {
      const config = await this.getConfig();

      if (!config.schoolConfigs) {
        return null;
      }

      return config.schoolConfigs.find(sc => sc.schoolId === schoolId) || null;
    } catch (error) {
      console.error(`Error getting school config for school ${schoolId}:`, error);
      return null;
    }
  },

  /**
   * Save email configuration
   */
  async saveConfig(config: EmailConfig): Promise<void> {
    try {
      // In a real implementation, this would be stored in a database table
      // For now, we'll use user metadata of the current user
      const { error } = await supabase.auth.updateUser({
        data: {
          emailConfig: config
        }
      });

      if (error) throw error;
    } catch (error) {
      console.error('Error saving email config:', error);
      throw error;
    }
  },

  /**
   * Test the email configuration by sending a test email
   */
  async testConfig(config: EmailConfig, testEmail: string): Promise<boolean> {
    try {
      if (config.provider === 'none') {
        throw new Error('No email provider configured');
      }

      // Validate required fields based on provider
      if (config.provider === 'smtp' && (!config.smtpHost || !config.smtpUsername || !config.smtpPassword || !config.fromEmail)) {
        throw new Error('SMTP requires host, username, password, and from email');
      }

      if (config.provider === 'smtp' && (!config.smtpHost || !config.smtpPort || !config.smtpUsername || !config.smtpPassword)) {
        throw new Error('SMTP requires host, port, username, and password');
      }

      // For SMTP, validate port is a number
      if (config.provider === 'smtp' && (typeof config.smtpPort !== 'number' || isNaN(config.smtpPort))) {
        throw new Error('SMTP port must be a valid number');
      }

      // Validate email format
      if (!config.fromEmail || !config.fromEmail.includes('@')) {
        throw new Error('From email must be a valid email address');
      }

      if (!testEmail || !testEmail.includes('@')) {
        throw new Error('Test email must be a valid email address');
      }

      // Log the test configuration
      console.log(`Testing email configuration with ${config.provider}:`, {
        fromEmail: config.fromEmail,
        fromName: config.fromName,
        to: testEmail,
        smtpHost: config.smtpHost,
        smtpPort: config.smtpPort,
        smtpSecure: config.smtpSecure
      });

      // Use our new emailTesterService to test the configuration
      if (config.provider === 'smtp') {
        try {
          // Import the emailTesterService module
          const { testSmtpConnection } = await import('./emailTesterService.js');

          // Test the SMTP connection
          const result = await testSmtpConnection(config, testEmail);

          if (!result.success) {
            throw new Error(result.message);
          }

          console.log('SMTP test successful:', result.message);
        } catch (testError) {
          console.error('SMTP test failed:', testError);
          throw testError;
        }
      }

      return true;
    } catch (error) {
      console.error('Error testing email config:', error);
      throw error;
    }
  },

  /**
   * Send an email using the configured provider
   */
  async sendEmail(params: SendEmailParams): Promise<boolean> {
    try {
      // Get the base configuration
      let config = await this.getConfig();

      // If a schoolId is provided, try to get school-specific config
      if (params.schoolId) {
        const schoolConfig = await this.getSchoolConfig(params.schoolId);

        if (schoolConfig) {
          // Use school-specific config but keep the global config as fallback
          config = {
            ...config,
            provider: schoolConfig.provider,
            apiKey: schoolConfig.apiKey || config.apiKey,
            fromEmail: schoolConfig.fromEmail || config.fromEmail,
            fromName: schoolConfig.fromName || config.fromName,

            smtpHost: schoolConfig.smtpHost || config.smtpHost,
            smtpPort: schoolConfig.smtpPort || config.smtpPort,
            smtpUsername: schoolConfig.smtpUsername || config.smtpUsername,
            smtpPassword: schoolConfig.smtpPassword || config.smtpPassword,
            smtpSecure: schoolConfig.smtpSecure !== undefined ? schoolConfig.smtpSecure : config.smtpSecure
          };

          console.log(`Using school-specific email config for school ${params.schoolId}`);
        }
      }

      // Import the emailSender module
      const { sendEmail } = await import('./emailSender');

      // Send the email
      const result = await sendEmail(config, params);

      if (!result.success) {
        console.error('Failed to send email:', result.message);
        return false;
      }

      console.log('Email sent successfully:', result.message);
      return true;
    } catch (error) {
      console.error('Error sending email:', error);
      return false;
    }
  },

  /**
   * Send an invitation email
   */
  async sendInvitationEmail(email: string, token: string, organizationName: string, role: string, schoolId?: string): Promise<boolean> {
    try {
      const invitationUrl = `${window.location.origin}/invitation/accept?token=${token}&email=${encodeURIComponent(email)}`;

      const subject = `Invitation to join ${organizationName} on Classtasker`;
      const body = `
        <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html xmlns="http://www.w3.org/1999/xhtml">
        <head>
          <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
          <title>Invitation to Classtasker</title>
        </head>
        <body style="font-family: Arial, Helvetica, sans-serif; line-height: 1.6; color: #333333; margin: 0; padding: 0; background-color: #f5f5f5;">
          <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px; margin: 20px auto; background-color: #ffffff; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
            <tr>
              <td style="background-color: #4f46e5; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0;">
                <h1 style="margin: 0; font-size: 24px;">You've been invited to join ${organizationName}</h1>
              </td>
            </tr>
            <tr>
              <td style="padding: 20px; border: 1px solid #e0e0e0; border-top: none; border-radius: 0 0 5px 5px;">
                <p style="margin-top: 0;">Hello,</p>
                <p>You have been invited to join <strong>${organizationName}</strong> as a <strong>${role}</strong> on Classtasker.</p>
                <p>Classtasker is a platform that connects schools with maintenance workers and suppliers to efficiently manage tasks and services.</p>
                <p>To accept this invitation, please click the button below:</p>
                <table border="0" cellpadding="0" cellspacing="0" width="100%">
                  <tr>
                    <td align="center" style="padding: 20px 0;">
                      <table border="0" cellpadding="0" cellspacing="0">
                        <tr>
                          <td align="center" bgcolor="#4f46e5" style="border-radius: 5px;">
                            <a href="${invitationUrl}" target="_blank" style="display: inline-block; padding: 12px 24px; font-family: Arial, sans-serif; font-size: 16px; color: #ffffff; text-decoration: none; border-radius: 5px; font-weight: bold;">Accept Invitation</a>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
                <p>Or copy and paste this URL into your browser:</p>
                <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 3px; font-size: 14px;">${invitationUrl}</p>
                <p>This invitation will expire in 7 days.</p>
                <p style="margin-bottom: 0;">If you have any questions, please contact the organization administrator.</p>
              </td>
            </tr>
            <tr>
              <td style="padding: 20px; font-size: 12px; color: #666; text-align: center;">
                <p style="margin-top: 0;">This is an automated message from Classtasker. Please do not reply to this email.</p>
                <p style="margin-bottom: 0;">&copy; ${new Date().getFullYear()} Classtasker. All rights reserved.</p>
              </td>
            </tr>
          </table>
        </body>
        </html>
      `;

      return await this.sendEmail({
        to: email,
        subject,
        body,
        schoolId
      });
    } catch (error) {
      console.error('Error sending invitation email:', error);
      return false;
    }
  }
};

export default emailService;
