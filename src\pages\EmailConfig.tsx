import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { emailService } from '@/services/emailService';
import { EmailConfig as EmailConfigType, EmailProvider } from '@/types/email';
import { Loader2, Mail, Send, CheckCircle, ExternalLink } from 'lucide-react';

const EmailConfig = () => {
  const { user, isAdmin } = useAuth();
  const navigate = useNavigate();

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [activeTab, setActiveTab] = useState<EmailProvider>('none');
  const [testEmail, setTestEmail] = useState('');

  const [config, setConfig] = useState<EmailConfigType>({
    provider: 'none',
    fromEmail: '',
    fromName: '',
  });

  useEffect(() => {
    if (!user) {
      toast({
        variant: 'destructive',
        title: 'Authentication Required',
        description: 'You must be logged in to access this page.',
      });
      navigate('/login');
      return;
    }

    if (!isAdmin) {
      toast({
        variant: 'destructive',
        title: 'Access Denied',
        description: 'You must be an admin to access this page.',
      });
      navigate('/dashboard');
      return;
    }

    loadConfig();
  }, [user, isAdmin, navigate]);

  const loadConfig = async () => {
    setIsLoading(true);
    try {
      const config = await emailService.getConfig();
      setConfig(config);
      setActiveTab(config.provider);

      // Set test email to current user's email
      if (user?.email) {
        setTestEmail(user.email);
      }
    } catch (error) {
      console.error('Error loading email config:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to load email configuration.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleProviderChange = (value: EmailProvider) => {
    setActiveTab(value);
    setConfig(prev => ({ ...prev, provider: value }));
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;

    if (type === 'number') {
      setConfig(prev => ({ ...prev, [name]: parseInt(value) }));
    } else {
      setConfig(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setConfig(prev => ({ ...prev, [name]: checked }));
  };

  const handleSaveConfig = async () => {
    setIsSaving(true);
    try {
      await emailService.saveConfig(config);
      toast({
        title: 'Configuration Saved',
        description: 'Email configuration has been saved successfully.',
      });
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Failed to save email configuration.',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleTestConfig = async () => {
    if (!testEmail) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Please enter a test email address.',
      });
      return;
    }

    // Validate configuration before testing
    if (config.provider === 'smtp') {
      if (!config.smtpHost) {
        toast({
          variant: 'destructive',
          title: 'Missing Configuration',
          description: 'Please enter an SMTP host.',
        });
        return;
      }

      if (!config.smtpPort) {
        toast({
          variant: 'destructive',
          title: 'Missing Configuration',
          description: 'Please enter an SMTP port.',
        });
        return;
      }

      if (!config.smtpUsername) {
        toast({
          variant: 'destructive',
          title: 'Missing Configuration',
          description: 'Please enter an SMTP username.',
        });
        return;
      }

      if (!config.smtpPassword) {
        toast({
          variant: 'destructive',
          title: 'Missing Configuration',
          description: 'Please enter an SMTP password.',
        });
        return;
      }

      if (!config.fromEmail) {
        toast({
          variant: 'destructive',
          title: 'Missing Configuration',
          description: 'Please enter a from email address.',
        });
        return;
      }
    }

    setIsTesting(true);
    try {
      // Just test the configuration - this will send a test email
      const success = await emailService.testConfig(config, testEmail);

      if (success) {
        toast({
          title: 'Test Successful',
          description: `A test email has been sent to ${testEmail}.`,
        });
      } else {
        console.error('Email testing failed but no error was thrown');
        toast({
          variant: 'destructive',
          title: 'Test Failed',
          description: 'Failed to send test email. Please check the console for more details.',
        });
      }
    } catch (error: any) {
      console.error('Error testing email configuration:', error);
      let errorMessage = 'Failed to send test email.';

      if (error.message) {
        errorMessage += ` Error: ${error.message}`;
      }

      // Check for specific error types
      if (error.message && error.message.includes('ECONNREFUSED')) {
        errorMessage = 'Connection refused. Please check your SMTP host and port.';
      } else if (error.message && error.message.includes('authentication')) {
        errorMessage = 'Authentication failed. Please check your username and password.';
      } else if (error.message && error.message.includes('certificate')) {
        errorMessage = 'SSL/TLS certificate error. Try toggling the SSL/TLS option.';
      }

      toast({
        variant: 'destructive',
        title: 'Test Failed',
        description: errorMessage,
      });
    } finally {
      setIsTesting(false);
    }
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <Card className="w-full max-w-4xl mx-auto">
            <CardContent className="flex justify-center items-center p-6">
              <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Mail className="mr-2 h-5 w-5 text-classtasker-blue" />
                Email Configuration
              </CardTitle>
              <CardDescription>
                Configure email settings for sending invitations and notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-2">Email Provider</h3>
                  <p className="text-sm text-gray-500 mb-4">
                    Select an email provider to use for sending emails
                  </p>

                  <RadioGroup
                    value={activeTab}
                    onValueChange={(value) => handleProviderChange(value as EmailProvider)}
                    className="grid grid-cols-1 md:grid-cols-2 gap-4"
                  >
                    <div className={`flex items-center space-x-2 border rounded-md p-4 cursor-pointer ${activeTab === 'smtp' ? 'border-classtasker-blue bg-blue-50' : ''}`}>
                      <RadioGroupItem value="smtp" id="smtp" />
                      <Label htmlFor="smtp" className="cursor-pointer">SMTP</Label>
                    </div>

                    <div className={`flex items-center space-x-2 border rounded-md p-4 cursor-pointer ${activeTab === 'none' ? 'border-classtasker-blue bg-blue-50' : ''}`}>
                      <RadioGroupItem value="none" id="none" />
                      <Label htmlFor="none" className="cursor-pointer">No Email (Debug)</Label>
                    </div>
                  </RadioGroup>
                </div>

                <Separator />

                <Tabs value={activeTab} onValueChange={(value) => handleProviderChange(value as EmailProvider)}>

                  <TabsContent value="smtp" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="smtpHost">SMTP Host</Label>
                        <Input
                          id="smtpHost"
                          name="smtpHost"
                          placeholder="e.g., smtp.gmail.com"
                          value={config.smtpHost || ''}
                          onChange={handleInputChange}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="smtpPort">SMTP Port</Label>
                        <Input
                          id="smtpPort"
                          name="smtpPort"
                          type="number"
                          placeholder="e.g., 587"
                          value={config.smtpPort || ''}
                          onChange={handleInputChange}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="smtpUsername">SMTP Username</Label>
                        <Input
                          id="smtpUsername"
                          name="smtpUsername"
                          placeholder="Enter SMTP username"
                          value={config.smtpUsername || ''}
                          onChange={handleInputChange}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="smtpPassword">SMTP Password</Label>
                        <Input
                          id="smtpPassword"
                          name="smtpPassword"
                          type="password"
                          placeholder="Enter SMTP password"
                          value={config.smtpPassword || ''}
                          onChange={handleInputChange}
                        />
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          id="smtpSecure"
                          checked={config.smtpSecure || false}
                          onCheckedChange={(checked) => handleSwitchChange('smtpSecure', checked)}
                        />
                        <Label htmlFor="smtpSecure">Use SSL/TLS</Label>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="none" className="space-y-4">
                    <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                      <p className="text-yellow-800">
                        No email provider is configured. Emails will be logged to the console but not actually sent.
                        This is useful for development and testing.
                      </p>
                    </div>
                  </TabsContent>
                </Tabs>

                <Separator />

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Sender Information</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="fromEmail">From Email</Label>
                      <Input
                        id="fromEmail"
                        name="fromEmail"
                        placeholder="<EMAIL>"
                        value={config.fromEmail || ''}
                        onChange={handleInputChange}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="fromName">From Name</Label>
                      <Input
                        id="fromName"
                        name="fromName"
                        placeholder="Your Organization Name"
                        value={config.fromName || ''}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Test Configuration</h3>
                  <p className="text-sm text-gray-500">
                    Send a test email to verify your configuration
                  </p>

                  <div className="flex flex-col md:flex-row gap-4">
                    <div className="flex-grow">
                      <Input
                        placeholder="Enter email address for testing"
                        value={testEmail}
                        onChange={(e) => setTestEmail(e.target.value)}
                      />
                    </div>
                    <Button
                      onClick={handleTestConfig}
                      disabled={isTesting || config.provider === 'none'}
                      className="whitespace-nowrap"
                    >
                      {isTesting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Sending...
                        </>
                      ) : (
                        <>
                          <Send className="mr-2 h-4 w-4" />
                          Send Test Email
                        </>
                      )}
                    </Button>
                  </div>

                  <div className="mt-4 pt-4 border-t">
                    <Button variant="outline" onClick={() => navigate('/test-email')} className="w-full">
                      <ExternalLink className="mr-2 h-4 w-4" />
                      Advanced SMTP Testing Tool
                    </Button>
                    <p className="text-xs text-gray-500 mt-2">
                      Having trouble? Use our advanced SMTP testing tool to diagnose connection issues.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col sm:flex-row justify-between border-t px-6 py-4 gap-4">
              <Button variant="outline" onClick={() => navigate('/profile/' + user?.id)} className="w-full sm:w-auto">
                Cancel
              </Button>
              <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
                <Button
                  variant="outline"
                  onClick={async () => {
                    await handleSaveConfig();
                    handleTestConfig();
                  }}
                  disabled={isSaving || isTesting}
                  className="w-full sm:w-auto"
                >
                  {isSaving || isTesting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {isSaving ? 'Saving...' : 'Testing...'}
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Save and Test
                    </>
                  )}
                </Button>
                <Button onClick={handleSaveConfig} disabled={isSaving} className="w-full sm:w-auto">
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Save Configuration
                    </>
                  )}
                </Button>
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
};

export default EmailConfig;
