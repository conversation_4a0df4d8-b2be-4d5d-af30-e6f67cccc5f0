# Stripe CLI Instructions for Local Testing

This document provides instructions on how to use the Stripe CLI for local testing of Stripe Connect functionality.

## Prerequisites

1. Install the Stripe CLI:
   - For Windows: `npm install -g stripe`
   - For macOS: `brew install stripe/stripe-cli/stripe`
   - For other platforms: See [Stripe CLI documentation](https://stripe.com/docs/stripe-cli)

2. Make sure you have a Stripe account with API keys.

## Setup

1. Login to the Stripe CLI:
   ```
   stripe login
   ```
   This will open a browser window to authenticate with Stripe.

2. Start forwarding events to your local server:
   ```
   stripe listen --forward-to http://localhost:3000/api/stripe-webhook
   ```
   This will provide a webhook signing secret that you should add to your `.env` file.

## Testing Stripe Connect

### Creating a Test Account

1. Run the `create-test-account.bat` script:
   ```
   .\create-test-account.bat
   ```
   This will:
   - Create a test Stripe Connect Express account
   - Generate an onboarding link
   - Open the onboarding link in your browser

2. Complete the onboarding process in the browser.

### Deleting a Test Account

1. Run the `delete-test-account.bat` script:
   ```
   .\delete-test-account.bat
   ```
   This will:
   - Prompt you for the account ID to delete
   - Delete the account from Stripe
   - Update the database to remove references to the account

## Troubleshooting

### HTTPS Requirement for Live Mode

Stripe requires HTTPS URLs for redirect URLs in live mode. For local testing, you should use test mode API keys.

If you're still getting the "Livemode requests must always be redirected via HTTPS" error, make sure:

1. You're using test mode API keys (they start with `sk_test_` and `pk_test_`).
2. You've restarted the server after updating the API keys.
3. You're not trying to use a live mode account ID with test mode API keys.

### Using the Stripe CLI for Direct Commands

You can use the Stripe CLI to directly interact with the Stripe API:

```
# List all accounts
stripe connect accounts list

# Get account details
stripe connect accounts get acct_123456789

# Create an account
stripe connect accounts create --type=express

# Generate an onboarding link
stripe connect onboarding-links create --account=acct_123456789 --refresh-url=http://localhost:8082/stripe-connect --return-url=http://localhost:8082/stripe-connect

# Delete an account
stripe connect accounts delete acct_123456789
```

## Additional Resources

- [Stripe CLI Documentation](https://stripe.com/docs/stripe-cli)
- [Stripe Connect Documentation](https://stripe.com/docs/connect)
- [Stripe Connect Express Documentation](https://stripe.com/docs/connect/express-accounts)