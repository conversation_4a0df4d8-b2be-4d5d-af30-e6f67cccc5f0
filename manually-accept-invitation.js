// <PERSON>ript to manually accept an invitation
import { createClient } from '@supabase/supabase-js';

// Create Supabase client with service role key for admin access
const supabaseUrl = "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFjbm90bG9qbXl2cHFiYmdveGJjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0Mzk4NzgzMywiZXhwIjoyMDU5NTYzODMzfQ.dRifBDSPK6GNYPialLfSeIQPu88lOSIsUVkynp2Be-U";

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function manuallyAcceptInvitation() {
  try {
    const token = 'c17282c1-7058-4f97-b2ef-a32159b8de5f';

    // Get the invitation
    const { data: invitation, error: invitationError } = await supabase
      .from('user_invitations')
      .select('*')
      .eq('token', token)
      .single();

    if (invitationError) {
      console.error('Error fetching invitation:', invitationError);
      return;
    }

    console.log('Invitation details:');
    console.log(invitation);

    // Get a test user
    const { data: { users }, error: usersError } = await supabase.auth.admin.listUsers();

    if (usersError) {
      console.error('Error listing users:', usersError);
      return;
    }

    // Use the first user we find
    const user = users[0];

    console.log(`Using user ${user.id} (${user.email}) to accept invitation`);

    // Call the accept_invitation function
    const { data: acceptResult, error: acceptError } = await supabase.rpc('accept_invitation', {
      token_param: token,
      user_id_param: user.id
    });

    if (acceptError) {
      console.error('Error accepting invitation:', acceptError);

      // Try direct update as fallback
      console.log('Trying direct update as fallback');

      // Update the profile
      const { error: profileUpdateError } = await supabase
        .from('profiles')
        .update({
          organization_id: invitation.organization_id,
          role: invitation.role,
          email: [user.email]
        })
        .eq('id', user.id);

      if (profileUpdateError) {
        console.error('Error updating profile:', profileUpdateError);
        return;
      }

      console.log('Successfully updated profile');

      // Mark the invitation as accepted
      const { error: invitationUpdateError } = await supabase
        .from('user_invitations')
        .update({ status: 'accepted' })
        .eq('id', invitation.id);

      if (invitationUpdateError) {
        console.error('Error marking invitation as accepted:', invitationUpdateError);
        return;
      }

      console.log('Successfully marked invitation as accepted');
    } else {
      console.log('Successfully accepted invitation:', acceptResult);
    }

    // Check the updated invitation
    const { data: updatedInvitation, error: updatedInvitationError } = await supabase
      .from('user_invitations')
      .select('*')
      .eq('token', token)
      .single();

    if (updatedInvitationError) {
      console.error('Error fetching updated invitation:', updatedInvitationError);
      return;
    }

    console.log('Updated invitation details:');
    console.log(updatedInvitation);

    // Check the updated profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('Error fetching profile:', profileError);
      return;
    }

    console.log('Updated profile details:');
    console.log(profile);

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

manuallyAcceptInvitation();
