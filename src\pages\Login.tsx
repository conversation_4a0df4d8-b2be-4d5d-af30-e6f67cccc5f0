
import { useState, useEffect } from "react";
import { Link, Navigate, useSearchParams, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import MainLayout from "@/components/layout/MainLayout";
import { useAuth } from "@/contexts/AuthContext";
import { Loader2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription } from "@/components/ui/alert";

const Login = () => {
  const [searchParams] = useSearchParams();
  const emailFromUrl = searchParams.get('email');
  const tokenFromUrl = searchParams.get('token');

  const [email, setEmail] = useState(emailFromUrl || "");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCreatingTestAccounts, setIsCreatingTestAccounts] = useState(false);
  const [error, setError] = useState("");
  const { signIn, user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  // Update email if URL parameter changes and store invitation token
  useEffect(() => {
    if (emailFromUrl) {
      setEmail(emailFromUrl);
    }

    // Store invitation token if present
    if (tokenFromUrl && emailFromUrl) {
      console.log('Storing invitation token for post-login acceptance:', tokenFromUrl);
      localStorage.setItem('pendingInvitationToken', tokenFromUrl);
      localStorage.setItem('pendingInvitationEmail', emailFromUrl);

      // Try to get organization name if possible
      const fetchOrgName = async () => {
        try {
          const { data } = await supabase
            .from('user_invitations')
            .select('organization_id')
            .eq('token', tokenFromUrl)
            .single();

          if (data?.organization_id) {
            const { data: orgData } = await supabase
              .from('organizations')
              .select('name')
              .eq('id', data.organization_id)
              .single();

            if (orgData?.name) {
              localStorage.setItem('pendingInvitationOrgName', orgData.name);
            }
          }
        } catch (error) {
          console.error('Error fetching organization name:', error);
        }
      };

      fetchOrgName();
    }
  }, [emailFromUrl, tokenFromUrl]);

  // Redirect if already logged in
  if (user) {
    // If there's a pending invitation, redirect to the confirmation page
    if (tokenFromUrl) {
      return <Navigate to="/invitation-confirmation" replace />;
    }
    // Otherwise, redirect to dashboard
    return <Navigate to="/dashboard" replace />;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email || !password) {
      setError("Email and password are required");
      return;
    }

    setError("");
    try {
      setIsSubmitting(true);
      await signIn(email, password);

      // If there's a pending invitation, redirect to the confirmation page after a short delay
      if (tokenFromUrl) {
        setTimeout(() => {
          navigate('/invitation-confirmation');
        }, 1000);
      }
      // Otherwise, it's handled by the auth context
    } catch (error) {
      console.error("Login error:", error);
      setError("Login failed. Please check your credentials and try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const createTestAccounts = async () => {
    try {
      setIsCreatingTestAccounts(true);

      // Call the edge function to create test accounts
      const { data, error } = await supabase.functions.invoke('create-test-accounts');

      if (error) {
        console.error("Error creating test accounts:", error);
        toast({
          title: "Error",
          description: "Failed to create test accounts. Check console for details.",
          variant: "destructive",
        });
        return;
      }

      console.log("Test accounts created:", data);
      toast({
        title: "Success",
        description: "Test accounts created successfully! You can now log in with the test credentials.",
      });
    } catch (err) {
      console.error("Error invoking function:", err);
      toast({
        title: "Error",
        description: "An unexpected error occurred. Check console for details.",
        variant: "destructive",
      });
    } finally {
      setIsCreatingTestAccounts(false);
    }
  };

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold">Welcome Back</h1>
            <p className="text-gray-600 mt-2">
              Log in to your Classtasker account
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-8">
            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  disabled={isSubmitting || !!emailFromUrl}
                />
                {emailFromUrl && (
                  <p className="text-sm text-gray-500 mt-1">
                    This email is from your invitation. Please create a password to continue.
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="password">Password</Label>
                  <Link
                    to="/forgot-password"
                    className="text-sm text-classtasker-blue hover:underline font-medium"
                  >
                    Forgot password?
                  </Link>
                </div>
                <Input
                  id="password"
                  type="password"
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  disabled={isSubmitting}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="remember"
                  checked={rememberMe}
                  onCheckedChange={(checked) => setRememberMe(checked as boolean)}
                />
                <Label
                  htmlFor="remember"
                  className="text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Remember me
                </Label>
              </div>

              <Button
                type="submit"
                className="w-full bg-classtasker-blue hover:bg-blue-600"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Signing in...
                  </>
                ) : (
                  "Sign In"
                )}
              </Button>
            </form>

            <div className="mt-6 text-center text-sm">
              <p className="text-gray-600">
                Don't have an account?{" "}
                <Link to="/register" className="text-classtasker-blue hover:underline font-medium">
                  Create one now
                </Link>
              </p>
            </div>


          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default Login;
