// <PERSON>ript to apply the fix for TaskCompletionActions component
const fs = require('fs');
const path = require('path');

// Paths
const componentPath = path.join(__dirname, 'src', 'components', 'tasks', 'TaskCompletionActions.tsx');
const backupPath = path.join(__dirname, 'src', 'components', 'tasks', 'TaskCompletionActions.tsx.bak');

// Create a backup of the original file
console.log('Creating backup of TaskCompletionActions.tsx...');
fs.copyFileSync(componentPath, backupPath);
console.log(`Backup created at ${backupPath}`);

// Read the file content
console.log('Reading TaskCompletionActions.tsx...');
let content = fs.readFileSync(componentPath, 'utf8');

// Add the useAuth import
if (!content.includes('import { useAuth }')) {
  console.log('Adding useAuth import...');
  content = content.replace(
    /import { useState } from 'react';/,
    `import { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';`
  );
}

// Add the isAdmin check
if (!content.includes('const { isAdmin }')) {
  console.log('Adding isAdmin check...');
  content = content.replace(
    /const { toast } = useToast\(\);/,
    `const { toast } = useToast();\n  const { isAdmin } = useAuth();`
  );
}

// Add isAdmin to the debug info
if (!content.includes('isAdmin')) {
  console.log('Adding isAdmin to debug info...');
  content = content.replace(
    /offers: acceptedOffer \? 'Has accepted offer' : 'No accepted offer'/,
    `offers: acceptedOffer ? 'Has accepted offer' : 'No accepted offer',\n    isAdmin`
  );
}

// Update the component comment
if (content.includes('// Only show this component for task owners with assigned tasks')) {
  console.log('Updating component comment...');
  content = content.replace(
    /\/\/ Only show this component for task owners with assigned tasks/,
    `// Only show this component for task owners or admins with assigned tasks`
  );
}

// Write the modified content back to the file
fs.writeFileSync(componentPath, content, 'utf8');
console.log('Fix applied successfully!');

console.log('\nChanges made:');
console.log('1. Added useAuth import');
console.log('2. Added isAdmin check');
console.log('3. Added isAdmin to debug info');
console.log('4. Updated component comment');

console.log('\nNote: This fix only adds the isAdmin variable to the component.');
console.log('The component will still be shown based on the condition in FixedTask.tsx:');
console.log('{(isTaskOwner || isAdmin) && (');
console.log('\nMake sure you have also applied the fix to FixedTask.tsx using:');
console.log('node apply-task-completion-fix.cjs');

console.log('\nIf you need to restore the original file, run:');
console.log(`cp ${backupPath} ${componentPath}`);