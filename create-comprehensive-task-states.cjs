// Script to create 7 tasks in different states covering the complete task lifecycle
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const Stripe = require('stripe');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Initialize Stripe (for payment-related tasks)
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
let stripe;

if (stripeSecretKey) {
  stripe = new Stripe(stripeSecretKey, {
    apiVersion: '2023-10-16',
  });
}

// Email addresses
const teacherEmail = '<EMAIL>';
const maintenanceEmail = '<EMAIL>';
const supportEmail = '<EMAIL>';
const supplierEmail = '<EMAIL>';

// Function to get user ID by email
async function getUserIdByEmail(email) {
  const { data: authData } = await supabase.auth.admin.listUsers();
  const user = authData.users.find(u => u.email === email);
  return user ? user.id : null;
}

async function createComprehensiveTaskStates() {
  try {
    console.log('Creating 7 tasks in different states covering the complete task lifecycle...');
    
    // Step 1: Get user IDs
    console.log('\nStep 1: Get user IDs');
    
    const teacherId = await getUserIdByEmail(teacherEmail);
    if (!teacherId) {
      console.error(`Teacher with email ${teacherEmail} not found.`);
      return;
    }
    
    const maintenanceId = await getUserIdByEmail(maintenanceEmail);
    if (!maintenanceId) {
      console.error(`Maintenance staff with email ${maintenanceEmail} not found.`);
      return;
    }
    
    const supportId = await getUserIdByEmail(supportEmail);
    if (!supportId) {
      console.error(`Support staff with email ${supportEmail} not found.`);
      return;
    }
    
    const supplierId = await getUserIdByEmail(supplierEmail);
    if (!supplierId) {
      console.error(`Supplier with email ${supplierEmail} not found.`);
      return;
    }
    
    console.log(`Teacher ID: ${teacherId}`);
    console.log(`Maintenance Staff ID: ${maintenanceId}`);
    console.log(`Support Staff ID: ${supportId}`);
    console.log(`Supplier ID: ${supplierId}`);
    
    // Step 2: Create Task 1 - Pending Review
    console.log('\nStep 2: Create Task 1 - Pending Review');
    
    const task1Data = {
      title: 'Replace Cafeteria Tables',
      description: 'The cafeteria needs new tables as the current ones are damaged and unstable. We need approximately 10 new tables.',
      location: 'School Cafeteria',
      category: 'Equipment Installation',
      budget: 2000.00,
      due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
      user_id: teacherId,
      status: 'open',
      visibility: 'admin', // Default for new tasks
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    const { data: task1, error: task1Error } = await supabase
      .from('tasks')
      .insert(task1Data)
      .select()
      .single();
    
    if (task1Error) {
      console.error('Error creating Task 1:', task1Error);
      return;
    }
    
    console.log(`Created Task 1 (Pending Review): ${task1.title} (${task1.id})`);
    console.log(`- Status: ${task1.status}`);
    console.log(`- Visibility: ${task1.visibility}`);
    
    // Step 3: Create Task 2 - Assigned to Maintenance
    console.log('\nStep 3: Create Task 2 - Assigned to Maintenance');
    
    // First create the task as if submitted by a teacher
    const task2InitialData = {
      title: 'Repair Gymnasium Floor',
      description: 'There are several damaged boards on the gymnasium floor that need to be replaced. The area is approximately 10 square feet.',
      location: 'School Gymnasium',
      category: 'Maintenance',
      budget: 300.00,
      due_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days from now
      user_id: teacherId,
      status: 'open',
      visibility: 'admin', // Initial visibility
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    const { data: task2Initial, error: task2InitialError } = await supabase
      .from('tasks')
      .insert(task2InitialData)
      .select()
      .single();
    
    if (task2InitialError) {
      console.error('Error creating initial Task 2:', task2InitialError);
      return;
    }
    
    // Now update it as if an admin assigned it to maintenance
    const task2UpdateData = {
      status: 'assigned',
      visibility: 'internal',
      assigned_to: maintenanceId,
      payment_status: 'not_required',
      updated_at: new Date().toISOString(),
    };
    
    const { data: task2, error: task2Error } = await supabase
      .from('tasks')
      .update(task2UpdateData)
      .eq('id', task2Initial.id)
      .select()
      .single();
    
    if (task2Error) {
      console.error('Error updating Task 2:', task2Error);
      return;
    }
    
    console.log(`Created Task 2 (Assigned to Maintenance): ${task2.title} (${task2.id})`);
    console.log(`- Status: ${task2.status}`);
    console.log(`- Visibility: ${task2.visibility}`);
    console.log(`- Assigned to: ${task2.assigned_to}`);
    
    // Step 4: Create Task 3 - Assigned to Support
    console.log('\nStep 4: Create Task 3 - Assigned to Support');
    
    // First create the task as if submitted by a teacher
    const task3InitialData = {
      title: 'Install Smart Board',
      description: 'We have a new smart board that needs to be installed in the science classroom. It requires mounting on the wall and connecting to the school network.',
      location: 'Science Classroom',
      category: 'IT Support',
      budget: 100.00,
      due_date: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days from now
      user_id: teacherId,
      status: 'open',
      visibility: 'admin', // Initial visibility
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    const { data: task3Initial, error: task3InitialError } = await supabase
      .from('tasks')
      .insert(task3InitialData)
      .select()
      .single();
    
    if (task3InitialError) {
      console.error('Error creating initial Task 3:', task3InitialError);
      return;
    }
    
    // Now update it as if an admin assigned it to support
    const task3UpdateData = {
      status: 'assigned',
      visibility: 'internal',
      assigned_to: supportId,
      payment_status: 'not_required',
      updated_at: new Date().toISOString(),
    };
    
    const { data: task3, error: task3Error } = await supabase
      .from('tasks')
      .update(task3UpdateData)
      .eq('id', task3Initial.id)
      .select()
      .single();
    
    if (task3Error) {
      console.error('Error updating Task 3:', task3Error);
      return;
    }
    
    console.log(`Created Task 3 (Assigned to Support): ${task3.title} (${task3.id})`);
    console.log(`- Status: ${task3.status}`);
    console.log(`- Visibility: ${task3.visibility}`);
    console.log(`- Assigned to: ${task3.assigned_to}`);
    
    // Step 5: Create Task 4 - Public (Open)
    console.log('\nStep 5: Create Task 4 - Public (Open)');
    
    // First create the task as if submitted by a teacher
    const task4InitialData = {
      title: 'Paint School Exterior',
      description: 'The exterior of the school building needs to be repainted. The total area is approximately 5000 square feet. We need a professional painting service.',
      location: 'School Exterior',
      category: 'Painting',
      budget: 3000.00,
      due_date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(), // 60 days from now
      user_id: teacherId,
      status: 'open',
      visibility: 'admin', // Initial visibility
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    const { data: task4Initial, error: task4InitialError } = await supabase
      .from('tasks')
      .insert(task4InitialData)
      .select()
      .single();
    
    if (task4InitialError) {
      console.error('Error creating initial Task 4:', task4InitialError);
      return;
    }
    
    // Now update it as if an admin made it public
    const task4UpdateData = {
      status: 'open', // Remains open for suppliers to bid
      visibility: 'public',
      assigned_to: null, // No specific assignment for public tasks
      updated_at: new Date().toISOString(),
    };
    
    const { data: task4, error: task4Error } = await supabase
      .from('tasks')
      .update(task4UpdateData)
      .eq('id', task4Initial.id)
      .select()
      .single();
    
    if (task4Error) {
      console.error('Error updating Task 4:', task4Error);
      return;
    }
    
    console.log(`Created Task 4 (Public - Open): ${task4.title} (${task4.id})`);
    console.log(`- Status: ${task4.status}`);
    console.log(`- Visibility: ${task4.visibility}`);
    
    // Step 6: Create Task 5 - Public with Offer (same budget)
    console.log('\nStep 6: Create Task 5 - Public with Offer (same budget)');
    
    // First create the task as if submitted by a teacher
    const task5InitialData = {
      title: 'Replace Classroom Carpeting',
      description: 'Three classrooms need new carpeting. The total area is approximately 1200 square feet. The old carpet needs to be removed and disposed of properly.',
      location: 'Classrooms 101, 102, 103',
      category: 'Maintenance',
      budget: 2500.00,
      due_date: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(), // 45 days from now
      user_id: teacherId,
      status: 'open',
      visibility: 'admin', // Initial visibility
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    const { data: task5Initial, error: task5InitialError } = await supabase
      .from('tasks')
      .insert(task5InitialData)
      .select()
      .single();
    
    if (task5InitialError) {
      console.error('Error creating initial Task 5:', task5InitialError);
      return;
    }
    
    // Now update it as if an admin made it public
    const task5UpdateData = {
      status: 'open', // Remains open for suppliers to bid
      visibility: 'public',
      assigned_to: null, // No specific assignment for public tasks
      updated_at: new Date().toISOString(),
    };
    
    const { data: task5, error: task5Error } = await supabase
      .from('tasks')
      .update(task5UpdateData)
      .eq('id', task5Initial.id)
      .select()
      .single();
    
    if (task5Error) {
      console.error('Error updating Task 5:', task5Error);
      return;
    }
    
    // Now create an offer from the supplier at the same budget
    const offer5Data = {
      task_id: task5.id,
      user_id: supplierId,
      amount: task5.budget, // Same as the budget
      message: 'I can complete this carpet replacement project within your budget. I have experience with similar school projects and can work during off-hours to minimize disruption.',
      status: 'awaiting', // Initial status for offers
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    const { data: offer5, error: offer5Error } = await supabase
      .from('offers')
      .insert(offer5Data)
      .select()
      .single();
    
    if (offer5Error) {
      console.error('Error creating offer for Task 5:', offer5Error);
      return;
    }
    
    console.log(`Created Task 5 (Public with Offer): ${task5.title} (${task5.id})`);
    console.log(`- Status: ${task5.status}`);
    console.log(`- Visibility: ${task5.visibility}`);
    console.log(`- Offer: £${offer5.amount} from ${supplierEmail}`);
    console.log(`- Offer status: ${offer5.status}`);
    
    // Step 7: Create Task 6 - Public with Counter Offer (different amount)
    console.log('\nStep 7: Create Task 6 - Public with Counter Offer (different amount)');
    
    // First create the task as if submitted by a teacher
    const task6InitialData = {
      title: 'Repair School Roof Leak',
      description: 'There is a leak in the roof above the library that needs to be repaired before it causes water damage to books and equipment.',
      location: 'School Library Roof',
      category: 'Maintenance',
      budget: 1000.00,
      due_date: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000).toISOString(), // 20 days from now
      user_id: teacherId,
      status: 'open',
      visibility: 'admin', // Initial visibility
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    const { data: task6Initial, error: task6InitialError } = await supabase
      .from('tasks')
      .insert(task6InitialData)
      .select()
      .single();
    
    if (task6InitialError) {
      console.error('Error creating initial Task 6:', task6InitialError);
      return;
    }
    
    // Now update it as if an admin made it public
    const task6UpdateData = {
      status: 'open', // Remains open for suppliers to bid
      visibility: 'public',
      assigned_to: null, // No specific assignment for public tasks
      updated_at: new Date().toISOString(),
    };
    
    const { data: task6, error: task6Error } = await supabase
      .from('tasks')
      .update(task6UpdateData)
      .eq('id', task6Initial.id)
      .select()
      .single();
    
    if (task6Error) {
      console.error('Error updating Task 6:', task6Error);
      return;
    }
    
    // Now create an offer from the supplier with a different amount (higher)
    const offer6Data = {
      task_id: task6.id,
      user_id: supplierId,
      amount: task6.budget + 200, // Higher than the budget
      message: 'After reviewing the details, I need to quote a higher price due to the complexity of roof repairs and potential water damage. I can start immediately and guarantee the work for 2 years.',
      status: 'awaiting', // Initial status for offers
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    const { data: offer6, error: offer6Error } = await supabase
      .from('offers')
      .insert(offer6Data)
      .select()
      .single();
    
    if (offer6Error) {
      console.error('Error creating offer for Task 6:', offer6Error);
      return;
    }
    
    console.log(`Created Task 6 (Public with Counter Offer): ${task6.title} (${task6.id})`);
    console.log(`- Status: ${task6.status}`);
    console.log(`- Visibility: ${task6.visibility}`);
    console.log(`- Budget: £${task6.budget}`);
    console.log(`- Offer: £${offer6.amount} from ${supplierEmail}`);
    console.log(`- Offer status: ${offer6.status}`);
    
    // Step 8: Create Task 7 - Public with Accepted Offer and Assigned
    console.log('\nStep 8: Create Task 7 - Public with Accepted Offer and Assigned');
    
    // First create the task as if submitted by a teacher
    const task7InitialData = {
      title: 'Install New Playground Equipment',
      description: 'We need to install new playground equipment including swings, slides, and climbing structures. The equipment has been purchased and is on-site.',
      location: 'School Playground',
      category: 'Equipment Installation',
      budget: 1500.00,
      due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
      user_id: teacherId,
      status: 'open',
      visibility: 'admin', // Initial visibility
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    const { data: task7Initial, error: task7InitialError } = await supabase
      .from('tasks')
      .insert(task7InitialData)
      .select()
      .single();
    
    if (task7InitialError) {
      console.error('Error creating initial Task 7:', task7InitialError);
      return;
    }
    
    // Now update it as if an admin made it public
    const task7UpdateData = {
      status: 'open', // Remains open for suppliers to bid
      visibility: 'public',
      assigned_to: null, // No specific assignment for public tasks
      updated_at: new Date().toISOString(),
    };
    
    const { data: task7Public, error: task7PublicError } = await supabase
      .from('tasks')
      .update(task7UpdateData)
      .eq('id', task7Initial.id)
      .select()
      .single();
    
    if (task7PublicError) {
      console.error('Error updating Task 7 to public:', task7PublicError);
      return;
    }
    
    // Now create an offer from the supplier
    const offer7Data = {
      task_id: task7Public.id,
      user_id: supplierId,
      amount: task7Public.budget, // Same as the budget
      message: 'I can install the playground equipment according to safety standards. I have experience with similar installations at other schools.',
      status: 'awaiting', // Initial status for offers
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    const { data: offer7, error: offer7Error } = await supabase
      .from('offers')
      .insert(offer7Data)
      .select()
      .single();
    
    if (offer7Error) {
      console.error('Error creating offer for Task 7:', offer7Error);
      return;
    }
    
    // Now update the offer to accepted
    const offer7UpdateData = {
      status: 'accepted',
      updated_at: new Date().toISOString(),
    };
    
    const { data: acceptedOffer7, error: acceptedOffer7Error } = await supabase
      .from('offers')
      .update(offer7UpdateData)
      .eq('id', offer7.id)
      .select()
      .single();
    
    if (acceptedOffer7Error) {
      console.error('Error accepting offer for Task 7:', acceptedOffer7Error);
      return;
    }
    
    // Now update the task to assigned
    const task7AssignData = {
      status: 'assigned',
      assigned_to: supplierId, // Assign to the supplier
      updated_at: new Date().toISOString(),
    };
    
    const { data: task7, error: task7Error } = await supabase
      .from('tasks')
      .update(task7AssignData)
      .eq('id', task7Public.id)
      .select()
      .single();
    
    if (task7Error) {
      console.error('Error assigning Task 7:', task7Error);
      return;
    }
    
    console.log(`Created Task 7 (Public with Accepted Offer): ${task7.title} (${task7.id})`);
    console.log(`- Status: ${task7.status}`);
    console.log(`- Visibility: ${task7.visibility}`);
    console.log(`- Assigned to: ${task7.assigned_to}`);
    console.log(`- Offer: £${acceptedOffer7.amount} from ${supplierEmail}`);
    console.log(`- Offer status: ${acceptedOffer7.status}`);
    
    // Step 9: Verify tasks appear in the correct sections
    console.log('\nStep 9: Verify tasks appear in the correct sections');
    
    // Check admin review section (should contain Task 1)
    const { data: adminReviewTasks, error: adminReviewTasksError } = await supabase
      .from('tasks')
      .select('*')
      .eq('status', 'open')
      .eq('visibility', 'admin')
      .order('created_at', { ascending: false });
    
    if (adminReviewTasksError) {
      console.error('Error fetching admin review tasks:', adminReviewTasksError);
      return;
    }
    
    const task1InReview = adminReviewTasks.some(task => task.id === task1.id);
    console.log(`Task 1 "${task1.title}" in admin review section: ${task1InReview ? 'Yes ✅' : 'No ❌'}`);
    
    // Check assigned tasks section (should contain Tasks 2-7)
    const { data: assignedTasks, error: assignedTasksError } = await supabase
      .from('tasks')
      .select('*')
      .or('status.eq.assigned,visibility.eq.public,visibility.eq.internal')
      .order('created_at', { ascending: false });
    
    if (assignedTasksError) {
      console.error('Error fetching assigned tasks:', assignedTasksError);
      return;
    }
    
    const task2InAssigned = assignedTasks.some(task => task.id === task2.id);
    const task3InAssigned = assignedTasks.some(task => task.id === task3.id);
    const task4InAssigned = assignedTasks.some(task => task.id === task4.id);
    const task5InAssigned = assignedTasks.some(task => task.id === task5.id);
    const task6InAssigned = assignedTasks.some(task => task.id === task6.id);
    const task7InAssigned = assignedTasks.some(task => task.id === task7.id);
    
    console.log(`Task 2 "${task2.title}" in assigned tasks section: ${task2InAssigned ? 'Yes ✅' : 'No ❌'}`);
    console.log(`Task 3 "${task3.title}" in assigned tasks section: ${task3InAssigned ? 'Yes ✅' : 'No ❌'}`);
    console.log(`Task 4 "${task4.title}" in assigned tasks section: ${task4InAssigned ? 'Yes ✅' : 'No ❌'}`);
    console.log(`Task 5 "${task5.title}" in assigned tasks section: ${task5InAssigned ? 'Yes ✅' : 'No ❌'}`);
    console.log(`Task 6 "${task6.title}" in assigned tasks section: ${task6InAssigned ? 'Yes ✅' : 'No ❌'}`);
    console.log(`Task 7 "${task7.title}" in assigned tasks section: ${task7InAssigned ? 'Yes ✅' : 'No ❌'}`);
    
    console.log('\nAll tasks created successfully!');
    console.log('Summary:');
    console.log(`1. Task 1 (Pending Review): ${task1.title} (${task1.id})`);
    console.log(`   - Status: ${task1.status}`);
    console.log(`   - Visibility: ${task1.visibility}`);
    
    console.log(`2. Task 2 (Assigned to Maintenance): ${task2.title} (${task2.id})`);
    console.log(`   - Status: ${task2.status}`);
    console.log(`   - Visibility: ${task2.visibility}`);
    console.log(`   - Assigned to: ${task2.assigned_to} (Maintenance)`);
    
    console.log(`3. Task 3 (Assigned to Support): ${task3.title} (${task3.id})`);
    console.log(`   - Status: ${task3.status}`);
    console.log(`   - Visibility: ${task3.visibility}`);
    console.log(`   - Assigned to: ${task3.assigned_to} (Support)`);
    
    console.log(`4. Task 4 (Public - Open): ${task4.title} (${task4.id})`);
    console.log(`   - Status: ${task4.status}`);
    console.log(`   - Visibility: ${task4.visibility}`);
    
    console.log(`5. Task 5 (Public with Offer): ${task5.title} (${task5.id})`);
    console.log(`   - Status: ${task5.status}`);
    console.log(`   - Visibility: ${task5.visibility}`);
    console.log(`   - Offer: £${offer5.amount} (same as budget)`);
    console.log(`   - Offer status: ${offer5.status}`);
    
    console.log(`6. Task 6 (Public with Counter Offer): ${task6.title} (${task6.id})`);
    console.log(`   - Status: ${task6.status}`);
    console.log(`   - Visibility: ${task6.visibility}`);
    console.log(`   - Budget: £${task6.budget}`);
    console.log(`   - Offer: £${offer6.amount} (higher than budget)`);
    console.log(`   - Offer status: ${offer6.status}`);
    
    console.log(`7. Task 7 (Public with Accepted Offer): ${task7.title} (${task7.id})`);
    console.log(`   - Status: ${task7.status}`);
    console.log(`   - Visibility: ${task7.visibility}`);
    console.log(`   - Assigned to: ${task7.assigned_to} (Supplier)`);
    console.log(`   - Offer: £${acceptedOffer7.amount}`);
    console.log(`   - Offer status: ${acceptedOffer7.status}`);
    
    console.log('\nYou can now log in to the dashboard to see these tasks in their respective sections:');
    console.log('- Admin: <EMAIL>');
    console.log('- Maintenance: <EMAIL>');
    console.log('- Support: <EMAIL>');
    console.log('- Teacher: <EMAIL>');
    console.log('- Supplier: <EMAIL>');
    
    return {
      success: true,
      tasks: {
        pendingReview: task1,
        assignedToMaintenance: task2,
        assignedToSupport: task3,
        publicOpen: task4,
        publicWithOffer: { task: task5, offer: offer5 },
        publicWithCounterOffer: { task: task6, offer: offer6 },
        publicWithAcceptedOffer: { task: task7, offer: acceptedOffer7 }
      }
    };
  } catch (error) {
    console.error('Error creating comprehensive task states:', error);
    return {
      success: false,
      error
    };
  }
}

createComprehensiveTaskStates();
