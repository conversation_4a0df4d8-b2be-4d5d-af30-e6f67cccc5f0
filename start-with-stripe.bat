@echo off
REM Start script for running the application with Stripe Connect integration on Windows

REM Check if .env file exists
if not exist .env (
  echo Error: .env file not found.
  echo Please create a .env file with your environment variables.
  exit /b 1
)

REM Check if .env.local file exists
if not exist .env.local (
  echo Warning: .env.local file not found.
  echo The frontend may not work correctly without the required environment variables.
)

echo Checking for required environment variables...
REM Note: We can't easily check environment variables in a batch file
REM but we can remind the user what's needed
echo Please ensure you have the following variables set:
echo - STRIPE_SECRET_KEY and STRIPE_WEBHOOK_SECRET in .env
echo - VITE_STRIPE_PUBLIC_KEY in .env.local

REM Apply Stripe Connect database setup
echo Applying Stripe Connect database setup...
call npm run setup-stripe-connect

REM Check if the setup was successful
if %ERRORLEVEL% neq 0 (
  echo Error: Failed to apply Stripe Connect database setup.
  exit /b 1
)

REM Start the application with the server
echo Starting the application with Stripe Connect integration...
call npm run dev:all

REM Exit with the status of the last command
exit /b %ERRORLEVEL%