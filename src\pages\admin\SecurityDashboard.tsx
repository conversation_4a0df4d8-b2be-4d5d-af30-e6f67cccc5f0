/**
 * Security Dashboard for monitoring security events and threats
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { getSecurityLogs, clearSecurityLogs, SecurityEvent, logSecurityEvent } from '@/utils/securityMonitoring';
import { Shield, AlertTriangle, Eye, Trash2, Download, RefreshCw, TestTube, Zap } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

const SecurityDashboard: React.FC = () => {
  const [securityLogs, setSecurityLogs] = useState<SecurityEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [testingAlert, setTestingAlert] = useState(false);
  const { toast } = useToast();

  // Load security logs
  const loadSecurityLogs = () => {
    setLoading(true);
    try {
      const logs = getSecurityLogs();
      setSecurityLogs(logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()));
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Error loading security logs:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSecurityLogs();

    // Auto-refresh every 30 seconds
    const interval = setInterval(loadSecurityLogs, 30000);
    return () => clearInterval(interval);
  }, []);

  // Clear all logs
  const handleClearLogs = () => {
    if (confirm('Are you sure you want to clear all security logs? This action cannot be undone.')) {
      clearSecurityLogs();
      setSecurityLogs([]);
    }
  };

  // Export logs
  const handleExportLogs = () => {
    const dataStr = JSON.stringify(securityLogs, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `security-logs-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  // Test security alert system
  const handleTestSecurityAlert = async () => {
    setTestingAlert(true);
    try {
      // Create a test critical security event
      const testEvent: Omit<SecurityEvent, 'timestamp'> = {
        type: 'suspicious_activity',
        severity: 'critical',
        userEmail: '<EMAIL>',
        action: 'security_system_test',
        resource: 'security_dashboard',
        details: {
          message: 'Manual security alert test triggered by admin',
          testType: 'critical_alert_test',
          triggeredBy: 'security_dashboard'
        },
        ipAddress: '127.0.0.1',
        userAgent: 'Security Dashboard Test'
      };

      // Log the event locally first
      await logSecurityEvent(testEvent);

      // Try to call the Edge Function
      try {
        const { data, error } = await supabase.functions.invoke('security-alert', {
          body: { event: { ...testEvent, timestamp: new Date().toISOString() } }
        });

        if (error) {
          console.error('Edge function error:', error);
          toast({
            title: 'Test Alert Created',
            description: 'Test security event logged locally. Edge Function may need deployment for email alerts.',
            variant: 'default',
          });
        } else {
          toast({
            title: 'Security Alert Test Successful!',
            description: 'Critical security alert triggered and email notifications sent to admins.',
            variant: 'default',
          });
        }
      } catch (edgeFunctionError) {
        console.error('Edge function not available:', edgeFunctionError);
        toast({
          title: 'Test Alert Created',
          description: 'Test security event logged locally. Deploy the Edge Function for email alerts.',
          variant: 'default',
        });
      }

      // Refresh the logs to show the new event
      setTimeout(() => {
        loadSecurityLogs();
      }, 1000);

    } catch (error) {
      console.error('Error testing security alert:', error);
      toast({
        title: 'Test Failed',
        description: 'Failed to create test security alert.',
        variant: 'destructive',
      });
    } finally {
      setTestingAlert(false);
    }
  };

  // Insert test security breach
  const handleTestSecurityBreach = async () => {
    setTestingAlert(true);
    try {
      // Insert multiple test events to simulate a security breach
      const breachEvents = [
        {
          type: 'auth_failure' as const,
          severity: 'high' as const,
          userEmail: '<EMAIL>',
          action: 'multiple_failed_login_attempts',
          resource: 'authentication_system',
          details: {
            attempts: 15,
            timeWindow: '5 minutes',
            ipAddress: '*************',
            userAgent: 'Automated Attack Tool'
          }
        },
        {
          type: 'suspicious_activity' as const,
          severity: 'critical' as const,
          userEmail: 'unknown',
          action: 'sql_injection_attempt',
          resource: 'user_database',
          details: {
            query: "'; DROP TABLE users; --",
            blocked: true,
            source: 'contact_form'
          }
        },
        {
          type: 'permission_denied' as const,
          severity: 'high' as const,
          userEmail: '<EMAIL>',
          action: 'privilege_escalation_attempt',
          resource: 'admin_panel',
          details: {
            attemptedRole: 'site_admin',
            currentRole: 'teacher',
            endpoint: '/admin/users'
          }
        }
      ];

      // Log all breach events
      for (const event of breachEvents) {
        await logSecurityEvent(event);

        // Also try to send to Edge Function
        try {
          await supabase.functions.invoke('security-alert', {
            body: { event: { ...event, timestamp: new Date().toISOString() } }
          });
        } catch (error) {
          console.log('Edge function not available for event:', event.action);
        }

        // Small delay between events
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      toast({
        title: 'Security Breach Simulation Complete!',
        description: `${breachEvents.length} security events created to test the monitoring system.`,
        variant: 'default',
      });

      // Refresh the logs
      setTimeout(() => {
        loadSecurityLogs();
      }, 2000);

    } catch (error) {
      console.error('Error simulating security breach:', error);
      toast({
        title: 'Simulation Failed',
        description: 'Failed to create security breach simulation.',
        variant: 'destructive',
      });
    } finally {
      setTestingAlert(false);
    }
  };

  // Get severity color
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  // Get event type icon
  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'auth_failure': return '🔐';
      case 'permission_denied': return '🚫';
      case 'suspicious_activity': return '⚠️';
      case 'data_access': return '📊';
      case 'admin_action': return '👑';
      default: return '🔍';
    }
  };

  // Filter logs by severity
  const filterLogsBySeverity = (severity: string) => {
    return securityLogs.filter(log => log.severity === severity);
  };

  // Get recent critical events
  const criticalEvents = filterLogsBySeverity('critical').slice(0, 5);
  const highEvents = filterLogsBySeverity('high').slice(0, 10);

  // Security statistics
  const stats = {
    total: securityLogs.length,
    critical: filterLogsBySeverity('critical').length,
    high: filterLogsBySeverity('high').length,
    medium: filterLogsBySeverity('medium').length,
    low: filterLogsBySeverity('low').length,
    last24h: securityLogs.filter(log =>
      new Date(log.timestamp).getTime() > Date.now() - 24 * 60 * 60 * 1000
    ).length
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Shield className="h-8 w-8 text-blue-600" />
            Security Dashboard
          </h1>
          <p className="text-gray-600 mt-2">
            Monitor security events and threats in real-time
          </p>
        </div>
        <div className="flex gap-2 flex-wrap">
          <Button onClick={loadSecurityLogs} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={handleExportLogs} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button
            onClick={handleTestSecurityAlert}
            variant="secondary"
            size="sm"
            disabled={testingAlert}
          >
            <TestTube className="h-4 w-4 mr-2" />
            {testingAlert ? 'Testing...' : 'Test Alert'}
          </Button>
          <Button
            onClick={handleTestSecurityBreach}
            variant="secondary"
            size="sm"
            disabled={testingAlert}
          >
            <Zap className="h-4 w-4 mr-2" />
            {testingAlert ? 'Simulating...' : 'Simulate Breach'}
          </Button>
          <Button onClick={handleClearLogs} variant="destructive" size="sm">
            <Trash2 className="h-4 w-4 mr-2" />
            Clear Logs
          </Button>
        </div>
      </div>

      {/* Security Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{stats.total}</div>
            <div className="text-sm text-gray-600">Total Events</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">{stats.critical}</div>
            <div className="text-sm text-gray-600">Critical</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-orange-600">{stats.high}</div>
            <div className="text-sm text-gray-600">High</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">{stats.medium}</div>
            <div className="text-sm text-gray-600">Medium</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">{stats.low}</div>
            <div className="text-sm text-gray-600">Low</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{stats.last24h}</div>
            <div className="text-sm text-gray-600">Last 24h</div>
          </CardContent>
        </Card>
      </div>

      {/* Critical Alerts */}
      {criticalEvents.length > 0 && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertTitle className="text-red-800">Critical Security Events Detected</AlertTitle>
          <AlertDescription className="text-red-700">
            {criticalEvents.length} critical security event(s) require immediate attention.
          </AlertDescription>
        </Alert>
      )}

      {/* Security Events Tabs */}
      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="all">All Events</TabsTrigger>
          <TabsTrigger value="critical">Critical</TabsTrigger>
          <TabsTrigger value="high">High</TabsTrigger>
          <TabsTrigger value="medium">Medium</TabsTrigger>
          <TabsTrigger value="low">Low</TabsTrigger>
          <TabsTrigger value="realtime">Real-time</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <SecurityEventsList events={securityLogs} />
        </TabsContent>

        <TabsContent value="critical" className="space-y-4">
          <SecurityEventsList events={filterLogsBySeverity('critical')} />
        </TabsContent>

        <TabsContent value="high" className="space-y-4">
          <SecurityEventsList events={filterLogsBySeverity('high')} />
        </TabsContent>

        <TabsContent value="medium" className="space-y-4">
          <SecurityEventsList events={filterLogsBySeverity('medium')} />
        </TabsContent>

        <TabsContent value="low" className="space-y-4">
          <SecurityEventsList events={filterLogsBySeverity('low')} />
        </TabsContent>

        <TabsContent value="realtime" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Real-time Monitoring
              </CardTitle>
              <CardDescription>
                Live security event monitoring (auto-refreshes every 30 seconds)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-sm text-gray-600 mb-4">
                Last refresh: {lastRefresh.toLocaleTimeString()}
              </div>
              <SecurityEventsList events={securityLogs.slice(0, 20)} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Security Events List Component
const SecurityEventsList: React.FC<{ events: SecurityEvent[] }> = ({ events }) => {
  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'auth_failure': return '🔐';
      case 'permission_denied': return '🚫';
      case 'suspicious_activity': return '⚠️';
      case 'data_access': return '📊';
      case 'admin_action': return '👑';
      default: return '🔍';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  if (events.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Security Events</h3>
          <p className="text-gray-600">No security events found for this filter.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-3">
      {events.map((event, index) => (
        <Card key={index} className="hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3 flex-1">
                <div className="text-2xl">{getEventTypeIcon(event.type)}</div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <Badge className={`${getSeverityColor(event.severity)} text-white`}>
                      {event.severity.toUpperCase()}
                    </Badge>
                    <span className="text-sm font-medium">{event.action}</span>
                  </div>
                  <div className="text-sm text-gray-600 mb-2">
                    {event.userEmail || event.userId || 'Unknown user'}
                    {event.resource && ` • ${event.resource}`}
                  </div>
                  {event.details && (
                    <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
                      <pre>{JSON.stringify(event.details, null, 2)}</pre>
                    </div>
                  )}
                </div>
              </div>
              <div className="text-right text-sm text-gray-500">
                <div>{new Date(event.timestamp).toLocaleDateString()}</div>
                <div>{new Date(event.timestamp).toLocaleTimeString()}</div>
                {event.ipAddress && (
                  <div className="text-xs">IP: {event.ipAddress}</div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default SecurityDashboard;
