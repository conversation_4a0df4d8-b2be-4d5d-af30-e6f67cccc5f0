/**
 * TaskTypeDebug
 * 
 * This page is used to test the task type system.
 * It displays information about tasks and their types.
 */

import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { supabase } from '@/integrations/supabase/client';
import { TaskFactory } from '@/services/taskFactory';
import { Task, isInternalTask, isExternalTask } from '@/types/tasks';
import { Loader2 } from 'lucide-react';

const TaskTypeDebug: React.FC = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTasks = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch tasks from the database
        const { data, error } = await supabase
          .from('tasks')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(20);

        if (error) throw new Error(error.message);

        // Convert to typed tasks using TaskFactory
        const typedTasks = data.map(task => TaskFactory.createFromDatabase(task));
        setTasks(typedTasks);
      } catch (err: any) {
        console.error('Error fetching tasks:', err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTasks();
  }, []);

  // Count internal and external tasks
  const internalTasks = tasks.filter(isInternalTask);
  const externalTasks = tasks.filter(isExternalTask);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Task Type Debug</h1>
        <p className="text-gray-600">
          This page displays information about tasks and their types.
        </p>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          <span className="ml-2">Loading tasks...</span>
        </div>
      ) : error ? (
        <Card className="mb-6 border-red-300">
          <CardHeader>
            <CardTitle className="text-red-600">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error}</p>
            <Button 
              className="mt-4" 
              onClick={() => window.location.reload()}
            >
              Retry
            </Button>
          </CardContent>
        </Card>
      ) : (
        <>
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Task Type Summary</CardTitle>
              <CardDescription>
                Overview of task types in the system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gray-100 p-4 rounded-lg">
                  <h3 className="font-medium">Total Tasks</h3>
                  <p className="text-2xl font-bold">{tasks.length}</p>
                </div>
                <div className="bg-blue-100 p-4 rounded-lg">
                  <h3 className="font-medium">Internal Tasks</h3>
                  <p className="text-2xl font-bold">{internalTasks.length}</p>
                </div>
                <div className="bg-amber-100 p-4 rounded-lg">
                  <h3 className="font-medium">External Tasks</h3>
                  <p className="text-2xl font-bold">{externalTasks.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Tabs defaultValue="all">
            <TabsList className="mb-4">
              <TabsTrigger value="all">All Tasks</TabsTrigger>
              <TabsTrigger value="internal">Internal Tasks</TabsTrigger>
              <TabsTrigger value="external">External Tasks</TabsTrigger>
            </TabsList>
            
            <TabsContent value="all">
              <TaskList tasks={tasks} />
            </TabsContent>
            
            <TabsContent value="internal">
              <TaskList tasks={internalTasks} />
            </TabsContent>
            
            <TabsContent value="external">
              <TaskList tasks={externalTasks} />
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  );
};

interface TaskListProps {
  tasks: Task[];
}

const TaskList: React.FC<TaskListProps> = ({ tasks }) => {
  if (tasks.length === 0) {
    return (
      <Card>
        <CardContent className="py-8 text-center">
          <p className="text-gray-500">No tasks found</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {tasks.map(task => (
        <Card key={task.id} className="overflow-hidden">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle>{task.title}</CardTitle>
              <div className="flex space-x-2">
                <Badge 
                  variant="outline" 
                  className={
                    isInternalTask(task)
                      ? 'bg-blue-100 text-blue-800 border-blue-200'
                      : 'bg-amber-100 text-amber-800 border-amber-200'
                  }
                >
                  {isInternalTask(task) ? 'Internal' : 'External'}
                </Badge>
                <Badge 
                  variant="outline" 
                  className="bg-gray-100 text-gray-800 border-gray-200"
                >
                  {task.status}
                </Badge>
              </div>
            </div>
            <CardDescription className="mt-1">
              {task.description.substring(0, 100)}
              {task.description.length > 100 ? '...' : ''}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Visibility</p>
                <p>{task.visibility}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Type</p>
                <p>{task.type}</p>
              </div>
              {isInternalTask(task) && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Assigned Role</p>
                  <p>{task.assigned_role || 'Not specified'}</p>
                </div>
              )}
              {isExternalTask(task) && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Offers</p>
                  <p>{task.offers_count}</p>
                </div>
              )}
            </div>
            <div className="flex justify-between">
              <Button 
                variant="outline" 
                size="sm" 
                asChild
              >
                <Link to={`/tasks/${task.id}`}>View Original</Link>
              </Button>
              <Button 
                variant="default" 
                size="sm" 
                asChild
              >
                <Link to={`/tasks/enhanced/${task.id}`}>View Enhanced</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default TaskTypeDebug;
