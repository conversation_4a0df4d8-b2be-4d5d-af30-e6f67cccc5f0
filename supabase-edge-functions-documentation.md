# Supabase Edge Functions Documentation

## Overview

This documentation covers the implementation of Supabase Edge Functions in the ClassTasker application, focusing on the invoice email functionality. Edge Functions provide a serverless approach to handling backend operations, improving scalability, security, and architecture.

## Table of Contents

1. [What Are Edge Functions?](#what-are-edge-functions)
2. [Current Implementation](#current-implementation)
3. [Configuration](#configuration)
4. [Deployment](#deployment)
5. [Testing](#testing)
6. [Troubleshooting](#troubleshooting)
7. [Best Practices](#best-practices)
8. [Future Implementations](#future-implementations)

## What Are Edge Functions?

Supabase Edge Functions are serverless functions that run on Deno, a secure JavaScript/TypeScript runtime. They allow you to execute code in response to HTTP requests without managing servers. Edge Functions run close to your users, reducing latency and improving performance.

### Benefits

- **Serverless**: No server management required
- **Scalable**: Automatically scales with demand
- **Secure**: Isolated execution environment
- **Fast**: Runs close to users
- **Cost-effective**: Pay only for what you use
- **TypeScript support**: Write in TypeScript or JavaScript
- **Access to Supabase services**: Direct access to your Supabase project

## Current Implementation

### Invoice Email Function

The ClassTasker application currently uses an Edge Function to send invoice emails to customers. This function interacts with the Stripe API to send invoice emails for completed tasks.

#### Function Location

```
/supabase/functions/send-invoice-email/index.ts
```

#### Function Code Overview

```typescript
// Main function handler
serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Stripe with secret key
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
      apiVersion: '2023-10-16',
    })

    // Get invoice ID from request
    const { invoiceId } = await req.json()

    // Retrieve invoice details
    const invoice = await stripe.invoices.retrieve(invoiceId)

    // Send the invoice email
    const sentInvoice = await stripe.invoices.sendInvoice(invoiceId)

    // Return success response
    return new Response(
      JSON.stringify({
        sent: true,
        invoice: sentInvoice,
        message: "Invoice email sent successfully...",
        mode: isLiveMode ? 'live' : 'test'
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    // Handle errors
    return new Response(
      JSON.stringify({ error: 'Failed to send invoice email' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
```

#### Client-Side Integration

The function is called from the client-side using the `stripeService.sendInvoiceEmail` method:

```typescript
// In src/services/stripeService.ts
async sendInvoiceEmail(invoiceId: string): Promise<any> {
  try {
    // Get the user's session
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      throw new Error('No active session');
    }

    // Call the Supabase Edge Function
    const supabaseFunctionsUrl = import.meta.env.VITE_SUPABASE_FUNCTIONS_URL ||
      'https://qcnotlojmyvpqbbgoxbc.supabase.co/functions/v1';

    const response = await fetch(
      `${supabaseFunctionsUrl}/send-invoice-email`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({ invoiceId }),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error sending invoice email:', errorData);
      return false;
    }

    return await response.json();
  } catch (error) {
    console.error('Error sending invoice email:', error);
    return false;
  }
}
```

## Configuration

### Environment Variables

The Edge Function requires the following environment variables:

| Variable | Description | Example |
|----------|-------------|---------|
| `STRIPE_SECRET_KEY` | Stripe API secret key for test mode | `sk_test_...` |
| `STRIPE_LIVE_SECRET_KEY` | Stripe API secret key for live mode (optional) | `sk_live_...` |

### Setting Environment Variables

Environment variables can be set using the Supabase Dashboard or CLI:

#### Using Supabase Dashboard

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project
3. Navigate to Settings > API
4. Click on the "Edge Functions" tab
5. Add your environment variables

#### Using Supabase CLI

```bash
npx supabase secrets set STRIPE_SECRET_KEY=sk_test_your_key
npx supabase secrets set STRIPE_LIVE_SECRET_KEY=sk_live_your_key
```

## Deployment

### Prerequisites

- Supabase CLI installed
- Supabase project set up
- Stripe account with API keys

### Deployment Steps

1. **Install Supabase CLI** (if not already installed):
   ```bash
   npm install -g supabase
   ```

2. **Login to Supabase**:
   ```bash
   npx supabase login
   ```

3. **Link to your project**:
   ```bash
   npx supabase link --project-ref your-project-ref
   ```

4. **Set environment variables**:
   ```bash
   npx supabase secrets set STRIPE_SECRET_KEY=sk_test_your_key
   ```

5. **Deploy the function**:
   ```bash
   npx supabase functions deploy send-invoice-email
   ```

### Updating an Existing Function

To update an existing function:

1. Make your changes to the function code
2. Deploy the updated function:
   ```bash
   npx supabase functions deploy send-invoice-email
   ```

## Testing

### Local Testing

1. **Serve the function locally**:
   ```bash
   npx supabase functions serve send-invoice-email
   ```

2. **Test with curl**:
   ```bash
   curl -X POST http://localhost:54321/functions/v1/send-invoice-email \
     -H "Content-Type: application/json" \
     -d '{"invoiceId":"in_your_invoice_id"}'
   ```

### Testing in Production

1. **Using the test script**:
   ```javascript
   // Test script to simulate clicking the email button
   async function testEmailButton() {
     try {
       console.log('Testing email button click...');
       
       // Import the stripeService
       const { stripeService } = await import('/src/services/stripeService.js');
       
       // Call the sendInvoiceEmail method
       const result = await stripeService.sendInvoiceEmail('in_your_invoice_id');
       
       console.log('Result:', result);
     } catch (error) {
       console.error('Error testing email button:', error);
     }
   }
   
   // Run the test
   testEmailButton();
   ```

2. **Verifying in Stripe Dashboard**:
   - Go to [Stripe Dashboard](https://dashboard.stripe.com/test/events)
   - Filter for `invoice.sent` events
   - Check the event details to confirm the invoice email was processed

## Troubleshooting

### Common Issues

#### 1. Function returns 500 error

**Possible causes**:
- Missing environment variables
- Invalid Stripe API key
- Incorrect invoice ID

**Solution**:
- Check that environment variables are set correctly
- Verify the Stripe API key is valid
- Ensure the invoice ID exists and is in the correct format

#### 2. Function deploys but doesn't work

**Possible causes**:
- CORS issues
- Authentication problems
- Incorrect URL

**Solution**:
- Check CORS headers in the function
- Verify authentication token is being sent correctly
- Confirm the function URL is correct

#### 3. "No such invoice" error

**Possible causes**:
- Invoice doesn't exist
- Invoice ID is from a different Stripe account
- Using test mode invoice ID in live mode (or vice versa)

**Solution**:
- Verify the invoice exists in your Stripe account
- Ensure you're using the correct mode (test/live)

### Debugging

1. **Enable verbose logging**:
   Add more `console.log` statements to your function to track execution flow.

2. **Check function logs**:
   ```bash
   npx supabase functions logs send-invoice-email
   ```

3. **Test with minimal example**:
   Create a simplified version of your function to isolate the issue.

## Best Practices

### Security

1. **Never expose sensitive keys**:
   Always use environment variables for API keys and secrets.

2. **Validate input**:
   Always validate and sanitize input data before processing.

3. **Use proper authentication**:
   Ensure your functions are protected with proper authentication.

### Performance

1. **Keep functions small**:
   Focus each function on a single responsibility.

2. **Minimize dependencies**:
   Only import what you need to reduce cold start times.

3. **Use caching when appropriate**:
   Cache results to improve performance for repeated operations.

### Error Handling

1. **Provide meaningful error messages**:
   Return detailed error information to help with debugging.

2. **Log errors properly**:
   Log errors with enough context to understand what went wrong.

3. **Implement retries for transient failures**:
   Add retry logic for operations that might fail temporarily.

## Future Implementations

Consider implementing these additional Edge Functions to enhance your application:

### 1. Payment Processing

Move payment processing logic to Edge Functions to improve security and scalability.

```typescript
// Example payment processing function
serve(async (req) => {
  // Process payment using Stripe
  const { paymentMethodId, amount, currency } = await req.json();
  
  const paymentIntent = await stripe.paymentIntents.create({
    payment_method: paymentMethodId,
    amount,
    currency,
    confirm: true,
  });
  
  return new Response(JSON.stringify({ paymentIntent }), {
    headers: { 'Content-Type': 'application/json' },
  });
});
```

### 2. Notification System

Implement a notification system using Edge Functions.

```typescript
// Example notification function
serve(async (req) => {
  const { userId, message, type } = await req.json();
  
  // Store notification in database
  const { data, error } = await supabase
    .from('notifications')
    .insert([{ user_id: userId, message, type }]);
  
  // Trigger real-time update
  await supabase
    .from('notification_channel')
    .insert([{ user_id: userId, event: 'new_notification' }]);
  
  return new Response(JSON.stringify({ success: true }), {
    headers: { 'Content-Type': 'application/json' },
  });
});
```

### 3. Scheduled Tasks

Implement scheduled tasks using Edge Functions and a cron service.

```typescript
// Example scheduled task function
serve(async (req) => {
  // This function could be triggered by a scheduled event
  
  // Find overdue invoices
  const { data: overdueInvoices } = await supabase
    .from('invoices')
    .select('*')
    .lt('due_date', new Date().toISOString())
    .eq('status', 'pending');
  
  // Send reminder emails
  for (const invoice of overdueInvoices) {
    await sendReminderEmail(invoice);
  }
  
  return new Response(JSON.stringify({ processed: overdueInvoices.length }), {
    headers: { 'Content-Type': 'application/json' },
  });
});
```
