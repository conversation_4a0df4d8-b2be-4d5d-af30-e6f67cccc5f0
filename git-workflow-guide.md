# Git Workflow Guide for ClassTasker Connect

## Issues Encountered

During our recent development work, we encountered several issues with Git that slowed down our workflow:

1. **Terminal Command Truncation**: Git commands were being truncated or timing out in the terminal, making it difficult to execute basic Git operations.

2. **Merge Conflicts**: We had merge conflicts between branches that were difficult to resolve, particularly in files like `vercel.json`.

3. **Pull Request Validation Errors**: We received errors like "No commits between main and branch" or "Pull Request is not mergeable" when trying to create or merge pull requests.

4. **Untracked Files and Staging Issues**: We had difficulty staging and committing specific files, with errors like "no changes added to commit".

5. **Unintended Files in Working Directory**: The working directory contained many untracked files and uncommitted changes that weren't relevant to our current task.

## Root Causes

1. **Terminal Limitations**: The terminal interface has limitations in command length and execution time, which can cause commands to be truncated or time out.

2. **Branch Management**: Multiple branches with overlapping changes created merge conflicts.

3. **Working Directory Pollution**: The working directory contained many untracked files and uncommitted changes from previous work.

4. **Incomplete Git Operations**: Git operations were sometimes interrupted or incomplete, leaving the repository in an inconsistent state.

## Working Approach Moving Forward

### 1. Clean Working Directory

Always start with a clean working directory:

```bash
# Check the status of your working directory
git status

# Stash any uncommitted changes you want to keep
git stash save "meaningful description"

# Discard any unwanted changes
git checkout -- .

# Remove untracked files (use with caution)
git clean -fd
```

### 2. Branch Management

Follow a consistent branching strategy:

```bash
# Always start from an up-to-date main branch
git checkout main
git pull origin main

# Create a new feature branch with a descriptive name
git checkout -b feature/descriptive-name

# Keep feature branches short-lived and focused on a single task
```

### 3. Commit Strategy

Make small, focused commits with clear messages:

```bash
# Stage specific files (avoid using git add .)
git add path/to/file1.js path/to/file2.js

# Create a commit with a clear message
git commit -m "feat: add detailed logging to GetStream components"
```

Use conventional commit messages:
- `feat:` for new features
- `fix:` for bug fixes
- `docs:` for documentation changes
- `refactor:` for code refactoring
- `test:` for adding tests
- `chore:` for maintenance tasks

### 4. Push and Pull Requests

Push changes regularly and create focused pull requests:

```bash
# Push your branch to the remote repository
git push -u origin feature/descriptive-name

# Create a pull request with a clear title and description
```

### 5. Handling Merge Conflicts

Resolve merge conflicts promptly:

```bash
# Update your branch with the latest changes from main
git checkout main
git pull origin main
git checkout feature/descriptive-name
git merge main

# Resolve any conflicts
# Edit conflicted files, then:
git add <resolved-files>
git commit -m "merge: resolve conflicts with main"
```

### 6. Troubleshooting Common Issues

#### Command Truncation
Use shorter commands or split operations into multiple steps.

#### "No commits between branches" Error
Ensure your branch has committed changes that aren't already in the target branch:
```bash
git log --oneline main..your-branch
```

#### "Pull Request is not mergeable" Error
Resolve merge conflicts before creating a pull request:
```bash
git checkout main
git pull origin main
git checkout your-branch
git merge main
# Resolve conflicts
git push origin your-branch
```

#### Untracked Files
Be selective about which files you add to your commits:
```bash
# List all untracked files
git ls-files --others --exclude-standard

# Add only specific files
git add file1.js file2.js

# Ignore files that shouldn't be tracked
# Add them to .gitignore
```

## Best Practices for ClassTasker Connect

1. **Direct Commits to Main**: For quick, non-breaking changes, it's acceptable to commit directly to main as per the project's workflow.

2. **Feature Branches**: For larger changes or features that require testing, use feature branches.

3. **Documentation**: Always document significant changes, especially those related to configuration or environment variables.

4. **Testing Before Merging**: Always test changes before merging to main, especially for critical components like authentication.

5. **Environment Variables**: Be careful with environment variables and configuration files, as these are common sources of merge conflicts.

6. **Regular Cleanup**: Regularly clean up your working directory and local branches to avoid confusion.

By following these guidelines, we can streamline our development workflow and avoid the issues we encountered during our recent work.
