import React from 'react';
import { Route, Routes, Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import PWATaskView from '@/components/pwa/PWATaskView';
import PWASimplifiedTaskView from '@/components/pwa/PWASimplifiedTaskView';
import PWATaskActions from '@/components/pwa/PWATaskActions';
import PWATaskActionsWrapper from '@/components/pwa/PWATaskActionsWrapper';
import PWATaskActionsCleanWrapper from '@/components/pwa/PWATaskActionsCleanWrapper';
import PWATaskActionsFinalWrapper from '@/components/pwa/PWATaskActionsFinalWrapper';
import PWAGetStreamChatView from '@/components/pwa/PWAGetStreamChatView';
import PWAGetStreamChatList from '@/components/pwa/PWAGetStreamChatList';
import PWAMobileLayout from '@/components/pwa/PWAMobileLayout';
import PWATaskList from '@/components/pwa/PWATaskList';
import { isPWA } from '@/utils/pwa-utils';
import PWAHome from '@/pages/pwa/PWAHome';
import PWAProfile from '@/pages/pwa/PWAProfile';
import PWAProfileEdit from '@/pages/pwa/PWAProfileEdit';
import PWAHelp from '@/pages/pwa/PWAHelp';
import PWAContact from '@/pages/pwa/PWAContact';
import PWAMarketplacePage from '@/pages/pwa/PWAMarketplacePage';
import PWAPostTask from '@/pages/pwa/PWAPostTask';
import PWANotifications from '@/pages/pwa/PWANotifications';
import PWATermsOfService from '@/pages/pwa/PWATermsOfService';
import PWAPrivacyPolicy from '@/pages/pwa/PWAPrivacyPolicy';
import PWATermsAndPrivacy from '@/pages/pwa/PWATermsAndPrivacy';
import Tasks from '@/pages/Tasks';


// Lazy-loaded components
const Login = React.lazy(() => import('@/pages/Login'));
const Register = React.lazy(() => import('@/pages/Register'));
const NotFound = React.lazy(() => import('@/pages/NotFound'));

/**
 * PWA-specific routes
 * These routes are optimized for mobile PWA usage
 */
const PWARoutes: React.FC = () => {
  const { user, isLoading } = useAuth();

  // If not running as PWA, redirect to regular routes
  if (!isPWA()) {
    return <Navigate to="/" replace />;
  }

  // Show loading state while auth is being checked
  if (isLoading) {
    return (
      <PWAMobileLayout>
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </PWAMobileLayout>
    );
  }

  return (
    <Routes>
        {/* Public routes */}
        <Route path="/login" element={
          user ? <Navigate to="/pwa" replace /> : <Login />
        } />
        <Route path="/register" element={
          user ? <Navigate to="/pwa" replace /> : <Register />
        } />

        {/* Protected routes - require authentication */}
        {user ? (
          <>
            {/* Dashboard - redirect suppliers to marketplace */}
            <Route path="/" element={
              <PWAHome />
            } />

            {/* Task routes */}
            <Route path="/tasks" element={
              <PWAMobileLayout>
                <PWATaskList />
              </PWAMobileLayout>
            } />
            <Route path="/tasks/create" element={<PWAPostTask />} />
            <Route path="/tasks/:id" element={<PWASimplifiedTaskView />} />
            <Route path="/tasks/:id/actions" element={<PWATaskActionsFinalWrapper />} />

            {/* Chat routes */}
            <Route path="/messages" element={<PWAGetStreamChatList />} />
            <Route path="/messages/:channelId" element={<PWAGetStreamChatView />} />

            {/* Marketplace route */}
            <Route path="/marketplace" element={<PWAMarketplacePage />} />

            {/* Profile routes */}
            <Route path="/profile" element={<PWAProfile />} />
            <Route path="/profile/edit" element={<PWAProfileEdit />} />

            {/* Notifications route */}
            <Route path="/notifications" element={<PWANotifications />} />

            {/* Help and Support routes */}
            <Route path="/help" element={<PWAHelp />} />
            <Route path="/contact" element={<PWAContact />} />

            {/* Legal routes */}
            <Route path="/terms" element={<PWATermsAndPrivacy />} />
            <Route path="/privacy" element={<PWAPrivacyPolicy />} />
            <Route path="/terms-of-service" element={<PWATermsOfService />} />
            <Route path="/privacy-policy" element={<PWAPrivacyPolicy />} />

            {/* Redirect to dashboard for any other route */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </>
        ) : (
          // Redirect to login if not authenticated
          <Route path="*" element={<Navigate to="/login" replace />} />
        )}

        {/* 404 page */}
        <Route path="/not-found" element={<NotFound />} />
      </Routes>
  );
};

export default PWARoutes;
