// Script to verify the admin invoice in Stripe
require('dotenv').config();
const Stripe = require('stripe');

// Initialize Stripe
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;

if (!stripeSecretKey) {
  console.error('Missing Stripe secret key. Check your environment variables.');
  process.exit(1);
}

const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2023-10-16',
});

// The invoice ID from the previous step
const invoiceId = 'in_1RGImGPWBcixtWOc2SXogLwe';

async function verifyAdminInvoice() {
  try {
    console.log(`Verifying invoice: ${invoiceId}`);
    
    // Retrieve the invoice from Stripe
    const invoice = await stripe.invoices.retrieve(invoiceId);
    
    console.log('Invoice details:');
    console.log(`- ID: ${invoice.id}`);
    console.log(`- Customer: ${invoice.customer}`);
    console.log(`- Customer Email: ${invoice.customer_email}`);
    console.log(`- Status: ${invoice.status}`);
    console.log(`- Total: ${invoice.total / 100} ${invoice.currency.toUpperCase()}`);
    console.log(`- Hosted Invoice URL: ${invoice.hosted_invoice_url}`);
    
    // Retrieve the customer from Stripe
    const customer = await stripe.customers.retrieve(invoice.customer);
    
    console.log('\nCustomer details:');
    console.log(`- ID: ${customer.id}`);
    console.log(`- Email: ${customer.email}`);
    console.log(`- Name: ${customer.name}`);
    console.log(`- Description: ${customer.description}`);
    
    // Check if the invoice can be sent
    console.log('\nChecking if invoice can be sent:');
    try {
      // This is a dry run - it won't actually send the email
      console.log('Attempting to retrieve the invoice again to check its status...');
      const refreshedInvoice = await stripe.invoices.retrieve(invoiceId);
      console.log(`Invoice status: ${refreshedInvoice.status}`);
      
      if (refreshedInvoice.status === 'draft') {
        console.log('Invoice is in draft status. It needs to be finalized before sending.');
      } else if (refreshedInvoice.status === 'open') {
        console.log('Invoice is in open status. It can be sent.');
      } else if (refreshedInvoice.status === 'paid') {
        console.log('Invoice is in paid status. It can still be sent.');
      } else {
        console.log(`Invoice is in ${refreshedInvoice.status} status.`);
      }
    } catch (error) {
      console.error('Error checking invoice status:', error.message);
    }
  } catch (error) {
    console.error('Error verifying admin invoice:', error);
  }
}

verifyAdminInvoice();
