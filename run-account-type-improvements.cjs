// <PERSON>ript to run the account type improvements SQL
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runAccountTypeImprovements() {
  try {
    console.log('Running account type improvements...');
    
    // Read the SQL file
    const sql = fs.readFileSync('./sql/improve_account_type_handling.sql', 'utf8');
    
    // Split the SQL into individual statements
    const statements = sql.split(';').filter(stmt => stmt.trim() !== '');
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim() + ';';
      console.log(`\nExecuting statement ${i + 1} of ${statements.length}:`);
      console.log(statement.substring(0, 100) + '...');
      
      try {
        // Execute the SQL statement
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        
        if (error) {
          console.error(`Error executing statement ${i + 1}:`, error);
          
          // Try an alternative approach for this statement
          console.log('Trying alternative approach...');
          
          // For functions, try creating them one by one
          if (statement.includes('CREATE OR REPLACE FUNCTION')) {
            const functionName = statement.match(/CREATE OR REPLACE FUNCTION\s+public\.(\w+)/)[1];
            console.log(`Creating function ${functionName}...`);
            
            // Try to execute just this function creation
            const { error: funcError } = await supabase.rpc('create_function', { 
              function_name: functionName,
              function_sql: statement
            });
            
            if (funcError) {
              console.error(`Error creating function ${functionName}:`, funcError);
            } else {
              console.log(`Successfully created function ${functionName}`);
            }
          }
          // For triggers, try creating them one by one
          else if (statement.includes('CREATE TRIGGER')) {
            const triggerName = statement.match(/CREATE TRIGGER\s+(\w+)/)[1];
            console.log(`Creating trigger ${triggerName}...`);
            
            // Try to execute just this trigger creation
            const { error: trigError } = await supabase.rpc('create_trigger', { 
              trigger_name: triggerName,
              trigger_sql: statement
            });
            
            if (trigError) {
              console.error(`Error creating trigger ${triggerName}:`, trigError);
            } else {
              console.log(`Successfully created trigger ${triggerName}`);
            }
          }
        } else {
          console.log(`Successfully executed statement ${i + 1}`);
        }
      } catch (stmtError) {
        console.error(`Exception executing statement ${i + 1}:`, stmtError);
      }
    }
    
    // Verify the changes
    console.log('\nVerifying account type consistency...');
    
    // Check for any remaining inconsistencies
    const { data: inconsistentProfiles, error: checkError } = await supabase
      .from('profiles')
      .select('id, email, role, account_type')
      .or('and(role.eq.supplier,account_type.neq.supplier),and(account_type.eq.supplier,role.neq.supplier)');
    
    if (checkError) {
      console.error('Error checking for inconsistencies:', checkError);
    } else if (inconsistentProfiles && inconsistentProfiles.length > 0) {
      console.log(`Found ${inconsistentProfiles.length} profiles with inconsistent role and account_type:`);
      inconsistentProfiles.forEach(profile => {
        console.log(`- ID: ${profile.id}, Email: ${profile.email}, Role: ${profile.role}, Account Type: ${profile.account_type}`);
      });
    } else {
      console.log('No inconsistencies found! All profiles have consistent role and account_type values.');
    }
    
    // Check the supplier accounts
    const { data: supplierProfiles, error: supplierError } = await supabase
      .from('profiles')
      .select('id, email, role, account_type')
      .eq('account_type', 'supplier');
    
    if (supplierError) {
      console.error('Error checking supplier profiles:', supplierError);
    } else {
      console.log(`\nFound ${supplierProfiles.length} supplier profiles:`);
      supplierProfiles.forEach(profile => {
        console.log(`- ID: ${profile.id}, Email: ${Array.isArray(profile.email) ? profile.email[0] : profile.email}, Role: ${profile.role}, Account Type: ${profile.account_type}`);
      });
    }
    
    console.log('\nAccount type improvements completed successfully!');
  } catch (error) {
    console.error('Error running account type improvements:', error);
  }
}

runAccountTypeImprovements();
