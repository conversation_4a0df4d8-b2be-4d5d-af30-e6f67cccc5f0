/**
 * GetStream Chat Client Integration
 *
 * This file provides a singleton instance of the GetStream Chat client
 * and utility functions for working with GetStream.
 */

import { StreamChat, TokenOrProvider, User as StreamUser, Channel } from 'stream-chat';
import { getStreamApiUrl } from '@/utils/apiConfig';
import { rateLimit } from '@/utils/security';

// Get API key from environment variables
const apiKey = import.meta.env.VITE_GETSTREAM_API_KEY ||
  (typeof window !== 'undefined' && window.env?.VITE_GETSTREAM_API_KEY);

// Create a singleton instance of the StreamChat client
let chatClient: StreamChat | null = null;

// Connection tracking
let activeConnections = 0;
let connectedUserId: string | null = null;
let connectionTimestamp: number | null = null;
let disconnectTimeout: NodeJS.Timeout | null = null;

// Rate limiting for GetStream API calls
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const MAX_REQUESTS_PER_WINDOW = 30; // Conservative limit
const RETRY_DELAYS = [1000, 2000, 4000, 8000]; // Exponential backoff delays

/**
 * Sleep function for delays
 */
const sleep = (ms: number): Promise<void> => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Retry function with exponential backoff for GetStream API calls
 */
const retryWithBackoff = async <T>(
  operation: () => Promise<T>,
  operationName: string,
  maxRetries: number = 3
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      // Check rate limiting before each attempt
      const rateLimitKey = `getstream_api_${operationName}`;
      if (rateLimit.isLimited(rateLimitKey, MAX_REQUESTS_PER_WINDOW, RATE_LIMIT_WINDOW)) {
        console.warn(`[${operationName}] Rate limited, waiting before retry...`);
        await sleep(RETRY_DELAYS[Math.min(attempt, RETRY_DELAYS.length - 1)]);
        continue;
      }

      const result = await operation();

      // Reset rate limit on success
      if (attempt > 0) {
        console.log(`[${operationName}] Succeeded on attempt ${attempt + 1}`);
      }

      return result;
    } catch (error: any) {
      lastError = error;

      // Check if this is a rate limit error (429)
      if (error?.code === 9 || error?.message?.includes('Too many requests')) {
        console.warn(`[${operationName}] Hit rate limit on attempt ${attempt + 1}, will retry...`);

        if (attempt < maxRetries) {
          const delay = RETRY_DELAYS[Math.min(attempt, RETRY_DELAYS.length - 1)];
          console.log(`[${operationName}] Waiting ${delay}ms before retry...`);
          await sleep(delay);
          continue;
        }
      }

      // For other errors, don't retry immediately
      if (attempt < maxRetries) {
        console.warn(`[${operationName}] Error on attempt ${attempt + 1}:`, error?.message || error);
        await sleep(1000); // Short delay for other errors
      }
    }
  }

  console.error(`[${operationName}] Failed after ${maxRetries + 1} attempts:`, lastError);
  throw lastError;
};

/**
 * Track active connections to prevent premature disconnection
 */
export const incrementConnectionCount = (): number => {
  activeConnections++;
  console.log(`[StreamClient] Connection count incremented to ${activeConnections}`);

  // Clear any pending disconnect timeout
  if (disconnectTimeout) {
    console.log('[StreamClient] Clearing pending disconnect timeout');
    clearTimeout(disconnectTimeout);
    disconnectTimeout = null;
  }

  return activeConnections;
};

/**
 * Decrement connection count and schedule disconnection if needed
 * @param delayDisconnect - Whether to delay disconnection to prevent race conditions
 */
export const decrementConnectionCount = (delayDisconnect: boolean = true): number => {
  if (activeConnections > 0) {
    activeConnections--;
  }

  console.log(`[StreamClient] Connection count decremented to ${activeConnections}`);

  // If no more active connections, schedule disconnection
  if (activeConnections === 0 && chatClient && chatClient.userID) {
    // In PWA mode, never disconnect
    const isPWA = typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches;
    if (isPWA) {
      console.log('[StreamClient] PWA mode detected, not scheduling disconnection');
      return activeConnections;
    }

    if (delayDisconnect) {
      console.log('[StreamClient] Scheduling disconnection in 5 seconds');

      // Clear any existing timeout
      if (disconnectTimeout) {
        clearTimeout(disconnectTimeout);
      }

      // Set a new timeout
      disconnectTimeout = setTimeout(() => {
        console.log('[StreamClient] Executing delayed disconnection');
        if (activeConnections === 0) {
          disconnectUser(false).catch(error => {
            console.error('[StreamClient] Error in delayed disconnection:', error);
          });
        } else {
          console.log('[StreamClient] Skipping delayed disconnection, connection count is now:', activeConnections);
        }
        disconnectTimeout = null;
      }, 5000); // 5 second delay to prevent race conditions
    }
  }

  return activeConnections;
};

/**
 * Reset connection count (for testing or recovery)
 */
export const resetConnectionCount = (): void => {
  console.log('[StreamClient] Resetting connection count from', activeConnections, 'to 0');
  activeConnections = 0;

  if (disconnectTimeout) {
    clearTimeout(disconnectTimeout);
    disconnectTimeout = null;
  }
};

/**
 * Get the StreamChat client instance
 * Creates a new instance if one doesn't exist
 * @param forceNew - Whether to force creation of a new client instance
 */
export const getStreamClient = (forceNew: boolean = false): StreamChat => {
  // If we're forcing a new client or don't have one yet
  if ((forceNew || !chatClient) && apiKey) {
    console.log('[getStreamClient] Creating new StreamChat instance');

    // If we already have a client and are forcing a new one, disconnect the old one
    if (chatClient && forceNew) {
      try {
        console.log('[getStreamClient] Disconnecting existing client before creating new one');
        chatClient.disconnectUser();
        resetConnectionCount(); // Reset connection count when forcing a new client
      } catch (error) {
        console.warn('[getStreamClient] Error disconnecting existing client:', error);
      }
    }

    // Create a new client instance
    const client = StreamChat.getInstance(apiKey);

    // Add safety wrapper for isConnected method
    const originalIsConnected = client.isConnected;
    client.isConnected = function() {
      try {
        // Check if the original method exists and is a function
        if (typeof originalIsConnected === 'function') {
          return originalIsConnected.call(this);
        }
        // Fallback: check if there's an active websocket connection
        return !!this.wsConnection;
      } catch (error) {
        console.error('[StreamClient] Error in isConnected method:', error);
        // Default to false if there's an error
        return false;
      }
    };

    // Add a safety wrapper for queryChannels method
    const originalQueryChannels = client.queryChannels;
    client.queryChannels = function(...args: any[]) {
      try {
        // Check if the client is connected before querying channels
        if (!this.isConnected()) {
          console.error('[StreamClient] Cannot query channels: client not connected');
          return Promise.reject(new Error('Call connectUser or connectAnonymousUser before creating a channel'));
        }

        // Call the original method
        return originalQueryChannels.apply(this, args);
      } catch (error) {
        console.error('[StreamClient] Error in queryChannels method:', error);
        return Promise.reject(error);
      }
    };

    chatClient = client;
  }

  if (!chatClient) {
    throw new Error('Failed to initialize StreamChat client. API key may be missing.');
  }

  return chatClient;
};

/**
 * Connect a user to the StreamChat client
 * @param userId - The user's ID
 * @param username - The user's display name
 * @param token - The user's token for authentication (not used, we generate a new one)
 * @param userImage - Optional URL to the user's avatar
 */
export const connectUser = async (
  userId: string,
  username: string,
  token: TokenOrProvider,
  userImage?: string
): Promise<StreamChat> => {
  try {
    // Increment the connection count
    incrementConnectionCount();

    let client = getStreamClient();

    // Define user data
    const userData: StreamUser = {
      id: userId,
      name: username,
      image: userImage,
    };

    // Check if the client is already connected with the same user
    try {
      const isConnected = client.isConnected?.() || false;

      if (client.userID === userId && isConnected) {
        console.log('[connectUser] User already connected to Stream:', userId);

        // Update connection tracking
        connectedUserId = userId;
        connectionTimestamp = Date.now();

        // Even if already connected, verify the connection is healthy
        try {
          // Try a simple API call to verify the connection
          await client.queryUsers({ id: userId }, { id: 1 }, { limit: 1 });
          console.log('[connectUser] Connection verified as healthy');
          return client;
        } catch (verifyError) {
          console.warn('[connectUser] Connection appears broken despite isConnected=true:', verifyError);
          // Fall through to reconnection logic below
        }
      }

      // If connected but with a different user, or not connected at all
      console.log('[connectUser] Client status:', {
        currentUserID: client.userID,
        targetUserID: userId,
        isConnected,
        needsReconnect: client.userID !== userId || !isConnected
      });

      // Disconnect any existing user first
      if (client.userID && isConnected) {
        try {
          console.log('[connectUser] Disconnecting existing user:', client.userID);
          await client.disconnectUser();
          console.log('[connectUser] Successfully disconnected existing user');

          // Reset connection tracking
          connectedUserId = null;
          connectionTimestamp = null;
        } catch (e) {
          console.warn('[connectUser] Error disconnecting existing user:', e);

          // If we can't disconnect cleanly, create a new client instance
          console.log('[connectUser] Creating new client instance after failed disconnect');
          client = getStreamClient(true); // Force new client creation
        }
      }
    } catch (checkError) {
      console.warn('[connectUser] Error checking client connection status:', checkError);

      // If we encounter an error checking connection status, create a new client
      console.log('[connectUser] Creating new client instance after connection check error');
      client = getStreamClient(true); // Force new client creation
    }

    // Generate a token from the server
    console.log('[connectUser] Generating token for user:', userId);
    const serverToken = await generateToken(userId);

    // Connect the user with the server token
    console.log('[connectUser] Connecting user with token:', userId);
    await client.connectUser(userData, serverToken);
    console.log('[connectUser] Successfully connected user to Stream:', userId);

    // Update connection tracking
    connectedUserId = userId;
    connectionTimestamp = Date.now();

    // Add enhanced connection handling for PWA
    const isPWA = typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches;
    if (isPWA) {
      console.log('[connectUser] Adding enhanced connection handling for PWA');

      // Listen for connection status changes
      client.on('connection.changed', (event) => {
        console.log('[StreamClient] Connection status changed:', {
          online: event.online,
          type: event.type
        });

        // If connection goes offline, attempt to reconnect after a delay
        if (!event.online) {
          console.log('[StreamClient] Connection went offline, will attempt to reconnect');

          // Wait a bit before attempting to reconnect
          setTimeout(() => {
            if (client && !client.isConnected() && activeConnections > 0) {
              console.log('[StreamClient] Attempting to reconnect...');
              client.connectUser(userData, serverToken)
                .then(() => {
                  console.log('[StreamClient] Reconnection successful');
                  connectedUserId = userId;
                  connectionTimestamp = Date.now();
                })
                .catch(err => console.error('[StreamClient] Reconnection failed:', err));
            }
          }, 5000);
        }
      });

      // Listen for connection errors
      client.on('connection.error', (event) => {
        console.error('[StreamClient] Connection error:', {
          error: event.error,
          type: event.type
        });

        // For recoverable errors, attempt to reconnect
        if (event.error && event.error.code !== 40) { // 40 is token expired
          console.log('[StreamClient] Attempting to recover from connection error');

          setTimeout(() => {
            if (client && activeConnections > 0) {
              console.log('[StreamClient] Attempting to reconnect after error...');
              client.connectUser(userData, serverToken)
                .then(() => {
                  console.log('[StreamClient] Error recovery successful');
                  connectedUserId = userId;
                  connectionTimestamp = Date.now();
                })
                .catch(err => console.error('[StreamClient] Error recovery failed:', err));
            }
          }, 3000);
        }
      });

      // Set up periodic connection check for PWA
      const connectionCheckInterval = setInterval(() => {
        if (client && !client.isConnected() && activeConnections > 0) {
          console.log('[StreamClient] Periodic check found disconnected client, reconnecting...');
          client.connectUser(userData, serverToken)
            .then(() => {
              console.log('[StreamClient] Periodic reconnection successful');
              connectedUserId = userId;
              connectionTimestamp = Date.now();
            })
            .catch(err => console.error('[StreamClient] Periodic reconnection failed:', err));
        }
      }, 30000); // Check every 30 seconds

      // Store the interval ID in window so it can be cleared if needed
      if (typeof window !== 'undefined') {
        window.__streamConnectionCheckInterval = connectionCheckInterval;
      }
    }

    return client;
  } catch (error) {
    console.error('[connectUser] Error connecting user to Stream:', error);

    // Decrement connection count on error
    decrementConnectionCount(false);

    throw error;
  }
};

/**
 * Disconnect the current user from the StreamChat client
 * @param force - Whether to force disconnection even in PWA mode
 */
export const disconnectUser = async (force: boolean = false): Promise<void> => {
  // Decrement the connection count
  const remainingConnections = decrementConnectionCount(false);

  // If there are still active connections and we're not forcing disconnection, don't disconnect
  if (remainingConnections > 0 && !force) {
    console.log(`[disconnectUser] Still have ${remainingConnections} active connections, not disconnecting`);
    return;
  }

  // Check if we're in PWA mode
  const isPWA = typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches;

  // Check if we're in a mobile browser (which might behave like PWA)
  const isMobile = typeof window !== 'undefined' && /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

  // In PWA mode or on mobile, we should NEVER disconnect the client to prevent the
  // "Error: You can't use a channel after client.disconnect() was called" issue
  if ((isPWA || isMobile) && !force) {
    console.log(`[disconnectUser] ${isPWA ? 'PWA' : 'Mobile'} mode detected - NOT disconnecting Stream client`);

    // Instead of disconnecting, we'll just log that we're keeping the connection
    if (chatClient) {
      const isConnected = chatClient.isConnected?.() || false;
      console.log('[disconnectUser] Client status (keeping connection):', {
        isConnected,
        userID: chatClient.userID,
        connectionID: chatClient.connectionID,
        isPWA,
        isMobile,
        activeConnections: remainingConnections
      });
    }

    return;
  }

  // For non-PWA mode or if force=true, proceed with normal disconnection
  if (chatClient) {
    try {
      // Check if client is connected before disconnecting
      const isConnected = chatClient.isConnected?.() || false;

      if (isConnected) {
        console.log('[disconnectUser] Disconnecting user from Stream', force ? '(forced)' : '');

        // Store the current user ID for logging
        const userId = chatClient.userID;

        // Disconnect the user
        await chatClient.disconnectUser();

        // Reset connection tracking
        connectedUserId = null;
        connectionTimestamp = null;

        console.log('[disconnectUser] Successfully disconnected user from Stream:', userId);
      } else {
        console.log('[disconnectUser] Client already disconnected, skipping disconnectUser call');
      }
    } catch (error) {
      console.error('[disconnectUser] Error disconnecting user from Stream:', error);

      // If we're forcing disconnection and encounter an error, try to reset the client
      if (force) {
        console.log('[disconnectUser] Force flag set, creating new client instance after error');
        chatClient = null; // Reset the client to force creation of a new one next time
        resetConnectionCount(); // Reset connection count
      }
    }
  }
};

/**
 * Create or update a channel for a task
 * @param taskId - The task ID
 * @param taskTitle - The task title
 * @param members - Array of user IDs to add as members
 * @param forceUpdate - Whether to force update the channel data even if it exists
 */
export const createOrUpdateTaskChannel = async (
  taskId: string,
  taskTitle: string,
  members: string[],
  forceUpdate: boolean = false
): Promise<Channel> => {
  try {
    const client = getStreamClient();

    // Create a unique channel ID
    const channelId = `task-${taskId}`;
    console.log(`[createOrUpdateTaskChannel] Processing channel for task ${taskId} with ${members.length} members`);

    let channel: Channel;
    let channelExists = false;

    // First try to get the channel if it exists
    try {
      channel = client.channel('messaging', channelId);
      await retryWithBackoff(
        () => channel.watch(),
        `watch_channel_${taskId}`
      );
      channelExists = true;
      console.log(`[createOrUpdateTaskChannel] Channel exists for task ${taskId}`);
    } catch (error) {
      // Channel doesn't exist, create it
      console.log(`[createOrUpdateTaskChannel] Channel doesn't exist for task ${taskId}, creating new channel`);
      // Convert members array to object format required by GetStream
      const membersObject = {};
      members.forEach(member => {
        membersObject[member] = { role: 'member' };
      });

      // Get the current user ID
      const currentUserId = client.userID;

      // Create role assignments with the current user as moderator
      const roleAssignments: Record<string, string> = {};
      if (currentUserId) {
        roleAssignments[currentUserId] = 'channel_moderator';
      }

      // Create the channel with proper permissions
      channel = client.channel('messaging', channelId, {
        name: taskTitle,
        members: membersObject,
        task_id: taskId,
        // Add explicit permissions for the channel
        permissions: [
          { name: 'read', resources: ['channel', 'message'], roles: ['channel_member', 'channel_moderator', 'admin'] },
          { name: 'write', resources: ['channel', 'message'], roles: ['channel_member', 'channel_moderator', 'admin'] }
        ],
        // Make sure the current user is a moderator
        role_assignments: roleAssignments
      });

      // Initialize the channel with retry
      await retryWithBackoff(
        () => channel.create(),
        `create_channel_${taskId}`
      );
      console.log(`[createOrUpdateTaskChannel] Successfully created channel for task ${taskId}`);

      // Add a system message with retry
      await retryWithBackoff(
        () => channel.sendMessage({
          text: 'Chat initialized.',
          user_id: members[0] || 'system',
          type: 'system',
        }),
        `send_init_message_${taskId}`
      );
    }

    // If channel exists and we need to update it
    if (channelExists && (forceUpdate || members.length > 0)) {
      // Update channel data if needed
      if (forceUpdate) {
        await channel.update({
          name: taskTitle,
          task_id: taskId,
        });
        console.log(`[createOrUpdateTaskChannel] Updated channel data for task ${taskId}`);
      }

      // Add members if needed
      if (members.length > 0) {
        // Get current members
        const currentMembers = Object.keys(channel.state?.members || {});

        // Filter out members that are already in the channel
        const newMembers = members.filter(member => !currentMembers.includes(member));

        if (newMembers.length > 0) {
          await channel.addMembers(newMembers);
          console.log(`[createOrUpdateTaskChannel] Added ${newMembers.length} new members to channel for task ${taskId}`);
        } else {
          console.log(`[createOrUpdateTaskChannel] No new members to add for task ${taskId}`);
        }
      }
    }

    return channel;
  } catch (error) {
    console.error('[createOrUpdateTaskChannel] Error creating/updating channel for task:', error);
    throw error;
  }
};

/**
 * Create a channel for a task (legacy method, use createOrUpdateTaskChannel instead)
 * @param taskId - The task ID
 * @param taskTitle - The task title
 * @param members - Array of user IDs to add as members
 * @deprecated Use createOrUpdateTaskChannel instead
 */
export const createTaskChannel = async (
  taskId: string,
  taskTitle: string,
  members: string[]
): Promise<Channel> => {
  console.warn('[createTaskChannel] This method is deprecated, use createOrUpdateTaskChannel instead');
  return createOrUpdateTaskChannel(taskId, taskTitle, members, false);
};

/**
 * Get a task channel by ID
 * @param taskId - The task ID
 * @param createIfNotExists - Whether to create the channel if it doesn't exist
 * @param taskTitle - The task title (required if createIfNotExists is true)
 * @param members - Array of user IDs to add as members (required if createIfNotExists is true)
 */
export const getTaskChannel = async (
  taskId: string,
  createIfNotExists: boolean = false,
  taskTitle?: string,
  members?: string[]
): Promise<Channel> => {
  try {
    const client = getStreamClient();
    const channelId = `task-${taskId}`;

    // Create the channel object
    const channel = client.channel('messaging', channelId);

    try {
      // Try to watch the channel to see if it exists with retry
      await retryWithBackoff(
        () => channel.watch(),
        `get_channel_${taskId}`
      );
      console.log(`[getTaskChannel] Successfully retrieved channel for task ${taskId}`);
      return channel;
    } catch (error) {
      // Channel doesn't exist
      console.log(`[getTaskChannel] Channel doesn't exist for task ${taskId}`);

      // Create the channel if requested
      if (createIfNotExists) {
        if (!taskTitle || !members || members.length === 0) {
          throw new Error('Task title and members are required to create a new channel');
        }

        console.log(`[getTaskChannel] Creating new channel for task ${taskId}`);
        return createOrUpdateTaskChannel(taskId, taskTitle, members);
      }

      // Rethrow the error if we don't want to create the channel
      throw error;
    }
  } catch (error) {
    console.error('[getTaskChannel] Error getting/creating task channel:', error);
    throw error;
  }
};

/**
 * Generate a token for a user
 * This should be done server-side in production
 * @param userId - The user's ID
 */
export const generateToken = async (userId: string): Promise<string> => {
  try {
    // Log environment information
    console.log('[generateToken] Environment info:', {
      apiKey: apiKey ? 'present' : 'missing',
      isPWA: typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches,
      origin: typeof window !== 'undefined' ? window.location.origin : 'not in browser',
      hostname: typeof window !== 'undefined' ? window.location.hostname : 'not in browser',
      protocol: typeof window !== 'undefined' ? window.location.protocol : 'not in browser',
      userId
    });

    // Get the API URL
    const apiUrl = getStreamApiUrl('/token');
    console.log('[generateToken] Calling token API at:', apiUrl);

    // Add a timeout to prevent hanging requests
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 8000); // Increased timeout for PWA

    try {
      // Call the token API route with timeout
      console.log('[generateToken] Making fetch request to:', apiUrl);
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      console.log('[generateToken] Fetch response received:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        headers: Array.from(response.headers.entries())
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[generateToken] API response not OK:', {
          status: response.status,
          statusText: response.statusText,
          errorText,
          url: apiUrl
        });
        throw new Error(`Failed to generate token: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('[generateToken] Successfully generated token for user:', userId, {
        tokenLength: data.token ? data.token.length : 0,
        tokenPrefix: data.token ? data.token.substring(0, 10) + '...' : 'missing'
      });
      return data.token;
    } catch (fetchError) {
      clearTimeout(timeoutId);

      // Check if this is an abort error (timeout)
      if (fetchError.name === 'AbortError') {
        console.error('[generateToken] Request timed out after 8 seconds');
        throw new Error('Token request timed out. Please try again.');
      }

      console.error('[generateToken] Fetch error details:', {
        name: fetchError.name,
        message: fetchError.message,
        stack: fetchError.stack,
        url: apiUrl
      });
      throw fetchError;
    }
  } catch (error) {
    console.error('[generateToken] Error generating token:', error);

    // For PWA, try multiple fallback URLs if the relative URL fails
    if (typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches) {
      // Try several fallback URLs in sequence
      const fallbackUrls = [
        '/api/getstream/token',                                // Relative path
        `${window.location.origin}/api/getstream/token`,       // Absolute path with origin
        'https://class-tasker-connect.vercel.app/api/getstream/token' // Hardcoded production URL
      ];

      console.log('[generateToken] PWA mode detected, will try fallback URLs:', fallbackUrls);

      for (const fallbackUrl of fallbackUrls) {
        try {
          console.log(`[generateToken] Attempting fallback for PWA: ${fallbackUrl}`);

          // Use a longer timeout for PWA requests
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout for PWA

          const response = await fetch(fallbackUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              // Add cache-busting headers
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0'
            },
            body: JSON.stringify({ userId }),
            signal: controller.signal
          });

          clearTimeout(timeoutId);

          console.log(`[generateToken] Fallback response:`, {
            url: fallbackUrl,
            status: response.status,
            ok: response.ok,
            statusText: response.statusText
          });

          if (response.ok) {
            const data = await response.json();
            console.log('[generateToken] Successfully generated token using fallback:', fallbackUrl, {
              tokenLength: data.token ? data.token.length : 0,
              tokenPrefix: data.token ? data.token.substring(0, 10) + '...' : 'missing'
            });
            return data.token;
          } else {
            // Log the error response for debugging
            const errorText = await response.text();
            console.error(`[generateToken] Fallback error response for ${fallbackUrl}:`, {
              status: response.status,
              statusText: response.statusText,
              errorText
            });
          }
        } catch (fallbackError) {
          console.error(`[generateToken] Fallback failed for ${fallbackUrl}:`, fallbackError);
        }
      }

      console.error('[generateToken] All fallback attempts failed');

      // Check if we're on a mobile device
      const isMobile = typeof window !== 'undefined' && /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      // Log additional information for mobile devices
      if (isMobile) {
        console.warn('[generateToken] Mobile device detected, logging additional information:');
        console.warn('- User Agent:', navigator.userAgent);
        console.warn('- Window Location:', window.location.href);
        console.warn('- Network Type:', navigator.connection ? navigator.connection.type : 'unknown');

        // Try to detect if we're using localhost on a mobile device (which won't work)
        if (window.location.hostname === 'localhost') {
          console.error('[generateToken] Using localhost on a mobile device will not work! Use the IP address instead.');
        }
      }

      // As a last resort, generate a token that matches the user ID
      // This is not secure but will help diagnose if the issue is with token generation
      console.warn('[generateToken] Using emergency fallback token for debugging with userId:', userId);

      // Create a JWT-like token with the correct user_id
      // Format: header.payload.signature
      // Note: GetStream expects the user_id field to match the userId parameter
      const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
      const payload = btoa(JSON.stringify({
        user_id: userId,
        exp: Math.floor(Date.now() / 1000) + 60 * 60 // 1 hour expiration
      }));
      const signature = btoa('debug_signature_' + userId); // Not a real signature

      return `${header}.${payload}.${signature}`;
    }

    throw error;
  }
};

/**
 * Ensure all users involved in a task are added to the chat channel
 * @param taskId - The task ID
 * @returns The updated channel
 */
export const ensureTaskParticipantsInChannel = async (taskId: string): Promise<Channel | null> => {
  try {
    // Import Supabase client
    const { supabase } = await import('@/integrations/supabase/client');

    // Get the task details with more comprehensive information
    const { data: task, error: taskError } = await supabase
      .from('tasks')
      .select(`
        id,
        title,
        user_id,
        assigned_to,
        visibility,
        status,
        organization_id,
        getstream_channel_id,
        chat_migrated_to_stream,
        offers (
          id,
          user_id,
          status
        ),
        organization:organizations(
          id,
          name
        )
      `)
      .eq('id', taskId)
      .single();

    if (taskError || !task) {
      console.error('[ensureTaskParticipantsInChannel] Error getting task:', taskError);
      return null;
    }

    // Collect all user IDs involved in the task
    const userIds = new Set<string>();

    // Add task creator
    if (task.user_id) userIds.add(task.user_id);

    // Add assigned user if any
    if (task.assigned_to) userIds.add(task.assigned_to);

    // Add organization admin if available - we'll need to query for this separately
    if (task.organization_id) {
      try {
        const { data: adminProfile } = await supabase
          .from('profiles')
          .select('id')
          .eq('organization_id', task.organization_id)
          .eq('role', 'admin')
          .limit(1)
          .single();

        if (adminProfile?.id) {
          userIds.add(adminProfile.id);
        }
      } catch (adminError) {
        // Ignore admin lookup errors - not critical for chat functionality
        console.warn('[ensureTaskParticipantsInChannel] Could not find organization admin:', adminError);
      }
    }

    // Add users who made offers
    if (task.offers && task.offers.length > 0) {
      task.offers.forEach((offer: any) => {
        if (offer.user_id) userIds.add(offer.user_id);
      });
    }

    // Get all users who have interacted with this task via chat
    try {
      // Check if there are any chat messages for this task
      const { data: messages, error: messagesError } = await supabase
        .from('task_messages')
        .select('sender_id')
        .eq('task_id', taskId)
        .not('sender_id', 'eq', '00000000-0000-0000-0000-000000000000'); // Exclude system messages

      if (!messagesError && messages && messages.length > 0) {
        messages.forEach((message: any) => {
          if (message.sender_id) userIds.add(message.sender_id);
        });
      }
    } catch (chatError) {
      console.warn('[ensureTaskParticipantsInChannel] Error getting chat participants:', chatError);
    }

    // Convert Set to Array
    const members = Array.from(userIds);

    if (members.length === 0) {
      console.warn('[ensureTaskParticipantsInChannel] No participants found for task:', taskId);
      return null;
    }

    console.log(`[ensureTaskParticipantsInChannel] Found ${members.length} participants for task ${taskId}:`, members);

    // Get or create the channel
    try {
      const channel = await getTaskChannel(taskId, true, task.title, members);

      // Update the task to mark it as migrated to GetStream if needed
      if (!task.chat_migrated_to_stream || !task.getstream_channel_id) {
        await supabase
          .from('tasks')
          .update({
            chat_migrated_to_stream: true,
            getstream_channel_id: channel.id
          })
          .eq('id', taskId);

        console.log(`[ensureTaskParticipantsInChannel] Updated task ${taskId} to mark as migrated to GetStream`);
      }

      return channel;
    } catch (error) {
      console.error('[ensureTaskParticipantsInChannel] Error getting/creating channel:', error);
      return null;
    }
  } catch (error) {
    console.error('[ensureTaskParticipantsInChannel] Error ensuring participants:', error);
    return null;
  }
};

/**
 * Get all task channels for a user
 * This function ensures that all tasks the user is involved with have corresponding GetStream channels
 * @param userId - The user ID
 * @returns Array of channels
 */
export const getAllUserTaskChannels = async (userId: string): Promise<Channel[]> => {
  try {
    if (!userId) {
      console.error('[getAllUserTaskChannels] No user ID provided');
      return [];
    }

    console.log('[getAllUserTaskChannels] Getting all task channels for user:', userId);

    // Import Supabase client
    const { supabase } = await import('@/integrations/supabase/client');

    // Get all tasks the user is involved with
    const { data: userTasks, error: tasksError } = await supabase
      .from('tasks')
      .select(`
        id,
        title,
        user_id,
        assigned_to,
        visibility,
        status,
        getstream_channel_id,
        chat_migrated_to_stream
      `)
      .or(`user_id.eq.${userId},assigned_to.eq.${userId}`);

    if (tasksError) {
      console.error('[getAllUserTaskChannels] Error fetching user tasks:', tasksError);
      return [];
    }

    console.log(`[getAllUserTaskChannels] Found ${userTasks?.length || 0} tasks for user`);

    if (!userTasks || userTasks.length === 0) {
      return [];
    }

    // Also check for tasks where the user has made offers
    const { data: offerTasks, error: offersError } = await supabase
      .from('offers')
      .select(`
        task_id,
        task:tasks(
          id,
          title,
          user_id,
          assigned_to,
          visibility,
          status,
          getstream_channel_id,
          chat_migrated_to_stream
        )
      `)
      .eq('user_id', userId);

    if (!offersError && offerTasks && offerTasks.length > 0) {
      console.log(`[getAllUserTaskChannels] Found ${offerTasks.length} offer tasks for user`);

      // Add offer tasks to the user tasks list
      offerTasks.forEach(offerTask => {
        if (offerTask.task && !userTasks.some(task => task.id === offerTask.task.id)) {
          userTasks.push(offerTask.task);
        }
      });
    }

    // Get the client
    const client = getStreamClient();

    // Create channels for all tasks
    const channels: Channel[] = [];

    for (const task of userTasks) {
      try {
        // Ensure the channel exists and the user is a member
        const channel = await ensureTaskParticipantsInChannel(task.id);

        if (channel) {
          channels.push(channel);
        }
      } catch (channelError) {
        console.error(`[getAllUserTaskChannels] Error ensuring channel for task ${task.id}:`, channelError);
      }
    }

    console.log(`[getAllUserTaskChannels] Successfully created/verified ${channels.length} channels for user ${userId}`);
    return channels;
  } catch (error) {
    console.error('[getAllUserTaskChannels] Error getting all user task channels:', error);
    return [];
  }
};

/**
 * Sync all user channels with tasks
 * This function ensures that all GetStream channels match the tasks in the database
 * @param userId - The user ID
 */
export const syncUserChannelsWithTasks = async (userId: string): Promise<void> => {
  try {
    if (!userId) {
      console.error('[syncUserChannelsWithTasks] No user ID provided');
      return;
    }

    console.log('[syncUserChannelsWithTasks] Syncing channels with tasks for user:', userId);

    // Get the client
    const client = getStreamClient();

    // First, get all channels from GetStream
    const filter = { type: 'messaging', members: { $in: [userId] } };
    const sort = { last_message_at: -1 };

    const streamChannels = await client.queryChannels(filter, sort, {
      watch: true,
      state: true,
      presence: true,
      limit: 50,
    });

    console.log(`[syncUserChannelsWithTasks] Found ${streamChannels.length} GetStream channels for user`);

    // Get all task channels from the database
    const taskChannels = await getAllUserTaskChannels(userId);
    console.log(`[syncUserChannelsWithTasks] Found ${taskChannels.length} task channels for user`);

    // Check for channels in GetStream that don't have a corresponding task
    const streamChannelIds = streamChannels.map(channel => channel.id);
    const taskChannelIds = taskChannels.map(channel => channel.id);

    // Find channels in GetStream that don't have a corresponding task
    const orphanedChannels = streamChannels.filter(channel =>
      channel.id.startsWith('task-') &&
      !taskChannelIds.includes(channel.id)
    );

    console.log(`[syncUserChannelsWithTasks] Found ${orphanedChannels.length} orphaned channels`);

    // Find task channels that don't exist in GetStream
    const missingChannels = taskChannels.filter(channel =>
      !streamChannelIds.includes(channel.id)
    );

    console.log(`[syncUserChannelsWithTasks] Found ${missingChannels.length} missing channels`);

    // Add the user to all missing channels
    for (const channel of missingChannels) {
      try {
        await channel.addMembers([userId]);
        console.log(`[syncUserChannelsWithTasks] Added user to channel: ${channel.id}`);
      } catch (addError) {
        console.error(`[syncUserChannelsWithTasks] Error adding user to channel ${channel.id}:`, addError);
      }
    }

    console.log('[syncUserChannelsWithTasks] Sync completed successfully');
  } catch (error) {
    console.error('[syncUserChannelsWithTasks] Error syncing user channels with tasks:', error);
  }
};

export default {
  getStreamClient,
  connectUser,
  disconnectUser,
  createTaskChannel,
  getTaskChannel,
  createOrUpdateTaskChannel,
  ensureTaskParticipantsInChannel,
  getAllUserTaskChannels,
  syncUserChannelsWithTasks,
  generateToken,
};
