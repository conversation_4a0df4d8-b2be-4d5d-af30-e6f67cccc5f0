// <PERSON><PERSON>t to restart the server
const { exec } = require('child_process');

console.log('Restarting the server...');

// Kill the current server process
exec('taskkill /F /IM node.exe', (error, stdout, stderr) => {
  if (error) {
    console.log('Warning: Could not kill existing Node processes. This is normal if no Node processes were running.');
  } else {
    console.log('Killed existing Node processes.');
  }
  
  // Wait a moment before starting the server again
  setTimeout(() => {
    console.log('Starting the server again...');
    
    // Start the server again
    exec('npm run dev:all', (error, stdout, stderr) => {
      if (error) {
        console.error('Error restarting server:', error);
        return;
      }
      
      console.log('Server restarted successfully!');
      console.log('Please refresh your browser to test the payment functionality.');
    });
  }, 2000);
});