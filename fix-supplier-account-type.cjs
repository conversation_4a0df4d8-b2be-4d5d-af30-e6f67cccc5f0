// Script to fix the supplier account type
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixSupplierAccountType() {
  try {
    const supplierEmail = '<EMAIL>';
    
    console.log(`Fixing account type for supplier: ${supplierEmail}`);
    
    // Get user ID from auth
    const { data: authData } = await supabase.auth.admin.listUsers();
    const user = authData.users.find(u => u.email === supplierEmail);
    
    if (!user) {
      console.error(`User with email ${supplierEmail} not found in auth system.`);
      return;
    }
    
    console.log(`User ID: ${user.id}`);
    
    // Get current profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      console.error('Error fetching profile:', profileError);
      return;
    }
    
    if (!profile) {
      console.error(`Profile for user ${user.id} not found.`);
      return;
    }
    
    console.log('Current profile:');
    console.log(`- ID: ${profile.id}`);
    console.log(`- Email: ${Array.isArray(profile.email) ? profile.email[0] : profile.email}`);
    console.log(`- Role: ${profile.role}`);
    console.log(`- Account Type: ${profile.account_type}`);
    
    // Update the account type to 'supplier'
    console.log('\nUpdating account type to "supplier"...');
    
    const { data: updatedProfile, error: updateError } = await supabase
      .from('profiles')
      .update({
        account_type: 'supplier',
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id)
      .select()
      .single();
    
    if (updateError) {
      console.error('Error updating profile:', updateError);
      return;
    }
    
    console.log('Profile updated successfully:');
    console.log(`- ID: ${updatedProfile.id}`);
    console.log(`- Email: ${Array.isArray(updatedProfile.email) ? updatedProfile.email[0] : updatedProfile.email}`);
    console.log(`- Role: ${updatedProfile.role}`);
    console.log(`- Account Type: ${updatedProfile.account_type}`);
    
    console.log('\nFix completed. The supplier should now be able to see public tasks.');
  } catch (error) {
    console.error('Error fixing supplier account type:', error);
  }
}

fixSupplierAccountType();
