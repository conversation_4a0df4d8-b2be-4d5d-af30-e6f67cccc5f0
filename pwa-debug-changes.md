# GetStream PWA Debugging Changes

This document outlines the changes made to debug the GetStream chat UI not loading in the PWA deployment.

## Changes Made

1. Added detailed logging to PWAGetStreamChatView component
2. Added detailed logging to PWASimplifiedChat component
3. Enhanced the generateToken function with more detailed logging and fallback URLs
4. Improved the getStreamApiUrl function to handle PWA URLs better
5. Added detailed logging to the useGetStreamChat hook

## Detailed Changes

### 1. PWAGetStreamChatView.tsx

```typescript
// Enhanced debug logging
useEffect(() => {
  console.log('[PWAGetStreamChatView] Chat state:', {
    client: !!client,
    channel: !!channel,
    channelId: channel?.id,
    channelType: channel?.type,
    channelData: channel?.data,
    isLoading,
    hasError: !!streamError,
    streamErrorMessage: streamError?.message,
    isPWA: typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches,
    isOnline: navigator.onLine,
    messages: messages?.length || 0,
    userAgent: navigator.userAgent,
    url: window.location.href,
    apiKey: import.meta.env.VITE_GETSTREAM_API_KEY || (typeof window !== 'undefined' && window.env?.VITE_GETSTREAM_API_KEY),
    apiBaseUrl: typeof window !== 'undefined' ? window.location.origin : '',
    taskId,
    channelIdParam: channelId
  });

  // Log environment variables and window.env
  console.log('[PWAGetStreamChatView] Environment variables:', {
    VITE_GETSTREAM_API_KEY: import.meta.env.VITE_GETSTREAM_API_KEY,
    windowEnv: typeof window !== 'undefined' ? window.env : undefined,
    windowEnvGetStreamKey: typeof window !== 'undefined' && window.env ? window.env.VITE_GETSTREAM_API_KEY : undefined
  });
}, [client, channel, isLoading, streamError, messages, taskId, channelId]);
```

### 2. PWASimplifiedChat.tsx

```typescript
// Enhanced logging for debugging
useEffect(() => {
  console.log('[PWASimplifiedChat] Component state:', {
    hasClient: !!client,
    hasChannel: !!channel,
    messagesCount: messages?.length || 0,
    isSending,
    clientConnected: client?.isConnected?.() || false,
    clientUserID: client?.userID,
    channelId: channel?.id,
    channelType: channel?.type,
    channelCid: channel?.cid,
    error
  });
}, [client, channel, messages, isSending, error]);
```

### 3. getstream/client.ts (generateToken function)

```typescript
export const generateToken = async (userId: string): Promise<string> => {
  try {
    // Log environment information
    console.log('[generateToken] Environment info:', {
      apiKey: apiKey ? 'present' : 'missing',
      isPWA: typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches,
      origin: typeof window !== 'undefined' ? window.location.origin : 'not in browser',
      hostname: typeof window !== 'undefined' ? window.location.hostname : 'not in browser',
      protocol: typeof window !== 'undefined' ? window.location.protocol : 'not in browser',
      userId
    });

    // For PWA, try multiple fallback URLs if the relative URL fails
    if (typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches) {
      // Try several fallback URLs in sequence
      const fallbackUrls = [
        '/api/getstream/token',                                // Relative path
        `${window.location.origin}/api/getstream/token`,       // Absolute path with origin
        'https://class-tasker-connect.vercel.app/api/getstream/token' // Hardcoded production URL
      ];
    }
  } catch (error) {
    console.error('[generateToken] Error generating token:', error);
  }
};
```

### 4. apiConfig.ts (getStreamApiUrl function)

```typescript
export const getStreamApiUrl = (endpoint: string): string => {
  // Ensure endpoint starts with a slash
  const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  
  // Get the base URL
  const baseUrl = getApiBaseUrl();
  
  // For PWA in production, try to use a more reliable URL construction
  const isPWA = typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches;
  const isProduction = process.env.NODE_ENV === 'production';
  
  // Log environment details
  console.log('[getStreamApiUrl] Environment:', {
    isPWA,
    isProduction,
    baseUrl,
    endpoint: normalizedEndpoint,
    hostname: typeof window !== 'undefined' ? window.location.hostname : 'not in browser',
    origin: typeof window !== 'undefined' ? window.location.origin : 'not in browser'
  });
  
  // Construct the URL
  let url;
  if (isPWA && isProduction) {
    // For PWA in production, use the full origin to avoid relative URL issues
    const origin = typeof window !== 'undefined' ? window.location.origin : '';
    url = `${origin}/api/getstream${normalizedEndpoint}`;
  } else {
    // Standard URL construction
    url = `${baseUrl}/api/getstream${normalizedEndpoint}`;
  }
  
  return url;
};
```

### 5. use-getstream-chat.ts

```typescript
// Initialize the client and channel
useEffect(() => {
  // Log environment information
  console.log('[useGetStreamChat] Environment info:', {
    isPWA: typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches,
    isOnline: typeof navigator !== 'undefined' ? navigator.onLine : 'unknown',
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
    apiKey: import.meta.env.VITE_GETSTREAM_API_KEY || (typeof window !== 'undefined' && window.env?.VITE_GETSTREAM_API_KEY) ? 'present' : 'missing',
    origin: typeof window !== 'undefined' ? window.location.origin : 'not in browser',
    hostname: typeof window !== 'undefined' ? window.location.hostname : 'not in browser',
    protocol: typeof window !== 'undefined' ? window.location.protocol : 'not in browser',
    pathname: typeof window !== 'undefined' ? window.location.pathname : 'not in browser'
  });
}, []);
```
