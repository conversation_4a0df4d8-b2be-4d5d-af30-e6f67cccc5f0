import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { CheckCircle, XCircle, RefreshCw, Database, Key } from 'lucide-react';
import { testSupabaseConnection, testSupabaseAuth } from '@/utils/test-supabase-connection';

type TestResult = {
  success: boolean;
  message: string;
  data?: any;
  error?: any;
};

const SupabaseConnectionTest = () => {
  const [connectionResult, setConnectionResult] = useState<TestResult | null>(null);
  const [authResult, setAuthResult] = useState<TestResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const runTests = async () => {
    setIsLoading(true);
    setConnectionResult(null);
    setAuthResult(null);
    
    try {
      // Test database connection
      const connResult = await testSupabaseConnection();
      setConnectionResult(connResult);
      
      // Test authentication
      const authRes = await testSupabaseAuth();
      setAuthResult(authRes);
    } catch (error) {
      console.error('Error running tests:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    runTests();
  }, []);

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Supabase Connection Test
        </CardTitle>
        <CardDescription>
          Check if the application can connect to Supabase and perform basic operations
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Database Connection Test */}
        <div className="space-y-2">
          <h3 className="text-lg font-medium flex items-center gap-2">
            <Database className="h-4 w-4" /> 
            Database Connection
          </h3>
          
          {connectionResult === null ? (
            <div className="flex items-center gap-2 text-muted-foreground">
              <RefreshCw className="h-4 w-4 animate-spin" />
              Testing database connection...
            </div>
          ) : (
            <Alert variant={connectionResult.success ? "default" : "destructive"}>
              <div className="flex items-center gap-2">
                {connectionResult.success ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <XCircle className="h-4 w-4" />
                )}
                <AlertTitle>{connectionResult.success ? 'Success' : 'Error'}</AlertTitle>
              </div>
              <AlertDescription className="mt-2">
                {connectionResult.message}
                
                {connectionResult.success && connectionResult.data && (
                  <div className="mt-2 text-sm">
                    <div>Total tasks: {connectionResult.data.count}</div>
                    {connectionResult.data.sampleTasks && connectionResult.data.sampleTasks.length > 0 && (
                      <div className="mt-1">
                        <div>Sample tasks:</div>
                        <ul className="list-disc pl-5 mt-1">
                          {connectionResult.data.sampleTasks.map((task: any) => (
                            <li key={task.id}>{task.title}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}
                
                {!connectionResult.success && connectionResult.error && (
                  <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                    {JSON.stringify(connectionResult.error, null, 2)}
                  </pre>
                )}
              </AlertDescription>
            </Alert>
          )}
        </div>
        
        {/* Authentication Test */}
        <div className="space-y-2">
          <h3 className="text-lg font-medium flex items-center gap-2">
            <Key className="h-4 w-4" /> 
            Authentication Status
          </h3>
          
          {authResult === null ? (
            <div className="flex items-center gap-2 text-muted-foreground">
              <RefreshCw className="h-4 w-4 animate-spin" />
              Checking authentication status...
            </div>
          ) : (
            <Alert>
              <div className="flex items-center gap-2">
                {authResult.data?.isAuthenticated ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <div className="h-4 w-4 text-yellow-500">●</div>
                )}
                <AlertTitle>
                  {authResult.data?.isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
                </AlertTitle>
              </div>
              <AlertDescription className="mt-2">
                {authResult.message}
                
                {authResult.data?.isAuthenticated && authResult.data?.session && (
                  <div className="mt-2 text-sm">
                    <div>User ID: {authResult.data.session.user.id}</div>
                    <div>Email: {authResult.data.session.user.email}</div>
                    <div>Session expires: {new Date(authResult.data.session.expires_at * 1000).toLocaleString()}</div>
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Button 
          onClick={runTests} 
          disabled={isLoading}
          className="flex items-center gap-2"
        >
          {isLoading && <RefreshCw className="h-4 w-4 animate-spin" />}
          {!isLoading && <RefreshCw className="h-4 w-4" />}
          Run Tests Again
        </Button>
      </CardFooter>
    </Card>
  );
};

export default SupabaseConnectionTest;
