// Script to fix profile email format
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixProfileEmail() {
  try {
    console.log('Checking and fixing profile email format...');

    // Get the admin profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', '4288cd97-e3ed-4e1d-8d22-abdc0d3f28bd')
      .single();

    if (profileError) {
      console.error('Error fetching profile:', profileError);
      return;
    }

    console.log('Current profile data:');
    console.log(profile);

    // Check if email is in the correct format
    const email = profile.email;
    console.log(`Current email value: ${email} (type: ${typeof email})`);

    if (typeof email !== 'string' || email.includes('=>')) {
      // Email is not in the correct format, fix it
      console.log('Email is not in the correct format, fixing...');

      // Since email is stored as an array, we need to update it as an array
      let fixedEmail = ['<EMAIL>'];

      // Update the profile with the fixed email
      const { data: updatedProfile, error: updateError } = await supabase
        .from('profiles')
        .update({ email: fixedEmail })
        .eq('id', profile.id)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating profile:', updateError);
        return;
      }

      console.log('Profile updated with fixed email:');
      console.log(updatedProfile);
    } else {
      console.log('Email is already in the correct format.');
    }

    // Also check the supplier profile
    const { data: supplierProfile, error: supplierError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', '18625693-2496-45a4-a1d8-675a9bf2683b')
      .single();

    if (supplierError) {
      console.error('Error fetching supplier profile:', supplierError);
      return;
    }

    console.log('\nCurrent supplier profile data:');
    console.log(supplierProfile);

    // Check if supplier email is in the correct format
    const supplierEmail = supplierProfile.email;
    console.log(`Current supplier email value: ${supplierEmail} (type: ${typeof supplierEmail})`);

    if (typeof supplierEmail !== 'string' || supplierEmail.includes('=>')) {
      // Supplier email is not in the correct format, fix it
      console.log('Supplier email is not in the correct format, fixing...');

      // Since email is stored as an array, we need to update it as an array
      let fixedSupplierEmail = ['<EMAIL>'];

      // Update the supplier profile with the fixed email
      const { data: updatedSupplier, error: updateSupplierError } = await supabase
        .from('profiles')
        .update({ email: fixedSupplierEmail })
        .eq('id', supplierProfile.id)
        .select()
        .single();

      if (updateSupplierError) {
        console.error('Error updating supplier profile:', updateSupplierError);
        return;
      }

      console.log('Supplier profile updated with fixed email:');
      console.log(updatedSupplier);
    } else {
      console.log('Supplier email is already in the correct format.');
    }

    console.log('\nEmail format fix completed.');
  } catch (error) {
    console.error('Error fixing profile email:', error);
  }
}

fixProfileEmail();
