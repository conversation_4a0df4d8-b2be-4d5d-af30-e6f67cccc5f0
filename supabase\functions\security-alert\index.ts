import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface SecurityEvent {
  type: 'auth_failure' | 'permission_denied' | 'suspicious_activity' | 'data_access' | 'admin_action';
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  userEmail?: string;
  action: string;
  resource?: string;
  details?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Parse request body
    const requestBody = await req.json()
    console.log('Raw request body:', requestBody)

    // Handle Supabase test interface (sends { name: "Functions" })
    if (requestBody.name === "Functions") {
      console.log('Test request from Supabase Dashboard detected')
      // Create a test event for Supabase dashboard testing
      const testEvent: SecurityEvent = {
        type: 'suspicious_activity',
        severity: 'critical',
        userEmail: '<EMAIL>',
        action: 'supabase_dashboard_test',
        resource: 'edge_function_test',
        details: {
          message: 'Test from Supabase Dashboard',
          testType: 'dashboard_test'
        },
        ipAddress: '127.0.0.1',
        userAgent: 'Supabase Dashboard',
        timestamp: new Date().toISOString()
      }

      // Process the test event
      await processSecurityEvent(testEvent, supabase)

      return new Response(
        JSON.stringify({
          success: true,
          message: 'Test security alert processed successfully',
          testEvent: testEvent
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    }

    // Handle both direct event and wrapped event formats
    const event: SecurityEvent = requestBody.event || requestBody

    if (!event || !event.type) {
      console.error('Invalid event data received:', requestBody)
      return new Response(
        JSON.stringify({
          error: 'Invalid event data',
          details: 'Event object is missing or invalid. For testing, use Supabase Dashboard test button or send proper SecurityEvent object.',
          received: requestBody,
          expectedFormat: {
            type: 'auth_failure | permission_denied | suspicious_activity | data_access | admin_action',
            severity: 'low | medium | high | critical',
            action: 'string describing the action',
            userEmail: '<EMAIL> (optional)',
            timestamp: 'ISO string'
          }
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        },
      )
    }

    // Process the security event
    await processSecurityEvent(event, supabase)

    return new Response(
      JSON.stringify({ success: true, message: 'Security alert processed' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('Error processing security alert:', error)

    return new Response(
      JSON.stringify({
        error: 'Failed to process security alert',
        details: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      },
    )
  }
})

async function processSecurityEvent(event: SecurityEvent, supabase: any) {
  console.log('Processing security event:', {
    type: event.type,
    severity: event.severity,
    action: event.action,
    userEmail: event.userEmail,
    timestamp: event.timestamp
  })

  // Store security event in database
  const { error: insertError } = await supabase
    .from('security_events')
    .insert({
      type: event.type,
      severity: event.severity,
      user_id: event.userId,
      user_email: event.userEmail,
      action: event.action,
      resource: event.resource,
      details: event.details,
      ip_address: event.ipAddress,
      user_agent: event.userAgent,
      timestamp: event.timestamp
    })

  if (insertError) {
    console.error('Error storing security event:', insertError)
    // Continue processing even if storage fails
  } else {
    console.log('Security event stored successfully')
  }

  // Handle critical and high severity events
  if (event.severity === 'critical' || event.severity === 'high') {
    await handleCriticalSecurityEvent(event, supabase)
  }

  // Send email notifications for critical events (but don't fail if email fails)
  if (event.severity === 'critical') {
    try {
      await sendSecurityAlert(event, supabase)
    } catch (emailError) {
      console.error('Email notification failed, but continuing:', emailError)
    }
  }
}

async function handleCriticalSecurityEvent(event: SecurityEvent, supabase: any) {
  console.log('Handling critical security event:', event.type)

  // Create a security incident record
  const { error: incidentError } = await supabase
    .from('security_incidents')
    .insert({
      event_type: event.type,
      severity: event.severity,
      description: `${event.action} - ${event.details ? JSON.stringify(event.details) : 'No additional details'}`,
      user_id: event.userId,
      user_email: event.userEmail,
      ip_address: event.ipAddress,
      status: 'open',
      created_at: new Date().toISOString()
    })

  if (incidentError) {
    console.error('Error creating security incident:', incidentError)
  }

  // Implement automatic response based on event type
  switch (event.type) {
    case 'suspicious_activity':
      if (event.severity === 'critical') {
        // Could implement automatic user suspension here
        console.log('Critical suspicious activity detected - consider user suspension')
      }
      break

    case 'auth_failure':
      if (event.details?.attempts && event.details.attempts > 10) {
        // Could implement IP blocking here
        console.log('Multiple auth failures detected - consider IP blocking')
      }
      break

    case 'permission_denied':
      if (event.severity === 'critical') {
        // Log potential privilege escalation attempts
        console.log('Critical permission denied - potential privilege escalation')
      }
      break
  }
}

async function sendSecurityAlert(event: SecurityEvent, supabase: any) {
  try {
    // Get admin users to notify
    const { data: admins, error: adminError } = await supabase
      .from('profiles')
      .select('email, first_name, last_name')
      .eq('is_site_admin', true)

    if (adminError) {
      console.error('Error fetching admin users:', adminError)
      return
    }

    if (!admins || admins.length === 0) {
      console.log('No admin users found to notify')
      return
    }

    // Prepare email content
    const subject = `🚨 CRITICAL Security Alert - ${event.action}`
    const emailBody = `
      <h2>Critical Security Alert</h2>
      <p>A critical security event has been detected on ClassTasker:</p>

      <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
        <tr style="background-color: #f8f9fa;">
          <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Event Type:</td>
          <td style="padding: 10px; border: 1px solid #ddd;">${event.type}</td>
        </tr>
        <tr>
          <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Severity:</td>
          <td style="padding: 10px; border: 1px solid #ddd; color: red; font-weight: bold;">${event.severity.toUpperCase()}</td>
        </tr>
        <tr style="background-color: #f8f9fa;">
          <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Action:</td>
          <td style="padding: 10px; border: 1px solid #ddd;">${event.action}</td>
        </tr>
        <tr>
          <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">User:</td>
          <td style="padding: 10px; border: 1px solid #ddd;">${event.userEmail || 'Unknown'}</td>
        </tr>
        <tr style="background-color: #f8f9fa;">
          <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">IP Address:</td>
          <td style="padding: 10px; border: 1px solid #ddd;">${event.ipAddress || 'Unknown'}</td>
        </tr>
        <tr>
          <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Timestamp:</td>
          <td style="padding: 10px; border: 1px solid #ddd;">${new Date(event.timestamp).toLocaleString()}</td>
        </tr>
        ${event.details ? `
        <tr style="background-color: #f8f9fa;">
          <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Details:</td>
          <td style="padding: 10px; border: 1px solid #ddd;"><pre>${JSON.stringify(event.details, null, 2)}</pre></td>
        </tr>
        ` : ''}
      </table>

      <p><strong>Immediate Action Required:</strong></p>
      <ul>
        <li>Review the security dashboard immediately</li>
        <li>Investigate the user account if applicable</li>
        <li>Check for any related suspicious activities</li>
        <li>Consider implementing additional security measures</li>
      </ul>

      <p>Access the security dashboard: <a href="https://classtasker.com/admin/security">https://classtasker.com/admin/security</a></p>

      <hr style="margin: 20px 0;">
      <p style="font-size: 12px; color: #666;">
        This is an automated security alert from ClassTasker.
        Please do not reply to this email.
      </p>
    `

    // Send email to all admins (but don't fail the whole function if email fails)
    for (const admin of admins) {
      try {
        console.log(`Attempting to send security alert to ${admin.email}`)

        // Use support-email-sender function (same as invitations use)
        console.log(`Preparing to send security alert to ${admin.email}`)
        console.log(`Email subject: ${subject}`)

        const emailRequest = {
          from: 'ClassTasker Security <<EMAIL>>',
          to: admin.email,
          subject: subject,
          name: 'ClassTasker Security System',
          email: '<EMAIL>',
          support_type: 'Security Alert',
          message: `Critical security event detected: ${event.action}`,
          html_content: emailBody // Use the HTML content directly
        }

        console.log('Email request payload:', JSON.stringify(emailRequest, null, 2))

        const { data: emailData, error: emailError } = await supabase.functions.invoke('support-email-sender', {
          body: emailRequest
        })

        if (emailError) {
          console.error(`Error sending security alert to ${admin.email}:`, emailError)
          console.log('Email function may not be deployed or configured properly')
          console.log('Email request that failed:', JSON.stringify(emailRequest, null, 2))
        } else {
          console.log(`Security alert sent successfully to ${admin.email}`)
          console.log('Email response data:', emailData)
        }
      } catch (emailErr) {
        console.error(`Failed to send email to ${admin.email}:`, emailErr)
        console.log('This is not a critical error - security event was still logged')
      }
    }

    console.log(`Email notification process completed for ${admins.length} admin(s)`)

  } catch (error) {
    console.error('Error sending security alerts:', error)
  }
}

/*
To deploy this function:
1. Make sure you have the Supabase CLI installed
2. Run: supabase functions deploy security-alert
3. The function will be available at: https://your-project.supabase.co/functions/v1/security-alert
*/
