/**
 * GetStream API Routes
 *
 * This file contains routes for GetStream integration
 */

import express from 'express';
import { StreamChat } from 'stream-chat';
import { createClient } from '@supabase/supabase-js';

const router = express.Router();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Initialize GetStream client
const streamApiKey = process.env.GETSTREAM_API_KEY;
const streamApiSecret = process.env.GETSTREAM_API_SECRET;
const serverClient = StreamChat.getInstance(streamApiKey, streamApiSecret);

/**
 * Generate a token for a user
 * POST /api/getstream/token
 */
router.post('/token', async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    console.log('Generating token for user:', userId);

    // Generate a token for the user
    const token = serverClient.createToken(userId);

    console.log('Token generated successfully');

    res.json({ token });
  } catch (error) {
    console.error('Error generating token:', error);
    res.status(500).json({ error: 'Failed to generate token' });
  }
});

/**
 * Create a channel for a task
 * POST /api/getstream/channels
 */
router.post('/channels', async (req, res) => {
  try {
    const { taskId, taskTitle, members } = req.body;

    if (!taskId || !taskTitle) {
      return res.status(400).json({ error: 'Task ID and title are required' });
    }

    console.log('Creating channel for task:', taskId);

    // Create a channel
    const channelId = `task-${taskId}`;

    // Use provided members or create an empty array
    const channelMembers = members && Array.isArray(members) ? members : [];

    const channel = serverClient.channel('messaging', channelId, {
      name: taskTitle,
      members: channelMembers,
      task_id: taskId,
    });

    await channel.create();

    console.log('Channel created successfully:', channelId);

    res.json({
      channelId,
      channel: channel.id,
      members: channel.state.members
    });
  } catch (error) {
    console.error('Error creating channel:', error);
    res.status(500).json({ error: 'Failed to create channel' });
  }
});

/**
 * Migrate a task's chat to GetStream
 * POST /api/getstream/migrate-task-chat
 */
router.post('/migrate-task-chat', async (req, res) => {
  try {
    const { taskId, taskTitle } = req.body;

    if (!taskId || !taskTitle) {
      return res.status(400).json({ error: 'Task ID and title are required' });
    }

    console.log('Migrating chat for task:', taskId);

    // Create a channel
    const channelId = `task-${taskId}`;
    const channel = serverClient.channel('messaging', channelId, {
      name: taskTitle,
      task_id: taskId,
    });

    await channel.create();

    console.log('Channel created successfully:', channelId);

    // Mark the task as migrated in Supabase
    const { error: updateError } = await supabase
      .from('tasks')
      .update({
        chat_migrated_to_stream: true,
        getstream_channel_id: channelId
      })
      .eq('id', taskId);

    if (updateError) {
      console.error('Error updating task:', updateError);
      return res.status(500).json({ error: 'Failed to update task' });
    }

    console.log('Task marked as migrated:', taskId);

    res.json({
      success: true,
      channelId,
      channel: channel.id
    });
  } catch (error) {
    console.error('Error migrating task chat:', error);
    res.status(500).json({ error: 'Failed to migrate task chat' });
  }
});

/**
 * Add a member to a channel
 * POST /api/getstream/channels/:channelId/members
 */
router.post('/channels/:channelId/members', async (req, res) => {
  try {
    const { channelId } = req.params;
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Get the channel
    const channel = serverClient.channel('messaging', channelId);

    // Add the member
    await channel.addMembers([userId]);

    res.json({ success: true });
  } catch (error) {
    console.error('Error adding member to channel:', error);
    res.status(500).json({ error: 'Failed to add member to channel' });
  }
});

/**
 * Remove a member from a channel
 * DELETE /api/getstream/channels/:channelId/members/:userId
 */
router.delete('/channels/:channelId/members/:userId', async (req, res) => {
  try {
    const { channelId, userId } = req.params;

    // Get the channel
    const channel = serverClient.channel('messaging', channelId);

    // Remove the member
    await channel.removeMembers([userId]);

    res.json({ success: true });
  } catch (error) {
    console.error('Error removing member from channel:', error);
    res.status(500).json({ error: 'Failed to remove member from channel' });
  }
});

/**
 * Delete a channel
 * DELETE /api/getstream/channels/:channelId
 */
router.delete('/channels/:channelId', async (req, res) => {
  try {
    const { channelId } = req.params;

    // Get the channel
    const channel = serverClient.channel('messaging', channelId);

    // Delete the channel
    await channel.delete();

    res.json({ success: true });
  } catch (error) {
    console.error('Error deleting channel:', error);
    res.status(500).json({ error: 'Failed to delete channel' });
  }
});

export default router;
