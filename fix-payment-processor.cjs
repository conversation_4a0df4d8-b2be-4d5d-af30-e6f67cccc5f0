// Script to fix the EnhancedPaymentProcessor.tsx file
const fs = require('fs');
const path = require('path');

// Paths
const processorPath = path.join(__dirname, 'src', 'components', 'stripe', 'EnhancedPaymentProcessor.tsx');
const backupPath = path.join(__dirname, 'src', 'components', 'stripe', 'EnhancedPaymentProcessor.tsx.bak');

// Create a backup of the original file
console.log('Creating backup of EnhancedPaymentProcessor.tsx...');
fs.copyFileSync(processorPath, backupPath);
console.log(`Backup created at ${backupPath}`);

// Read the file content
console.log('Reading EnhancedPaymentProcessor.tsx...');
let content = fs.readFileSync(processorPath, 'utf8');

// Remove the setup_future_usage parameter
if (content.includes('setup_future_usage: \'off_session\'')) {
  console.log('Found setup_future_usage parameter. Removing it...');
  
  // Remove the line with setup_future_usage
  content = content.replace(/\s*setup_future_usage: ['"]off_session['"],?\s*/g, '');
  
  // Write the modified content back to the file
  fs.writeFileSync(processorPath, content, 'utf8');
  console.log('Fix applied successfully!');
  
  console.log('\nChanges made:');
  console.log('- Removed setup_future_usage: \'off_session\' from confirmParams');
  
  console.log('\nPlease refresh the page and try the payment again.');
} else {
  console.log('Could not find setup_future_usage parameter in the file.');
  console.log('Please apply the fix manually:');
  console.log('1. Open src/components/stripe/EnhancedPaymentProcessor.tsx');
  console.log('2. Find the line with: setup_future_usage: \'off_session\'');
  console.log('3. Remove this line from the confirmParams object');
}

console.log('\nIf you need to restore the original file, run:');
console.log(`cp ${backupPath} ${processorPath}`);