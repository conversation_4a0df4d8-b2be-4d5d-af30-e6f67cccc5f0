// <PERSON>ript to create a test invitation
import { createClient } from '@supabase/supabase-js';
import { randomUUID } from 'crypto';

// Create Supabase client with service role key
const supabase = createClient(
  'https://qcnotlojmyvpqbbgoxbc.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFjbm90bG9qbXl2cHFiYmdveGJjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0Mzk4NzgzMywiZXhwIjoyMDU5NTYzODMzfQ.dRifBDSPK6GNYPialLfSeIQPu88lOSIsUVkynp2Be-U'
);

async function createTestInvitation() {
  try {
    // Generate a random token
    const token = randomUUID();

    // Create the invitation
    const { data, error } = await supabase
      .from('user_invitations')
      .insert({
        email: `test${Date.now()}@eboarddemo.com`,
        organization_id: 'b8db712b-7f48-44be-9269-be2cbbb2d213',
        role: 'teacher',
        invited_by: 'dfaf494b-a559-4191-872b-5b0ec8b9d613',
        status: 'pending',
        token: token,
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      })
      .select();

    if (error) {
      console.error('Error creating invitation:', error);
      return;
    }

    console.log('Created test invitation:');
    console.log(data[0]);

    // Generate the invitation URL
    const invitationUrl = `http://localhost:8082/invitation/accept?token=${token}`;
    console.log('\nInvitation URL:');
    console.log(invitationUrl);

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the function
createTestInvitation();
