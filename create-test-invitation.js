// <PERSON>ript to create a test invitation
import { createClient } from '@supabase/supabase-js';
import { randomUUID } from 'crypto';

// SECURITY: Create Supabase client with service role key - NEVER hardcode keys
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = 'https://qcnotlojmyvpqbbgoxbc.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Validate that the service role key is present
if (!supabaseServiceKey) {
  console.error('SECURITY ERROR: SUPABASE_SERVICE_ROLE_KEY not found in environment variables');
  console.error('This script requires the service role key to function');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createTestInvitation() {
  try {
    // Generate a random token
    const token = randomUUID();

    // Create the invitation
    const { data, error } = await supabase
      .from('user_invitations')
      .insert({
        email: `test${Date.now()}@eboarddemo.com`,
        organization_id: 'b8db712b-7f48-44be-9269-be2cbbb2d213',
        role: 'teacher',
        invited_by: 'dfaf494b-a559-4191-872b-5b0ec8b9d613',
        status: 'pending',
        token: token,
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      })
      .select();

    if (error) {
      console.error('Error creating invitation:', error);
      return;
    }

    console.log('Created test invitation:');
    console.log(data[0]);

    // Generate the invitation URL
    const invitationUrl = `http://localhost:8082/invitation/accept?token=${token}`;
    console.log('\nInvitation URL:');
    console.log(invitationUrl);

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the function
createTestInvitation();
