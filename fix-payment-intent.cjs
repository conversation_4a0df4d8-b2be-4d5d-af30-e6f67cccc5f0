// Script to fix the payment intent issue in EnhancedPaymentProcessor.tsx
const fs = require('fs');
const path = require('path');

// Paths
const processorPath = path.join(__dirname, 'src', 'components', 'stripe', 'EnhancedPaymentProcessor.tsx');
const backupPath = path.join(__dirname, 'src', 'components', 'stripe', 'EnhancedPaymentProcessor.tsx.bak6');

// Create a backup of the original file
console.log('Creating backup of EnhancedPaymentProcessor.tsx...');
fs.copyFileSync(processorPath, backupPath);
console.log(`Backup created at ${backupPath}`);

// Read the file content
console.log('Reading EnhancedPaymentProcessor.tsx...');
let content = fs.readFileSync(processorPath, 'utf8');

// Fix the payment intent issue
if (content.includes('const { clientSecret: secret } = await stripeService.createPaymentIntent(payment.id);')) {
  console.log('Fixing payment intent issue...');
  
  // Replace the destructuring with direct assignment
  content = content.replace(
    'const { clientSecret: secret } = await stripeService.createPaymentIntent(payment.id);',
    'const secret = await stripeService.createPaymentIntent(payment.id);'
  );
  
  // Write the modified content back to the file
  fs.writeFileSync(processorPath, content, 'utf8');
  console.log('Fix applied successfully!');
  
  console.log('\nChanges made:');
  console.log('- Fixed payment intent issue by changing destructuring to direct assignment');
  
  console.log('\nPlease refresh the page and try the payment again.');
} else {
  console.log('Could not find the payment intent issue in the file.');
  console.log('Please apply the fix manually:');
  console.log('1. Open src/components/stripe/EnhancedPaymentProcessor.tsx');
  console.log('2. Find the line with: const { clientSecret: secret } = await stripeService.createPaymentIntent(payment.id);');
  console.log('3. Replace it with: const secret = await stripeService.createPaymentIntent(payment.id);');
}

console.log('\nIf you need to restore the original file, run:');
console.log(`cp ${backupPath} ${processorPath}`);