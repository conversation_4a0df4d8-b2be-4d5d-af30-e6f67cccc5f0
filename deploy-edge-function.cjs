// Script to deploy the Supabase Edge Function
require('dotenv').config();
const { execSync } = require('child_process');

async function deployEdgeFunction() {
  try {
    console.log('Deploying Supabase Edge Function for sending invoice emails...');
    
    // Step 1: Set the Stripe secret key as an environment variable for the function
    console.log('\nStep 1: Setting environment variables...');
    
    const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
    if (!stripeSecretKey) {
      console.error('Missing STRIPE_SECRET_KEY environment variable.');
      return;
    }
    
    console.log('Setting STRIPE_SECRET_KEY environment variable...');
    execSync(`npx supabase secrets set STRIPE_SECRET_KEY="${stripeSecretKey}"`, { stdio: 'inherit' });
    
    // Step 2: Deploy the function
    console.log('\nStep 2: Deploying the function...');
    execSync('npx supabase functions deploy send-invoice-email', { stdio: 'inherit' });
    
    console.log('\nDeployment completed successfully!');
  } catch (error) {
    console.error('Error deploying Edge Function:', error);
  }
}

deployEdgeFunction();
