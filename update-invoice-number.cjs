// Script to update the invoice number in Supabase
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// The Stripe invoice ID and number
const stripeInvoiceId = 'in_1RGJ6iAwo0W7IrjomgEHnUkf';
const stripeInvoiceNumber = '5AAEF052-0001';

async function updateInvoiceNumber() {
  try {
    console.log(`Updating invoice number for Stripe invoice ID: ${stripeInvoiceId}`);
    console.log(`New invoice number: ${stripeInvoiceNumber}`);
    
    // Update the invoice number in Supabase
    const { data: updatedInvoice, error: updateError } = await supabase
      .from('invoices')
      .update({
        invoice_number: stripeInvoiceNumber
      })
      .eq('stripe_invoice_id', stripeInvoiceId)
      .select()
      .single();
    
    if (updateError) {
      console.error('Error updating invoice number in Supabase:', updateError);
      return;
    }
    
    console.log('Successfully updated invoice number in Supabase:');
    console.log(`- ID: ${updatedInvoice.id}`);
    console.log(`- Invoice Number: ${updatedInvoice.invoice_number}`);
    console.log(`- Stripe Invoice ID: ${updatedInvoice.stripe_invoice_id}`);
    console.log(`- Status: ${updatedInvoice.status}`);
    console.log(`- Created At: ${updatedInvoice.created_at}`);
  } catch (error) {
    console.error('Error updating invoice number:', error);
  }
}

updateInvoiceNumber();
