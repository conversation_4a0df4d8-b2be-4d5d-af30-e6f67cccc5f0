import React from 'react';
import {
  Sheet,
  SheetContent,
  SheetD<PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>er
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Check, SlidersHorizontal, AlertCircle } from 'lucide-react';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/contexts/AuthContext';

// Define base task status options
const BASE_STATUS_OPTIONS = [
  { value: 'all', label: 'All Tasks' },
  { value: 'open', label: 'Open' },
  { value: 'interest', label: 'Interest' },
  { value: 'questions', label: 'Questions' },
  { value: 'offer', label: 'Offers Received' },
  { value: 'assigned', label: 'Assigned' },
  { value: 'in_progress', label: 'In Progress' },
  { value: 'completed', label: 'Completed' },
  { value: 'confirmed', label: 'Confirmed' },
  { value: 'pending_payment', label: 'Payment Required' },
];

// Admin-specific status options
const ADMIN_STATUS_OPTIONS = [
  { value: 'all', label: 'All Tasks' },
  { value: 'admin_review', label: 'Awaiting Admin Review', icon: AlertCircle, description: 'Tasks created by teachers that need admin review' },
  { value: 'open', label: 'Open' },
  { value: 'interest', label: 'Interest' },
  { value: 'questions', label: 'Questions' },
  { value: 'offer', label: 'Offers Received' },
  { value: 'assigned', label: 'Assigned' },
  { value: 'in_progress', label: 'In Progress' },
  { value: 'completed', label: 'Completed' },
  { value: 'confirmed', label: 'Confirmed' },
  { value: 'pending_payment', label: 'Payment Required' },
];

interface PWATaskStatusFilterProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  statusFilter: string;
  setStatusFilter: (status: string) => void;
  isAdminView?: boolean;
}

const PWATaskStatusFilter: React.FC<PWATaskStatusFilterProps> = ({
  open,
  onOpenChange,
  statusFilter,
  setStatusFilter,
  isAdminView = false,
}) => {
  const { isAdmin } = useAuth();

  // Local state for the filter form
  const [localStatus, setLocalStatus] = React.useState(statusFilter);

  // Determine which status options to show
  const statusOptions = (isAdmin && isAdminView) ? ADMIN_STATUS_OPTIONS : BASE_STATUS_OPTIONS;

  // Apply filter
  const handleApply = () => {
    setStatusFilter(localStatus);
    onOpenChange(false);
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="w-full sm:max-w-md p-0 flex flex-col h-full">
        {/* Fixed Header */}
        <div className="p-6 border-b">
          <SheetHeader>
            <SheetTitle className="flex items-center">
              <SlidersHorizontal className="h-5 w-5 mr-2" />
              Filter by Status
            </SheetTitle>
            <SheetDescription>
              Select a task status to filter the list
            </SheetDescription>
          </SheetHeader>
        </div>

        {/* Scrollable Content Area */}
        <div className="flex-1 overflow-y-auto p-6 pb-24">
          <RadioGroup
            value={localStatus}
            onValueChange={setLocalStatus}
            className="space-y-3"
          >
            {statusOptions.map(option => (
              <div key={option.value} className="flex items-start space-x-2 border p-3 rounded-md">
                <RadioGroupItem value={option.value} id={option.value} className="mt-1" />
                <div className="flex-1">
                  <Label htmlFor={option.value} className="flex items-center cursor-pointer">
                    {option.icon && <option.icon className="h-4 w-4 mr-2 text-orange-500" />}
                    {option.label}
                  </Label>
                  {option.description && (
                    <p className="text-xs text-gray-500 mt-1 ml-6">
                      {option.description}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </RadioGroup>
        </div>

        {/* Fixed Footer */}
        <div className="p-6 border-t bg-background absolute bottom-0 left-0 right-0">
          <Button
            onClick={handleApply}
            className="w-full"
          >
            <Check className="h-4 w-4 mr-2" />
            Apply Filter
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default PWATaskStatusFilter;
