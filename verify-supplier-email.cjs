// Script to verify the supplier's email
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// The supplier user ID
const supplierId = '18625693-2496-45a4-a1d8-675a9bf2683b';

async function verifySupplierEmail() {
  try {
    console.log(`Verifying supplier email for user with ID: ${supplierId}`);
    
    // Get the user from the auth.users table
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(supplierId);
    
    if (authError) {
      console.error('Error fetching user from auth system:', authError);
      return;
    }
    
    if (!authUser || !authUser.user) {
      console.log(`No user found in auth system with ID: ${supplierId}`);
      return;
    }
    
    console.log('User found in auth system:');
    console.log(`- ID: ${authUser.user.id}`);
    console.log(`- Email: ${authUser.user.email}`);
    
    // Get the user from the profiles table
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', supplierId)
      .single();
    
    if (profileError) {
      console.error('Error fetching user from profiles table:', profileError);
      return;
    }
    
    if (!profile) {
      console.log(`No user found in profiles table with ID: ${supplierId}`);
      return;
    }
    
    console.log('\nUser found in profiles table:');
    console.log(`- ID: ${profile.id}`);
    console.log(`- Email: ${profile.email}`);
    console.log(`- Organization ID: ${profile.organization_id}`);
    
    // Check if the emails match
    const authEmail = authUser.user.email;
    const profileEmail = Array.isArray(profile.email) ? profile.email[0] : profile.email;
    
    console.log('\nEmail comparison:');
    console.log(`- Auth Email: ${authEmail}`);
    console.log(`- Profile Email: ${profileEmail}`);
    
    if (authEmail === profileEmail) {
      console.log('✅ Emails match!');
    } else {
      console.log('❌ Emails do not match!');
    }
  } catch (error) {
    console.error('Error verifying supplier email:', error);
  }
}

verifySupplierEmail();
