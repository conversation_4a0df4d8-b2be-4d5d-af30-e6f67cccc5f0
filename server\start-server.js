// Simple script to start the server with environment variables
import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Set environment variables
process.env.STRIPE_SECRET_KEY = 'sk_test_51REKxHAwo0W7IrjoN1jRkmmLyiWGarpzAKItKLmjk9JTRdZPjbZfjZOR7eDcGRStlulfqBNnn7Zf4BRff7JB7f7O003GIQgn5q';
process.env.STRIPE_PUBLIC_KEY = 'pk_test_51REKxHAwo0W7IrjowNl7nykyo6V6RwzMuo0aTJk2gHueU3nTC5WiJaTmGZ648ZndgM1WoR675qFU90f774bPIqCL00xGP85S6u';
process.env.SUPABASE_URL = 'https://qcnotlojmyvpqbbgoxbc.supabase.co';
process.env.SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFjbm90bG9qbXl2cHFiYmdveGJjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDc1ODg4NCwiZXhwIjoyMDYwMzM0ODg0fQ.-wliEddUk77OA8-AsON_nw2okLtRSqW5OfubOLyON5A';
process.env.PORT = '3001';

// Import and run the server
import('./index.js').catch(err => {
  console.error('Error starting server:', err);
  process.exit(1);
});

console.log('Server started with environment variables set programmatically');
