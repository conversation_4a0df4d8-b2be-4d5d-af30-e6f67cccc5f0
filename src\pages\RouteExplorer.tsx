import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ExternalLink } from 'lucide-react';

interface RouteInfo {
  path: string;
  name: string;
  description: string;
  category: 'main' | 'admin' | 'debug' | 'auth' | 'other';
}

const RouteExplorer = () => {
  const { user, login } = useAuth();
  const navigate = useNavigate();
  const [adminEmail, setAdminEmail] = useState('');
  const [adminPassword, setAdminPassword] = useState('');
  const [isAdmin, setIsAdmin] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Check if user has admin role using the profiles table as the source of truth
  const { userRole } = useAuth();
  useEffect(() => {
    if (userRole === 'admin') {
      setIsAdmin(true);
    }
  }, [userRole]);

  // Admin login handler
  const handleAdminLogin = async () => {
    setIsLoading(true);
    setError('');

    try {
      // Check if email and password are valid
      if (adminEmail === '<EMAIL>' && adminPassword === 'classtasker-admin') {
        setIsAdmin(true);
        setError('');
      } else {
        // Check specific errors
        if (adminEmail !== '<EMAIL>') {
          setError('Invalid admin email');
        } else {
          setError('Invalid admin password');
        }
      }
    } catch (err) {
      console.error('Login error:', err);
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Define all routes with descriptions
  const routes: RouteInfo[] = [
    // Main routes
    { path: '/', name: 'Home', description: 'Landing page', category: 'main' },
    { path: '/dashboard', name: 'Dashboard', description: 'User dashboard with tasks, offers, and messages', category: 'main' },
    { path: '/tasks', name: 'Tasks', description: 'Browse all available tasks', category: 'main' },
    { path: '/post-task', name: 'Post Task', description: 'Create a new task', category: 'main' },
    { path: '/messages', name: 'Messages', description: 'View and send messages', category: 'main' },
    { path: '/payments', name: 'Payments', description: 'Manage payments', category: 'main' },
    { path: '/how-it-works', name: 'How It Works', description: 'Information about how the platform works', category: 'main' },
    { path: '/help', name: 'Help', description: 'Help and support', category: 'main' },
    { path: '/contact', name: 'Contact', description: 'Contact information', category: 'main' },

    // Task Views
    { path: '/tasks/1743fbd0-00a4-4b0e-964b-8a2f0bd20ee0', name: 'Task View', description: 'View a task using the FixedTask component (standard view)', category: 'main' },
    { path: '/tasks/1743fbd0-00a4-4b0e-964b-8a2f0bd20ee0?messages=true', name: 'Task View (With Messages)', description: 'View a task with messages panel open', category: 'main' },

    // Different Task Types
    { path: '/tasks/1743fbd0-00a4-4b0e-964b-8a2f0bd20ee0', name: 'Open Task', description: 'A task that is open for offers', category: 'main' },
    { path: '/tasks/c8b7f3a1-2d45-4e8a-9b6c-3f5d7e912a4d', name: 'Assigned Task', description: 'A task that has been assigned to a supplier', category: 'main' },
    { path: '/tasks/e5d9a2b7-8c31-4f6e-b0a5-1d2e3f4a5b6c', name: 'Completed Task', description: 'A task that has been completed', category: 'main' },

    // Message Views
    { path: '/messages', name: 'Messages', description: 'View all messages', category: 'main' },
    { path: '/debug-messages', name: 'Debug Messages', description: 'Debug view for messages', category: 'debug' },

    // Profile Pages
    { path: '/profile/dfaf494b-a559-4191-872b-5b0ec8b9d613', name: 'Example Profile', description: 'View a user profile', category: 'main' },

    // Admin routes
    { path: '/organization/setup', name: 'Organization Setup', description: 'Set up a new organization', category: 'admin' },
    { path: '/organization/users', name: 'Organization Users', description: 'Manage organization users', category: 'admin' },
    { path: '/admin/users', name: 'Admin Users', description: 'Admin user management', category: 'admin' },
    { path: '/admin/tasks', name: 'Admin Tasks', description: 'Admin task management', category: 'admin' },
    { path: '/set-admin-role', name: 'Set Admin Role', description: 'Set admin role for a user', category: 'admin' },
    { path: '/email-config', name: 'Email Config', description: 'Configure email settings', category: 'admin' },
    { path: '/test-email', name: 'Test Email', description: 'Test email functionality', category: 'admin' },

    // Auth routes
    { path: '/login', name: 'Login', description: 'User login', category: 'auth' },
    { path: '/register', name: 'Register', description: 'User registration', category: 'auth' },
    { path: '/invitation/accept', name: 'Accept Invitation', description: 'Accept an invitation to join an organization', category: 'auth' },

    // Debug routes
    { path: '/debug', name: 'Public Debug', description: 'Public debug page', category: 'debug' },
    { path: '/system-debug', name: 'System Debug', description: 'System debug page', category: 'debug' },
    { path: '/debug-chat', name: 'Debug Chat', description: 'Debug chat functionality', category: 'debug' },
    { path: '/public-debug-chat', name: 'Public Debug Chat', description: 'Public debug chat functionality', category: 'debug' },
    { path: '/create-test-users', name: 'Create Test Users', description: 'Create test users', category: 'debug' },
    { path: '/debug-messages', name: 'Debug Messages', description: 'Debug message functionality', category: 'debug' },
  ];

  // Group routes by category
  const mainRoutes = routes.filter(route => route.category === 'main');
  const adminRoutes = routes.filter(route => route.category === 'admin');
  const authRoutes = routes.filter(route => route.category === 'auth');
  const debugRoutes = routes.filter(route => route.category === 'debug');
  const otherRoutes = routes.filter(route => route.category === 'other');

  // Render a route card
  const renderRouteCard = (route: RouteInfo) => (
    <Card key={route.path} className="mb-2 hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-medium text-lg flex items-center">
              <Link to={route.path} className="text-classtasker-blue hover:underline flex items-center">
                {route.name}
                <ExternalLink className="ml-1 h-4 w-4" />
              </Link>
            </h3>
            <p className="text-gray-600 text-sm">{route.description}</p>
            <code className="text-xs text-gray-500 mt-1 block">{route.path}</code>
          </div>
          <Badge
            variant="outline"
            className={
              route.category === 'main' ? 'bg-green-100 text-green-800 border-green-200' :
              route.category === 'admin' ? 'bg-purple-100 text-purple-800 border-purple-200' :
              route.category === 'debug' ? 'bg-orange-100 text-orange-800 border-orange-200' :
              route.category === 'auth' ? 'bg-blue-100 text-blue-800 border-blue-200' :
              'bg-gray-100 text-gray-800 border-gray-200'
            }
          >
            {route.category}
          </Badge>
        </div>
      </CardContent>
    </Card>
  );

  // If not admin, show login form
  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <h1 className="text-2xl font-bold mb-6 text-center">Route Explorer Admin Access</h1>
            <p className="text-gray-600 mb-6 text-center">
              This page requires admin access. Please enter your admin credentials to continue.
            </p>

            <div className="space-y-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Admin Email
                </label>
                <input
                  type="email"
                  id="email"
                  value={adminEmail}
                  onChange={(e) => setAdminEmail(e.target.value)}
                  className="w-full p-2 border rounded-md"
                  placeholder="Enter admin email"
                  onKeyDown={(e) => e.key === 'Enter' && handleAdminLogin()}
                />
                <p className="text-xs text-gray-500 mt-1">Hint: The email is "<EMAIL>"</p>
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Admin Password
                </label>
                <input
                  type="password"
                  id="password"
                  value={adminPassword}
                  onChange={(e) => setAdminPassword(e.target.value)}
                  className="w-full p-2 border rounded-md"
                  placeholder="Enter admin password"
                  onKeyDown={(e) => e.key === 'Enter' && handleAdminLogin()}
                />
                <p className="text-xs text-gray-500 mt-1">Hint: The password is "classtasker-admin"</p>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
                  {error}
                </div>
              )}

              <Button
                onClick={handleAdminLogin}
                className="w-full bg-classtasker-blue hover:bg-blue-600"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Logging in...
                  </>
                ) : (
                  'Access Route Explorer'
                )}
              </Button>

              <div className="text-center">
                <Link to="/" className="text-sm text-gray-500 hover:text-gray-700">
                  Return to Home
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Admin view with all routes
  return (
    <div className="min-h-screen bg-gray-50 py-6 px-4">
      <div className="container mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Route Explorer</h1>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => navigate('/')}>
              Return to Home
            </Button>
            <Button variant="outline" onClick={() => setIsAdmin(false)}>
              Logout Admin
            </Button>
          </div>
        </div>

        <p className="text-gray-600 mb-8">
          This page shows all available routes in the application. Click on any route to navigate to that page.
          Use this to explore the application and decide which pages are needed.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <span className="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
              Main Routes
            </h2>
            <div className="space-y-2">
              {mainRoutes.map(renderRouteCard)}
            </div>

            <h2 className="text-xl font-semibold mb-4 mt-8 flex items-center">
              <span className="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
              Authentication Routes
            </h2>
            <div className="space-y-2">
              {authRoutes.map(renderRouteCard)}
            </div>
          </div>

          <div>
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <span className="w-3 h-3 rounded-full bg-purple-500 mr-2"></span>
              Admin Routes
            </h2>
            <div className="space-y-2">
              {adminRoutes.map(renderRouteCard)}
            </div>

            <h2 className="text-xl font-semibold mb-4 mt-8 flex items-center">
              <span className="w-3 h-3 rounded-full bg-orange-500 mr-2"></span>
              Debug Routes
            </h2>
            <div className="space-y-2">
              {debugRoutes.map(renderRouteCard)}
            </div>

            {otherRoutes.length > 0 && (
              <>
                <h2 className="text-xl font-semibold mb-4 mt-8 flex items-center">
                  <span className="w-3 h-3 rounded-full bg-gray-500 mr-2"></span>
                  Other Routes
                </h2>
                <div className="space-y-2">
                  {otherRoutes.map(renderRouteCard)}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RouteExplorer;
