import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import Stripe from 'https://esm.sh/stripe@12.0.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the API key from environment variables - ONLY use the environment variable
    const apiKey = Deno.env.get('STRIPE_SECRET_KEY');
    
    // Validate the API key
    if (!apiKey) {
      console.error('No Stripe API key found in environment variables');
      return new Response(
        JSON.stringify({ error: 'Server configuration error: No API key available' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
    // Log key information for debugging (never log the full key)
    const keyPrefix = apiKey.substring(0, 7);
    const keyLength = apiKey.length;
    console.log(`Using Stripe API key: ${keyPrefix}... (length: ${keyLength})`);
    console.log(`Key type: ${apiKey.startsWith('sk_live_') ? 'LIVE' : apiKey.startsWith('sk_test_') ? 'TEST' : 'UNKNOWN'}`);
    
    // Initialize Stripe with the environment variable
    const stripe = new Stripe(apiKey, {
      apiVersion: '2023-10-16',
    })

    // Get the invoice ID from the request
    const { invoiceId } = await req.json()

    if (!invoiceId) {
      return new Response(
        JSON.stringify({ error: 'Invoice ID is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log(`Processing invoice ${invoiceId}`)

    try {
      // First, retrieve the invoice to check its details
      console.log(`Retrieving invoice ${invoiceId}...`)
      const invoice = await stripe.invoices.retrieve(invoiceId)
      
      console.log('Invoice details:', {
        id: invoice.id,
        customer_email: invoice.customer_email,
        status: invoice.status,
      })
      
      // Send the invoice email
      console.log(`Sending email for invoice ${invoiceId}...`)
      const sentInvoice = await stripe.invoices.sendInvoice(invoiceId)
      
      console.log('Invoice email sent successfully')

      // Determine if we're in live mode
      const isLiveMode = apiKey.startsWith('sk_live_');
      
      // Prepare response message
      const responseMessage = isLiveMode
        ? "Invoice email sent successfully. Since we're in LIVE mode, an actual email has been sent to the customer's email address."
        : "Invoice email sent successfully. Note: In test mode, Stripe doesn't actually send emails to real addresses. Check the Stripe Dashboard Events section to see the attempted email.";
      
      return new Response(
        JSON.stringify({ 
          sent: true, 
          invoice: {
            id: sentInvoice.id,
            status: sentInvoice.status,
            customer_email: sentInvoice.customer_email,
          },
          message: responseMessage,
          mode: isLiveMode ? 'live' : 'test'
        }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    } catch (stripeError) {
      console.error('Stripe error sending invoice email:', stripeError)
      
      return new Response(
        JSON.stringify({ 
          error: 'Stripe error sending invoice email', 
          details: stripeError.message 
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
  } catch (error) {
    console.error('Error sending invoice email:', error)
    
    return new Response(
      JSON.stringify({ error: 'Failed to send invoice email' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
