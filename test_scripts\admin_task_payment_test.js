/**
 * Comprehensive Test Script for Admin Task Payment Flow
 *
 * This script tests the entire flow from task creation to payment for the admin user.
 * It will:
 * 1. Create a new task as the admin user
 * 2. Create a supplier offer for the task
 * 3. Accept the offer as the admin user
 * 4. Mark the task as complete (pending_payment)
 * 5. Verify the payment button is visible
 *
 * Run this script with Node.js:
 * node test_scripts/admin_task_payment_test.js
 */

import { createClient } from '@supabase/supabase-js';

// SECURITY: Supabase credentials - NEVER hardcode service role keys
import dotenv from 'dotenv';
dotenv.config();

const SUPABASE_URL = 'https://qcnotlojmyvpqbbgoxbc.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Validate that the service role key is present
if (!SUPABASE_SERVICE_KEY) {
  console.error('SECURITY ERROR: SUPABASE_SERVICE_ROLE_KEY not found in environment variables');
  console.error('This script requires the service role key to function');
  process.exit(1);
}

// User IDs
const ADMIN_ID = '4288cd97-e3ed-4e1d-8d22-abdc0d3f28bd';
const SUPPLIER_ID = '6cbb91a0-b55c-464f-80a0-659030aa0ffc';

// Create Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Helper function to generate a unique task title
const generateTaskTitle = () => `Test Task ${Math.floor(Math.random() * 10000)}`;

// Main test function
async function runTest() {
  console.log('Starting Admin Task Payment Test');
  console.log('-------------------------------');

  try {
    // Step 1: Create a new task as the admin user
    console.log('\nStep 1: Creating a new task as the admin user');
    const taskTitle = generateTaskTitle();
    const { data: task, error: taskError } = await supabase
      .from('tasks')
      .insert([
        {
          title: taskTitle,
          description: 'This is a test task for admin payment flow',
          location: 'Test Location',
          category: 'Other',
          budget: 1000,
          due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
          status: 'open',
          visibility: 'public',
          user_id: ADMIN_ID,
          payment_status: 'unpaid'
        }
      ])
      .select()
      .single();

    if (taskError) {
      throw new Error(`Error creating task: ${taskError.message}`);
    }

    console.log(`Task created successfully: ${task.id} - ${task.title}`);
    console.log(`Task status: ${task.status}, visibility: ${task.visibility}`);

    // Step 2: Create a supplier offer for the task
    console.log('\nStep 2: Creating a supplier offer for the task');
    const { data: offer, error: offerError } = await supabase
      .from('offers')
      .insert([
        {
          task_id: task.id,
          user_id: SUPPLIER_ID,
          amount: 1500,
          message: 'This is a test offer for the admin payment flow',
          status: 'awaiting'
        }
      ])
      .select()
      .single();

    if (offerError) {
      throw new Error(`Error creating offer: ${offerError.message}`);
    }

    console.log(`Offer created successfully: ${offer.id}`);
    console.log(`Offer status: ${offer.status}, amount: ${offer.amount}`);

    // Step 3: Accept the offer as the admin user
    console.log('\nStep 3: Accepting the offer as the admin user');

    // First update the offer status to accepted
    const { data: updatedOffer, error: updateOfferError } = await supabase
      .from('offers')
      .update({ status: 'accepted' })
      .eq('id', offer.id)
      .select()
      .single();

    if (updateOfferError) {
      throw new Error(`Error updating offer status: ${updateOfferError.message}`);
    }

    console.log(`Offer status updated to: ${updatedOffer.status}`);

    // Then update the task status to assigned and set the assigned_to field
    const { data: assignedTask, error: assignTaskError } = await supabase
      .from('tasks')
      .update({
        status: 'assigned',
        assigned_to: SUPPLIER_ID,
        visibility: 'public'
      })
      .eq('id', task.id)
      .select()
      .single();

    if (assignTaskError) {
      throw new Error(`Error updating task status: ${assignTaskError.message}`);
    }

    console.log(`Task status updated to: ${assignedTask.status}`);
    console.log(`Task assigned to: ${assignedTask.assigned_to}`);

    // Step 4: Mark the task as complete (pending_payment)
    console.log('\nStep 4: Marking the task as complete (pending_payment)');
    const { data: completedTask, error: completeTaskError } = await supabase
      .from('tasks')
      .update({
        status: 'pending_payment',
        payment_status: 'pending'
      })
      .eq('id', task.id)
      .select()
      .single();

    if (completeTaskError) {
      throw new Error(`Error marking task as complete: ${completeTaskError.message}`);
    }

    console.log(`Task status updated to: ${completedTask.status}`);
    console.log(`Task payment status: ${completedTask.payment_status}`);

    // Step 5: Create a payment record for the task
    console.log('\nStep 5: Creating a payment record for the task');
    const { data: payment, error: paymentError } = await supabase
      .from('payments')
      .insert([
        {
          task_id: task.id,
          offer_id: offer.id,
          payer_id: ADMIN_ID,
          payee_id: SUPPLIER_ID,
          amount: offer.amount,
          platform_fee: offer.amount * 0.1, // 10% platform fee
          supplier_amount: offer.amount * 0.9, // 90% to supplier
          status: 'pending',
          currency: 'gbp'
        }
      ])
      .select()
      .single();

    if (paymentError) {
      throw new Error(`Error creating payment record: ${paymentError.message}`);
    }

    console.log(`Payment record created successfully: ${payment.id}`);
    console.log(`Payment status: ${payment.status}, amount: ${payment.amount}`);

    // Step 6: Verify the task and payment status
    console.log('\nStep 6: Verifying the task and payment status');
    const { data: finalTask, error: finalTaskError } = await supabase
      .from('tasks')
      .select('*')
      .eq('id', task.id)
      .single();

    if (finalTaskError) {
      throw new Error(`Error fetching final task: ${finalTaskError.message}`);
    }

    console.log('Final task status:');
    console.log(`- ID: ${finalTask.id}`);
    console.log(`- Title: ${finalTask.title}`);
    console.log(`- Status: ${finalTask.status}`);
    console.log(`- Payment Status: ${finalTask.payment_status}`);
    console.log(`- Visibility: ${finalTask.visibility}`);
    console.log(`- Assigned To: ${finalTask.assigned_to}`);

    const { data: finalOffer, error: finalOfferError } = await supabase
      .from('offers')
      .select('*')
      .eq('id', offer.id)
      .single();

    if (finalOfferError) {
      throw new Error(`Error fetching final offer: ${finalOfferError.message}`);
    }

    console.log('\nFinal offer status:');
    console.log(`- ID: ${finalOffer.id}`);
    console.log(`- Status: ${finalOffer.status}`);
    console.log(`- Amount: ${finalOffer.amount}`);

    const { data: finalPayment, error: finalPaymentError } = await supabase
      .from('payments')
      .select('*')
      .eq('id', payment.id)
      .single();

    if (finalPaymentError) {
      throw new Error(`Error fetching final payment: ${finalPaymentError.message}`);
    }

    console.log('\nFinal payment status:');
    console.log(`- ID: ${finalPayment.id}`);
    console.log(`- Status: ${finalPayment.status}`);
    console.log(`- Amount: ${finalPayment.amount}`);

    // Step 7: Provide instructions for manual testing
    console.log('\nStep 7: Manual Testing Instructions');
    console.log('-------------------------------');
    console.log('1. Log in as the admin user (<EMAIL>)');
    console.log(`2. Navigate to the task page: http://localhost:8083/task/${task.id}`);
    console.log('3. Verify that the "Pay Now" button is visible');
    console.log('4. Click the "Pay Now" button to test the payment flow');
    console.log('-------------------------------');
    console.log(`Task URL: http://localhost:8083/task/${task.id}`);
    console.log('-------------------------------');

    return {
      success: true,
      taskId: task.id,
      taskUrl: `http://localhost:8083/task/${task.id}`
    };
  } catch (error) {
    console.error('Test failed:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// Run the test
runTest().then(result => {
  if (result.success) {
    console.log('\nTest completed successfully!');
    console.log(`Please visit: ${result.taskUrl} to verify the payment button is visible`);
  } else {
    console.log('\nTest failed!');
  }
});
