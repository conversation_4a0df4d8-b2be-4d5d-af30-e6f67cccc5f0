import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { emailService } from '@/services/emailService';
import { EmailConfig as EmailConfigType, EmailProvider, SchoolEmailConfig } from '@/types/email';
import { Loader2, Mail, Send, CheckCircle, ExternalLink, School, Plus, X, Check } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";

// Mock school data - in a real implementation, this would come from the database
const mockSchools = [
  {
    id: '1',
    name: 'Springfield Elementary',
    address: '123 School Lane',
    city: 'Springfield',
    state: 'IL',
    user_count: 24,
    created_at: '2023-01-15T00:00:00Z'
  },
  {
    id: '2',
    name: 'Riverdale High',
    address: '456 Education Blvd',
    city: 'Riverdale',
    state: 'NY',
    user_count: 32,
    created_at: '2023-02-20T00:00:00Z'
  },
  {
    id: '3',
    name: 'Tech Academy',
    address: '789 Innovation Way',
    city: 'Silicon Valley',
    state: 'CA',
    user_count: 18,
    created_at: '2023-03-10T00:00:00Z'
  }
];

const EmailConfigForm = () => {
  const { user, organizationId } = useAuth();
  const navigate = useNavigate();

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [activeTab, setActiveTab] = useState<EmailProvider>('none');
  const [testEmail, setTestEmail] = useState('');
  const [schools, setSchools] = useState(mockSchools);
  const [selectedSchoolId, setSelectedSchoolId] = useState<string>('');
  const [configMode, setConfigMode] = useState<'global' | 'school'>('global');
  const [selectedSchools, setSelectedSchools] = useState<string[]>([]);

  const [config, setConfig] = useState<EmailConfigType>({
    provider: 'none',
    fromEmail: '',
    fromName: '',
    schoolConfigs: []
  });

  useEffect(() => {
    if (user?.email) {
      setTestEmail(user.email);
    }
    loadConfig();

    // In a real implementation, we would fetch schools from the database
    // For now, we'll use the mock data
    // fetchSchools();
  }, [user]);

  const loadConfig = async () => {
    setIsLoading(true);
    try {
      const config = await emailService.getConfig();
      setConfig(config);
      setActiveTab(config.provider);

      // Initialize school configs if they don't exist
      if (!config.schoolConfigs) {
        config.schoolConfigs = [];
      }
    } catch (error) {
      console.error('Error loading email config:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to load email configuration.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // In a real implementation, this would fetch schools from the database
  const fetchSchools = async () => {
    try {
      // For now, we'll use the mock data
      setSchools(mockSchools);
    } catch (error) {
      console.error('Error fetching schools:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to load schools.',
      });
    }
  };

  const handleProviderChange = (value: EmailProvider) => {
    setActiveTab(value);
    setConfig(prev => ({ ...prev, provider: value }));
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;

    if (type === 'number') {
      setConfig(prev => ({ ...prev, [name]: parseInt(value) }));
    } else {
      setConfig(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setConfig(prev => ({ ...prev, [name]: checked }));
  };

  const handleSaveConfig = async () => {
    setIsSaving(true);
    try {
      // If we're in school mode and have selected schools, update their configs
      if (configMode === 'school' && selectedSchools.length > 0) {
        // Create a new school config based on the current settings
        const schoolConfig: SchoolEmailConfig = {
          schoolId: selectedSchoolId,
          schoolName: schools.find(s => s.id === selectedSchoolId)?.name || 'Unknown School',
          provider: config.provider,
          apiKey: config.apiKey,
          fromEmail: config.fromEmail,
          fromName: config.fromName,

          smtpHost: config.smtpHost,
          smtpPort: config.smtpPort,
          smtpUsername: config.smtpUsername,
          smtpPassword: config.smtpPassword,
          smtpSecure: config.smtpSecure
        };

        // Update or add school configs for all selected schools
        const updatedSchoolConfigs = [...(config.schoolConfigs || [])];

        selectedSchools.forEach(schoolId => {
          const schoolName = schools.find(s => s.id === schoolId)?.name || 'Unknown School';
          const existingIndex = updatedSchoolConfigs.findIndex(sc => sc.schoolId === schoolId);

          if (existingIndex >= 0) {
            // Update existing config
            updatedSchoolConfigs[existingIndex] = {
              ...schoolConfig,
              schoolId,
              schoolName
            };
          } else {
            // Add new config
            updatedSchoolConfigs.push({
              ...schoolConfig,
              schoolId,
              schoolName
            });
          }
        });

        // Update the config with the new school configs
        const updatedConfig = {
          ...config,
          schoolConfigs: updatedSchoolConfigs
        };

        await emailService.saveConfig(updatedConfig);
        setConfig(updatedConfig);
      } else {
        // Save global config
        await emailService.saveConfig(config);
      }

      toast({
        title: 'Configuration Saved',
        description: 'Email configuration has been saved successfully.',
      });
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Failed to save email configuration.',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleTestConfig = async () => {
    if (!testEmail) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Please enter a test email address.',
      });
      return;
    }

    // Validate configuration before testing
    if (config.provider === 'smtp') {
      if (!config.smtpHost) {
        toast({
          variant: 'destructive',
          title: 'Missing Configuration',
          description: 'Please enter an SMTP host.',
        });
        return;
      }

      if (!config.smtpPort) {
        toast({
          variant: 'destructive',
          title: 'Missing Configuration',
          description: 'Please enter an SMTP port.',
        });
        return;
      }

      if (!config.smtpUsername) {
        toast({
          variant: 'destructive',
          title: 'Missing Configuration',
          description: 'Please enter an SMTP username.',
        });
        return;
      }

      if (!config.smtpPassword) {
        toast({
          variant: 'destructive',
          title: 'Missing Configuration',
          description: 'Please enter an SMTP password.',
        });
        return;
      }

      if (!config.fromEmail) {
        toast({
          variant: 'destructive',
          title: 'Missing Configuration',
          description: 'Please enter a from email address.',
        });
        return;
      }
    }

    setIsTesting(true);
    try {
      // Just test the configuration - this will send a test email
      const success = await emailService.testConfig(config, testEmail);

      if (success) {
        toast({
          title: 'Test Successful',
          description: `A test email has been sent to ${testEmail}.`,
        });
      } else {
        console.error('Email testing failed but no error was thrown');
        toast({
          variant: 'destructive',
          title: 'Test Failed',
          description: 'Failed to send test email. Please check the console for more details.',
        });
      }
    } catch (error: any) {
      console.error('Error testing email configuration:', error);
      let errorMessage = 'Failed to send test email.';

      if (error.message) {
        errorMessage += ` Error: ${error.message}`;
      }

      // Check for specific error types
      if (error.message && error.message.includes('ECONNREFUSED')) {
        errorMessage = 'Connection refused. Please check your SMTP host and port.';
      } else if (error.message && error.message.includes('authentication')) {
        errorMessage = 'Authentication failed. Please check your username and password.';
      } else if (error.message && error.message.includes('certificate')) {
        errorMessage = 'SSL/TLS certificate error. Try toggling the SSL/TLS option.';
      }

      toast({
        variant: 'destructive',
        title: 'Test Failed',
        description: errorMessage,
      });
    } finally {
      setIsTesting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-6">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Configuration Mode Selection */}
      <div>
        <h3 className="text-lg font-medium mb-2">Configuration Mode</h3>
        <RadioGroup
          value={configMode}
          onValueChange={(value) => setConfigMode(value as 'global' | 'school')}
          className="flex space-x-4"
        >
          <div
            className={`flex items-center space-x-2 border rounded-md p-4 cursor-pointer ${configMode === 'global' ? 'border-classtasker-blue bg-blue-50' : ''}`}
          >
            <RadioGroupItem value="global" id="global" />
            <Label htmlFor="global" className="cursor-pointer">Global Configuration</Label>
          </div>
          <div
            className={`flex items-center space-x-2 border rounded-md p-4 cursor-pointer ${configMode === 'school' ? 'border-classtasker-blue bg-blue-50' : ''}`}
          >
            <RadioGroupItem value="school" id="school" />
            <Label htmlFor="school" className="cursor-pointer">School-Specific Configuration</Label>
          </div>
        </RadioGroup>
      </div>

      {/* School Selection (only visible in school mode) */}
      {configMode === 'school' && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium mb-2">Select Schools</h3>
          <p className="text-sm text-gray-500 mb-4">
            Configure email settings for specific schools. You can select multiple schools to apply the same configuration.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="schoolSelect">Select School</Label>
              <Select
                value={selectedSchoolId}
                onValueChange={(value) => {
                  setSelectedSchoolId(value);

                  // If the school is already selected, don't add it again
                  if (!selectedSchools.includes(value)) {
                    setSelectedSchools([...selectedSchools, value]);
                  }

                  // If this school has a specific config, load it
                  const schoolConfig = config.schoolConfigs?.find(sc => sc.schoolId === value);
                  if (schoolConfig) {
                    // Update the form with this school's config
                    setActiveTab(schoolConfig.provider);
                    // We're not replacing the entire config, just updating the form fields
                    // that are specific to the email provider
                    setConfig(prevConfig => ({
                      ...prevConfig,
                      provider: schoolConfig.provider,
                      apiKey: schoolConfig.apiKey,
                      fromEmail: schoolConfig.fromEmail,
                      fromName: schoolConfig.fromName,
                      sendgridTemplateId: schoolConfig.sendgridTemplateId,
                      mailgunDomain: schoolConfig.mailgunDomain,
                      smtpHost: schoolConfig.smtpHost,
                      smtpPort: schoolConfig.smtpPort,
                      smtpUsername: schoolConfig.smtpUsername,
                      smtpPassword: schoolConfig.smtpPassword,
                      smtpSecure: schoolConfig.smtpSecure
                    }));
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a school" />
                </SelectTrigger>
                <SelectContent>
                  {schools.map(school => (
                    <SelectItem key={school.id} value={school.id}>
                      {school.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Selected Schools</Label>
              <div className="border rounded-md p-2 min-h-[42px] flex flex-wrap gap-2">
                {selectedSchools.length === 0 ? (
                  <p className="text-sm text-gray-500 p-1">No schools selected</p>
                ) : (
                  selectedSchools.map(schoolId => {
                    const school = schools.find(s => s.id === schoolId);
                    return (
                      <Badge key={schoolId} variant="secondary" className="flex items-center gap-1">
                        <School className="h-3 w-3" />
                        {school?.name || 'Unknown School'}
                        <button
                          onClick={() => setSelectedSchools(selectedSchools.filter(id => id !== schoolId))}
                          className="ml-1 hover:text-red-500"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    );
                  })
                )}
              </div>
            </div>
          </div>

          {/* Show which schools have custom configurations */}
          {config.schoolConfigs && config.schoolConfigs.length > 0 && (
            <div className="mt-4">
              <h4 className="text-sm font-medium mb-2">Schools with Custom Configurations</h4>
              <div className="border rounded-md p-3 flex flex-wrap gap-2">
                {config.schoolConfigs.map(schoolConfig => (
                  <Badge key={schoolConfig.schoolId} variant="outline" className="flex items-center gap-1">
                    <School className="h-3 w-3" />
                    {schoolConfig.schoolName}
                    <span className="text-xs text-gray-500 ml-1">({schoolConfig.provider})</span>
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      <Separator />
      <div>
        <h3 className="text-lg font-medium mb-2">Email Provider</h3>
        <p className="text-sm text-gray-500 mb-4">
          Select an email provider to use for sending emails
        </p>

        <RadioGroup
          value={activeTab}
          onValueChange={(value) => handleProviderChange(value as EmailProvider)}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
        >
          <div className={`flex items-center space-x-2 border rounded-md p-4 cursor-pointer ${activeTab === 'sendgrid' ? 'border-classtasker-blue bg-blue-50' : ''}`}>
            <RadioGroupItem value="sendgrid" id="sendgrid" />
            <Label htmlFor="sendgrid" className="cursor-pointer">SendGrid</Label>
          </div>

          <div className={`flex items-center space-x-2 border rounded-md p-4 cursor-pointer ${activeTab === 'mailgun' ? 'border-classtasker-blue bg-blue-50' : ''}`}>
            <RadioGroupItem value="mailgun" id="mailgun" />
            <Label htmlFor="mailgun" className="cursor-pointer">Mailgun</Label>
          </div>

          <div className={`flex items-center space-x-2 border rounded-md p-4 cursor-pointer ${activeTab === 'smtp' ? 'border-classtasker-blue bg-blue-50' : ''}`}>
            <RadioGroupItem value="smtp" id="smtp" />
            <Label htmlFor="smtp" className="cursor-pointer">SMTP</Label>
          </div>

          <div className={`flex items-center space-x-2 border rounded-md p-4 cursor-pointer ${activeTab === 'none' ? 'border-classtasker-blue bg-blue-50' : ''}`}>
            <RadioGroupItem value="none" id="none" />
            <Label htmlFor="none" className="cursor-pointer">No Email (Debug)</Label>
          </div>
        </RadioGroup>
      </div>

      <Separator />

      <Tabs value={activeTab} onValueChange={(value) => handleProviderChange(value as EmailProvider)}>
        <TabsContent value="sendgrid" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="apiKey">SendGrid API Key</Label>
              <Input
                id="apiKey"
                name="apiKey"
                type="password"
                placeholder="Enter your SendGrid API key"
                value={config.apiKey || ''}
                onChange={handleInputChange}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="sendgridTemplateId">Template ID (Optional)</Label>
              <Input
                id="sendgridTemplateId"
                name="sendgridTemplateId"
                placeholder="Enter template ID for invitation emails"
                value={config.sendgridTemplateId || ''}
                onChange={handleInputChange}
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="mailgun" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="apiKey">Mailgun API Key</Label>
              <Input
                id="apiKey"
                name="apiKey"
                type="password"
                placeholder="Enter your Mailgun API key"
                value={config.apiKey || ''}
                onChange={handleInputChange}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="mailgunDomain">Mailgun Domain</Label>
              <Input
                id="mailgunDomain"
                name="mailgunDomain"
                placeholder="Enter your Mailgun domain"
                value={config.mailgunDomain || ''}
                onChange={handleInputChange}
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="smtp" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="smtpHost">SMTP Host</Label>
              <Input
                id="smtpHost"
                name="smtpHost"
                placeholder="e.g., smtp.gmail.com"
                value={config.smtpHost || ''}
                onChange={handleInputChange}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="smtpPort">SMTP Port</Label>
              <Input
                id="smtpPort"
                name="smtpPort"
                type="number"
                placeholder="e.g., 587"
                value={config.smtpPort || ''}
                onChange={handleInputChange}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="smtpUsername">SMTP Username</Label>
              <Input
                id="smtpUsername"
                name="smtpUsername"
                placeholder="Enter SMTP username"
                value={config.smtpUsername || ''}
                onChange={handleInputChange}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="smtpPassword">SMTP Password</Label>
              <Input
                id="smtpPassword"
                name="smtpPassword"
                type="password"
                placeholder="Enter SMTP password"
                value={config.smtpPassword || ''}
                onChange={handleInputChange}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="smtpSecure"
                checked={config.smtpSecure || false}
                onCheckedChange={(checked) => handleSwitchChange('smtpSecure', checked)}
              />
              <Label htmlFor="smtpSecure">Use SSL/TLS</Label>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="none" className="space-y-4">
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <p className="text-yellow-800">
              No email provider is configured. Emails will be logged to the console but not actually sent.
              This is useful for development and testing.
            </p>
          </div>
        </TabsContent>
      </Tabs>

      <Separator />

      {/* Configuration Summary */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Configuration Summary</h3>
        <Card>
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="font-medium">Global Configuration:</span>
                <Badge variant="outline">{config.provider}</Badge>
              </div>

              <div className="text-sm text-gray-600">
                {config.fromEmail && (
                  <div className="flex justify-between">
                    <span>From Email:</span>
                    <span>{config.fromEmail}</span>
                  </div>
                )}
              </div>

              {config.schoolConfigs && config.schoolConfigs.length > 0 && (
                <div className="mt-4 pt-4 border-t">
                  <div className="font-medium mb-2">School-Specific Configurations:</div>
                  <div className="space-y-2">
                    {config.schoolConfigs.map(schoolConfig => (
                      <div key={schoolConfig.schoolId} className="flex justify-between items-center">
                        <div className="flex items-center">
                          <School className="h-4 w-4 mr-2 text-gray-500" />
                          <span>{schoolConfig.schoolName}</span>
                        </div>
                        <Badge variant="outline">{schoolConfig.provider}</Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Sender Information</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="fromEmail">From Email</Label>
            <Input
              id="fromEmail"
              name="fromEmail"
              placeholder="<EMAIL>"
              value={config.fromEmail || ''}
              onChange={handleInputChange}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="fromName">From Name</Label>
            <Input
              id="fromName"
              name="fromName"
              placeholder="Your Organization Name"
              value={config.fromName || ''}
              onChange={handleInputChange}
            />
          </div>
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Test Configuration</h3>
        <p className="text-sm text-gray-500">
          Send a test email to verify your configuration
        </p>

        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-grow">
            <Input
              placeholder="Enter email address for testing"
              value={testEmail}
              onChange={(e) => setTestEmail(e.target.value)}
            />
          </div>
          <Button
            onClick={handleTestConfig}
            disabled={isTesting || config.provider === 'none'}
            className="whitespace-nowrap"
          >
            {isTesting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Sending...
              </>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                Send Test Email
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="flex justify-end space-x-4 pt-4 border-t">
        {configMode === 'school' && selectedSchools.length > 0 && (
          <div className="mr-auto text-sm text-gray-500 flex items-center">
            <Check className="h-4 w-4 mr-1 text-green-500" />
            Saving configuration for {selectedSchools.length} selected {selectedSchools.length === 1 ? 'school' : 'schools'}
          </div>
        )}
        <Button
          variant="outline"
          onClick={async () => {
            await handleSaveConfig();
            handleTestConfig();
          }}
          disabled={isSaving || isTesting}
        >
          {isSaving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving & Testing...
            </>
          ) : (
            <>
              <CheckCircle className="mr-2 h-4 w-4" />
              Save & Test
            </>
          )}
        </Button>
        <Button onClick={handleSaveConfig} disabled={isSaving}>
          {isSaving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            'Save Configuration'
          )}
        </Button>
      </div>
    </div>
  );
};

export default EmailConfigForm;