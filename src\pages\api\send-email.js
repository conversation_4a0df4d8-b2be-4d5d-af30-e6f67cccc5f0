// API endpoint for sending emails
// This is a serverless function that uses nodemailer to send emails

export default async function handler(req, res) {
  console.log('DEBUG: API route handler called');
  console.log('DEBUG: Request method:', req.method);
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('DEBUG: Accessing request body');
    console.log('DEBUG: Request body:', JSON.stringify(req.body, null, 2));
    const { config, params } = req.body;

    if (!config || !params) {
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    // Validate email configuration
    if (config.provider === 'smtp') {
      if (!config.smtpHost || !config.smtpPort || !config.smtpUsername || !config.smtpPassword) {
        return res.status(400).json({
          error: 'Missing SMTP configuration. Please provide host, port, username, and password.'
        });
      }
    } else {
      return res.status(400).json({
        error: `Unsupported email provider: ${config.provider}`
      });
    }

    // Validate email parameters
    if (!params.to || !params.subject) {
      return res.status(400).json({
        error: 'Missing email parameters. Please provide to and subject.'
      });
    }

    // Log the email sending request
    console.log('Sending email with configuration:', {
      provider: config.provider,
      from: `${config.fromName || 'Classtasker'} <${config.fromEmail}>`,
      to: params.to,
      subject: params.subject,
      body: params.body ? params.body.substring(0, 100) + '...' : '(empty)'
    });

    try {
      // Import nodemailer dynamically
      const nodemailer = await import('nodemailer');

      // Create SMTP transporter
      console.log('Creating SMTP transporter with config:', {
        host: config.smtpHost,
        port: config.smtpPort,
        secure: config.smtpSecure || false,
        auth: {
          user: config.smtpUsername,
          pass: '********' // Masked for security
        }
      });

      const transporter = nodemailer.default.createTransport({
        host: config.smtpHost,
        port: config.smtpPort,
        secure: config.smtpSecure || false,
        auth: {
          user: config.smtpUsername,
          pass: config.smtpPassword,
        },
        debug: true, // Enable debug output
        logger: true // Log to console
      });

      // Verify connection configuration
      console.log('Verifying SMTP connection...');
      await transporter.verify();
      console.log('SMTP connection verified successfully');

      // Send email
      console.log('Sending email...');
      const info = await transporter.sendMail({
        from: `"${config.fromName || 'Classtasker'}" <${config.fromEmail}>`,
        to: params.to,
        subject: params.subject,
        html: params.body,
      });

      console.log('Email sent successfully:', info);
      return res.status(200).json({
        success: true,
        message: `Email sent to ${params.to} using ${config.provider} provider. Message ID: ${info.messageId}`
      });
    } catch (sendError) {
      console.error('Error sending email:', sendError);
      return res.status(500).json({
        success: false,
        error: `Error sending email: ${sendError.message}`,
        details: sendError.stack
      });
    }
  } catch (error) {
    console.error('Unexpected error in email API:', error);
    return res.status(500).json({
      success: false,
      error: `Unexpected error: ${error.message}`,
      details: error.stack
    });
  }
}
