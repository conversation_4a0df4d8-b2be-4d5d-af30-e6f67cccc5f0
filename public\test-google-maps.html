<!DOCTYPE html>
<html>
<head>
  <title>Google Maps API Test</title>
  <style>
    #map {
      height: 400px;
      width: 100%;
    }
    .controls {
      margin: 20px 0;
      padding: 10px;
      background: #f5f5f5;
      border: 1px solid #ddd;
    }
    .log {
      margin-top: 20px;
      padding: 10px;
      background: #f5f5f5;
      border: 1px solid #ddd;
      max-height: 200px;
      overflow-y: auto;
    }
    .error {
      color: red;
    }
    .success {
      color: green;
    }
  </style>
</head>
<body>
  <h1>Google Maps API Test</h1>
  
  <div class="controls">
    <label for="api-key">API Key:</label>
    <input type="text" id="api-key" style="width: 300px;" placeholder="Enter Google Maps API Key">
    <button id="load-map">Load Map</button>
    <button id="test-geocode">Test Geocoding</button>
  </div>
  
  <div id="map"></div>
  
  <div class="log" id="log"></div>
  
  <script>
    // Log function
    function log(message, isError = false) {
      const logElement = document.getElementById('log');
      const entry = document.createElement('div');
      entry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
      entry.className = isError ? 'error' : 'success';
      logElement.appendChild(entry);
      console.log(message);
    }
    
    // Load the map
    document.getElementById('load-map').addEventListener('click', function() {
      const apiKey = document.getElementById('api-key').value;
      if (!apiKey) {
        log('Please enter an API key', true);
        return;
      }
      
      log(`Loading Google Maps with API key: ${apiKey.substring(0, 5)}...`);
      
      // Remove any existing script
      const existingScript = document.getElementById('google-maps-script');
      if (existingScript) {
        existingScript.remove();
      }
      
      // Create a new script element
      const script = document.createElement('script');
      script.id = 'google-maps-script';
      script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&callback=initMap`;
      script.async = true;
      script.defer = true;
      script.onerror = function() {
        log('Failed to load Google Maps API', true);
      };
      
      document.head.appendChild(script);
    });
    
    // Test geocoding
    document.getElementById('test-geocode').addEventListener('click', function() {
      const apiKey = document.getElementById('api-key').value;
      if (!apiKey) {
        log('Please enter an API key', true);
        return;
      }
      
      log(`Testing geocoding with API key: ${apiKey.substring(0, 5)}...`);
      
      // Test location
      const location = 'Woking, UK';
      
      // Make a direct API call to the Geocoding API
      fetch(`https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(location)}&key=${apiKey}`)
        .then(response => response.json())
        .then(data => {
          log(`Geocoding response for "${location}": ${JSON.stringify(data)}`);
          
          if (data.status === 'OK') {
            log(`Successfully geocoded "${location}"`);
            log(`Coordinates: ${JSON.stringify(data.results[0].geometry.location)}`);
            
            // If we have a map, add a marker
            if (window.map) {
              const position = data.results[0].geometry.location;
              new google.maps.Marker({
                position,
                map: window.map,
                title: location
              });
              
              // Center the map on the marker
              window.map.setCenter(position);
            }
          } else {
            log(`Failed to geocode "${location}": ${data.status}`, true);
          }
        })
        .catch(error => {
          log(`Error geocoding "${location}": ${error}`, true);
        });
    });
    
    // Initialize the map
    window.initMap = function() {
      log('Google Maps API loaded successfully');
      
      try {
        // Create the map
        window.map = new google.maps.Map(document.getElementById('map'), {
          center: { lat: 51.5074, lng: -0.1278 }, // London
          zoom: 8
        });
        
        log('Map initialized successfully');
      } catch (error) {
        log(`Error initializing map: ${error}`, true);
      }
    };
    
    // Auto-fill API key from URL if present
    window.onload = function() {
      const urlParams = new URLSearchParams(window.location.search);
      const apiKey = urlParams.get('key');
      if (apiKey) {
        document.getElementById('api-key').value = apiKey;
        log(`API key loaded from URL: ${apiKey.substring(0, 5)}...`);
      }
    };
  </script>
</body>
</html>