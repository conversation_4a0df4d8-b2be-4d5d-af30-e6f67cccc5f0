/**
 * Stripe Configuration
 * 
 * This file centralizes all Stripe configuration to ensure consistency across the application.
 * IMPORTANT: Never hardcode API keys in this file. Always use environment variables.
 */

// Get Stripe public key from environment variables
export const STRIPE_PUBLIC_KEY = import.meta.env.VITE_STRIPE_PUBLIC_KEY;

// Platform fee percentage
export const PLATFORM_FEE_PERCENTAGE = Number(import.meta.env.VITE_PLATFORM_FEE_PERCENTAGE || 20);

// API URL for Stripe server
export const STRIPE_API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

// Stripe API version
export const STRIPE_API_VERSION = '2025-03-31.basil';

// Validate configuration
if (!STRIPE_PUBLIC_KEY) {
  console.error('Missing Stripe public key. Check your environment variables.');
}

// Log configuration (without exposing full keys)
if (STRIPE_PUBLIC_KEY) {
  console.log('Stripe public key loaded:', `${STRIPE_PUBLIC_KEY.substring(0, 7)}...${STRIPE_PUBLIC_KEY.substring(STRIPE_PUBLIC_KEY.length - 4)}`);
}
console.log('Platform fee percentage:', PLATFORM_FEE_PERCENTAGE);
console.log('Stripe API URL:', STRIPE_API_URL);
console.log('Stripe API version:', STRIPE_API_VERSION);

// Export default configuration object
export default {
  publicKey: STRIPE_PUBLIC_KEY,
  platformFeePercentage: PLATFORM_FEE_PERCENTAGE,
  apiUrl: STRIPE_API_URL,
  apiVersion: STRIPE_API_VERSION,
};
