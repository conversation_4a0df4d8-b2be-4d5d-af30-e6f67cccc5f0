import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { MapPin, Calendar, PoundSterling, ChevronRight, AlertCircle } from "lucide-react";
import { format } from "date-fns";
import { Task } from "@/services/taskService";

interface PWATaskCardProps {
  task: Task;
}

const PWATaskCard: React.FC<PWATaskCardProps> = ({ task }) => {
  const navigate = useNavigate();

  // Function to get status badge variant
  const getStatusVariant = (status: string, visibility?: string) => {
    // Special case for admin review tasks
    if (visibility === 'admin' && status === 'open') {
      return 'bg-purple-100 text-purple-800 border-purple-200';
    }

    switch (status) {
      case 'open':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'interest':
      case 'questions':
        return 'bg-cyan-100 text-cyan-800 border-cyan-200';
      case 'offer':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'assigned':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'in_progress':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case 'completed':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'confirmed':
        return 'bg-teal-100 text-teal-800 border-teal-200';
      case 'pending_payment':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'admin_review':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Format status for display
  const formatStatus = (status: string, visibility?: string) => {
    // Special case for admin review tasks
    if (visibility === 'admin' && status === 'open') {
      return 'Admin Review';
    }

    switch (status) {
      case 'pending_payment':
        return 'Payment Required';
      case 'in_progress':
        return 'In Progress';
      case 'interest':
        return 'Interest Expressed';
      case 'questions':
        return 'Discussion Phase';
      case 'admin_review':
        return 'Admin Review';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  // Extract location display (just the town/city)
  const extractTownFromLocation = (loc: string): string => {
    if (!loc) return '';
    const parts = loc.split(',').map(part => part.trim());
    if (parts.length >= 2) {
      return parts[1]; // Usually the town/city is the second part
    }
    return loc;
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return 'No due date';
    try {
      return format(new Date(dateString), 'dd MMM yyyy');
    } catch (e) {
      return 'Invalid date';
    }
  };

  const handleClick = () => {
    navigate(`/tasks/${task.id}`);
  };

  return (
    <Card
      className="border-l-4 shadow-sm cursor-pointer hover:bg-gray-50 transition-colors"
      style={{
        borderLeftColor: task.visibility === 'admin' && task.status === 'open' ? '#9333ea' : // Purple for admin review
                         task.status === 'open' ? '#22c55e' :
                         task.status === 'assigned' ? '#3b82f6' :
                         task.status === 'in_progress' ? '#6366f1' :
                         task.status === 'completed' ? '#a855f7' :
                         task.status === 'confirmed' ? '#14b8a6' :
                         task.status === 'pending_payment' ? '#eab308' : '#94a3b8'
      }}
      onClick={handleClick}
    >
      <CardContent className="p-3">
        <div className="flex justify-between items-start">
          <div className="flex-1 min-w-0">
            <div className="flex items-center">
              <h3 className="font-medium text-sm truncate">{task.title}</h3>
              {task.visibility === 'admin' && task.status === 'open' && !task.assigned_to && (
                <AlertCircle className="h-4 w-4 text-purple-600 ml-2 flex-shrink-0" />
              )}
            </div>
            <div className="flex items-center text-xs text-gray-500 mt-1">
              <MapPin size={12} className="mr-1 flex-shrink-0" />
              <span className="truncate">{extractTownFromLocation(task.location || '')}</span>
            </div>
            {task.due_date && (
              <div className="flex items-center text-xs text-gray-500 mt-1">
                <Calendar size={12} className="mr-1 flex-shrink-0" />
                {formatDate(task.due_date)}
              </div>
            )}
          </div>
          <div className="flex flex-col items-end ml-2">
            <Badge
              variant="outline"
              className={`text-xs px-2 py-0.5 ${getStatusVariant(task.status, task.visibility)}`}
            >
              {formatStatus(task.status, task.visibility)}
            </Badge>
            {task.budget > 0 && (
              <div className="flex items-center text-xs font-medium mt-1">
                <PoundSterling size={12} className="mr-0.5" />
                {task.budget.toFixed(2)}
              </div>
            )}
          </div>
        </div>
        <div className="flex justify-end mt-1">
          <ChevronRight className="h-4 w-4 text-gray-400" />
        </div>
      </CardContent>
    </Card>
  );
};

export default PWATaskCard;
