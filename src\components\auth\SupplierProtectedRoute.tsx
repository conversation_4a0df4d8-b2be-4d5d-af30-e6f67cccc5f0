import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import AccessDenied from '@/pages/AccessDenied';

interface SupplierProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
}

/**
 * A route component that only allows suppliers to access the content
 * Non-suppliers will see an access denied message
 */
const SupplierProtectedRoute: React.FC<SupplierProtectedRouteProps> = ({
  children,
  redirectTo = '/access-denied'
}) => {
  const { user, isSupplier, isLoading } = useAuth();

  // If auth is still loading, show nothing
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // If user is not logged in, redirect to login
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // If user is not a supplier, show access denied
  if (!isSupplier) {
    return <AccessDenied message="This page is only available to suppliers." />;
  }

  // User is a supplier, allow access
  return <>{children}</>;
};

export default SupplierProtectedRoute;
