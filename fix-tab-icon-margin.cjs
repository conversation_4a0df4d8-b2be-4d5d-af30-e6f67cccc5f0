// Script to fix the TabIcon margin issue in EnhancedPaymentProcessor.tsx
const fs = require('fs');
const path = require('path');

// Paths
const processorPath = path.join(__dirname, 'src', 'components', 'stripe', 'EnhancedPaymentProcessor.tsx');
const backupPath = path.join(__dirname, 'src', 'components', 'stripe', 'EnhancedPaymentProcessor.tsx.bak7');

// Create a backup of the original file
console.log('Creating backup of EnhancedPaymentProcessor.tsx...');
fs.copyFileSync(processorPath, backupPath);
console.log(`Backup created at ${backupPath}`);

// Read the file content
console.log('Reading EnhancedPaymentProcessor.tsx...');
let content = fs.readFileSync(processorPath, 'utf8');

// Fix the TabIcon margin issue
if (content.includes("'.TabIcon': {\n      margin: '0 8px 0 0',\n    },")) {
  console.log('TabIcon margin is already fixed.');
} else if (content.includes("'.TabIcon': {")) {
  console.log('Fixing TabIcon margin issue...');
  
  // Replace the TabIcon style
  content = content.replace(
    /['"]\.TabIcon['"]:\s*{[^}]*}/g,
    "'.TabIcon': {\n      marginRight: '8px'\n    }"
  );
  
  // Write the modified content back to the file
  fs.writeFileSync(processorPath, content, 'utf8');
  console.log('Fix applied successfully!');
  
  console.log('\nChanges made:');
  console.log('- Fixed TabIcon margin issue by using marginRight instead of margin');
  
  console.log('\nPlease refresh the page and try the payment again.');
} else {
  console.log('Could not find TabIcon style in the file.');
  console.log('Please apply the fix manually:');
  console.log('1. Open src/components/stripe/EnhancedPaymentProcessor.tsx');
  console.log('2. Find the TabIcon style and replace margin with marginRight');
}

console.log('\nIf you need to restore the original file, run:');
console.log(`cp ${backupPath} ${processorPath}`);