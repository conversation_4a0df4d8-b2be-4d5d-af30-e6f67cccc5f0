import { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { AdminTaskReview } from '@/components/admin/AdminTaskReview';
import { AdminAssignedTasks } from '@/components/admin/AdminAssignedTasks';
import { useAuth } from '@/contexts/AuthContext';
import { Navigate } from 'react-router-dom';

export default function AdminTasks() {
  const [activeTab, setActiveTab] = useState('pending');
  const { user, isAdmin } = useAuth();

  // Redirect non-admin users
  if (!user || !isAdmin) {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Task Management</h1>
          <p className="text-gray-500">
            Review and assign tasks created by teachers
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="pending">Pending Review</TabsTrigger>
            <TabsTrigger value="assigned">Assigned Tasks</TabsTrigger>
          </TabsList>
          <TabsContent value="pending" className="mt-6">
            <AdminTaskReview />
          </TabsContent>
          <TabsContent value="assigned" className="mt-6">
            <AdminAssignedTasks />
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
