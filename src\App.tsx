
import React from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate, useParams, useLocation } from "react-router-dom";
import { Suspense, useEffect, useState } from "react";

import MobileAppLayout from "@/layouts/MobileAppLayout";
import GetStreamChatList from "@/pages/mobile/GetStreamChatList";
import GetStreamChatView from "@/pages/mobile/GetStreamChatView";
import PWARoutes from "@/routes/PWARoutes";
import { isPWA } from "@/utils/pwa-utils";
import { AuthProvider } from "./contexts/AuthContext";
import { NotificationProvider } from "./contexts/NotificationContext";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import Dashboard from "./pages/Dashboard";
import Tasks from "./pages/Tasks";
import TaskWrapper from "./pages/TaskWrapper";
import EnhancedTaskWrapper from "./pages/EnhancedTaskWrapper";
import PostTask from "./pages/PostTask";
import Profile from "./pages/Profile";
import OrganizationSetup from "./pages/OrganizationSetup";
import AcceptInvitation from "./pages/AcceptInvitation";
import InvitationConfirmation from "./pages/InvitationConfirmation";
import SetAdminRole from "./pages/SetAdminRole";
import EmailConfig from "./pages/EmailConfig";
import Login from "./pages/Login";
import Register from "./pages/Register";
import HowItWorks from "./pages/HowItWorks";
import Plans from "./pages/Plans";
import Help from "./pages/Help";
import Contact from "./pages/Contact";
import MessagesDirect from "./pages/MessagesDirect";
import Notifications from "./pages/Notifications";
import Payments from "./pages/Payments";
import PublicDebug from "./pages/PublicDebug";
import SystemDebug from "./pages/SystemDebug";
import DebugChat from "./pages/DebugChat";
import PublicDebugChat from "./pages/PublicDebugChat";
import CreateTestUsers from "./pages/CreateTestUsers";
import DebugMessages from "./pages/DebugMessages";
import AdminUserManagement from "./pages/AdminUserManagement";
import AdminUsers from "./pages/AdminUsers";
import AdminTasks from "./pages/AdminTasks";
import AdminDashboard from "./pages/AdminDashboard";
import OrganizationDashboard from "./pages/OrganizationDashboard";
import RouteExplorer from "./pages/RouteExplorer";
import OrganizationUsersRedirect from "./pages/OrganizationUsersRedirect";
import StripeConnectPage from "./pages/StripeConnectPage";
import OrganizationInvoices from "./pages/organization/Invoices";
import TestTaskInvoiceFlow from "./pages/TestTaskInvoiceFlow";
import EmergencyTaskActions from "./pages/EmergencyTaskActions";
import InternalTask from "./pages/InternalTask";
import TestInternalTaskActions from "./pages/TestInternalTaskActions";

// New role-based components
import RoleProtectedRoute from "./components/auth/RoleProtectedRoute";
import SupplierProtectedRoute from "./components/auth/SupplierProtectedRoute";
import SiteAdminDashboard from "./pages/admin/SiteAdminDashboard";
import RoleManagement from "./pages/admin/RoleManagement";
import SecurityDashboard from "./pages/admin/SecurityDashboard";
import AccessDenied from "./pages/AccessDenied";
import RoleDebug from "./pages/debug/RoleDebug";
import DebugLinks from "./pages/debug/DebugLinks";
import TaskTypeDebug from "./pages/debug/TaskTypeDebug";
import ChatDebug from "./pages/debug/ChatDebug";
import GetStreamDebug from "./pages/admin/GetStreamDebug";
import AssignedTasks from "./pages/AssignedTasks";
// Use a try-catch to handle potential import errors
const PWADebug = React.lazy(() =>
  import("./pages/debug/PWADebug")
    .catch(() => {
      console.error("Failed to load PWADebug component");
      return { default: () => <div>PWA Debug not available</div> };
    })
);

const queryClient = new QueryClient();

// Component to redirect from /tasks/:id to /tasks/enhanced/:id
const TaskRedirect = () => {
  const { id } = useParams();
  const location = useLocation();
  return <Navigate to={`/tasks/enhanced/${id}${location.search}`} replace />;
};

// Component to redirect from /mobile/chat/:threadId to /mobile/stream-chat/:threadId
const ChatRedirect = () => {
  const { threadId } = useParams();
  const location = useLocation();
  return <Navigate to={`/mobile/stream-chat/${threadId}${location.search}`} replace />;
};

const AppWithProviders = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <AuthProvider>
        <NotificationProvider>
          <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/tasks" element={
            <SupplierProtectedRoute>
              <Tasks />
            </SupplierProtectedRoute>
          } />
          <Route path="/tasks/create" element={<PostTask />} />
          <Route path="/tasks/my-tasks" element={<Dashboard />} />
          <Route path="/tasks/assigned" element={<AssignedTasks />} />
          {/* Redirect from original Task page to Enhanced Task page */}
          <Route path="/tasks/:id" element={<TaskRedirect />} />

          {/* Enhanced Task page with new architecture */}
          <Route path="/tasks/enhanced/:id" element={<EnhancedTaskWrapper />} />

          {/* Redirect from /enhancedtask/:id to /tasks/enhanced/:id */}
          <Route path="/enhancedtask/:id" element={<TaskRedirect />} />
          <Route path="/post-task" element={<PostTask />} />
          <Route path="/profile/:id" element={<Profile />} />
          <Route path="/organization/setup" element={<OrganizationSetup />} />
          <Route path="/supplier/onboarding" element={
            <React.Suspense fallback={<div className="flex items-center justify-center h-screen">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>}>
              {React.createElement(React.lazy(() => import('./pages/SupplierOnboarding')))}
            </React.Suspense>
          } />

          {/* Organization Management Routes - Only for organization admins */}
          <Route
            path="/organization/dashboard"
            element={
              <RoleProtectedRoute requiredPermission="manage_organizations">
                <OrganizationDashboard />
              </RoleProtectedRoute>
            }
          />
          <Route
            path="/organization/users"
            element={
              <RoleProtectedRoute requiredPermission="manage_organizations">
                <OrganizationUsersRedirect />
              </RoleProtectedRoute>
            }
          /> {/* Redirect old path */}
          <Route
            path="/organization/members"
            element={
              <RoleProtectedRoute requireSiteAdmin>
                <AdminUserManagement />
              </RoleProtectedRoute>
            }
          />
          <Route
            path="/organization/settings"
            element={
              <Navigate to="/organization/dashboard?tab=settings" replace />
            }
          />
          <Route
            path="/organization/invoices"
            element={
              <RoleProtectedRoute requiredPermission="manage_payments">
                <OrganizationInvoices />
              </RoleProtectedRoute>
            }
          />

          {/* Site Admin Routes */}
          <Route
            path="/admin/site"
            element={
              <RoleProtectedRoute requireSiteAdmin>
                <SiteAdminDashboard />
              </RoleProtectedRoute>
            }
          />

          <Route
            path="/admin/roles"
            element={
              <RoleProtectedRoute requireSiteAdmin>
                <RoleManagement />
              </RoleProtectedRoute>
            }
          />

          <Route
            path="/admin/security"
            element={
              <RoleProtectedRoute requireSiteAdmin>
                <SecurityDashboard />
              </RoleProtectedRoute>
            }
          />

          {/* System Management Routes - Only for site admins */}
          <Route
            path="/admin"
            element={
              <RoleProtectedRoute requireSiteAdmin>
                <AdminDashboard />
              </RoleProtectedRoute>
            }
          />
          <Route
            path="/admin/users"
            element={
              <RoleProtectedRoute requireSiteAdmin>
                <AdminUsers />
              </RoleProtectedRoute>
            }
          />
          <Route
            path="/admin/tasks"
            element={
              <RoleProtectedRoute requiredPermission="manage_tasks">
                <AdminTasks />
              </RoleProtectedRoute>
            }
          />
          <Route
            path="/admin/email"
            element={
              <RoleProtectedRoute requireSiteAdmin>
                <EmailConfig />
              </RoleProtectedRoute>
            }
          />
          <Route
            path="/admin/getstream-debug"
            element={
              <RoleProtectedRoute requireSiteAdmin>
                <GetStreamDebug />
              </RoleProtectedRoute>
            }
          />

          {/* Authentication Routes */}
          <Route path="/invitation/accept" element={<AcceptInvitation />} />
          <Route path="/invitation-confirmation" element={<InvitationConfirmation />} />
          <Route path="/set-admin-role" element={<SetAdminRole />} />
          <Route path="/email-config" element={<EmailConfig />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/forgot-password" element={
            <React.Suspense fallback={<div className="flex items-center justify-center h-screen">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>}>
              {React.createElement(React.lazy(() => import('./pages/ForgotPassword')))}
            </React.Suspense>
          } />
          {/* Auth callback handler - processes all Supabase auth redirects */}
          <Route path="/auth/callback" element={
            <React.Suspense fallback={<div className="flex items-center justify-center h-screen">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>}>
              {React.createElement(React.lazy(() => import('./pages/AuthCallback')))}
            </React.Suspense>
          } />

          {/* Password reset routes */}
          <Route path="/reset-password" element={
            <React.Suspense fallback={<div className="flex items-center justify-center h-screen">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>}>
              {React.createElement(React.lazy(() => import('./pages/ResetPassword')))}
            </React.Suspense>
          } />

          {/* Password reset confirmation page */}
          <Route path="/reset-password/confirm" element={
            <React.Suspense fallback={<div className="flex items-center justify-center h-screen">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>}>
              {React.createElement(React.lazy(() => import('./pages/ResetPasswordConfirm')))}
            </React.Suspense>
          } />

          {/* Direct password reset page - handles token directly */}
          <Route path="/reset-password/direct" element={
            <React.Suspense fallback={<div className="flex items-center justify-center h-screen">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>}>
              {React.createElement(React.lazy(() => import('./pages/ResetPasswordDirect')))}
            </React.Suspense>
          } />

          {/* Public Routes */}
          <Route path="/how-it-works" element={<HowItWorks />} />
          <Route path="/plans" element={<Plans />} />
          <Route path="/help" element={<Help />} />
          <Route path="/contact" element={<Contact />} />

          {/* User Routes */}
          {/* IMPORTANT: All chat functionality should use GetStream implementation via the Dashboard messages tab */}
          <Route path="/messages" element={<Navigate to="/dashboard?tab=messages" replace />} />
          <Route path="/notifications" element={<Notifications />} />
          <Route
            path="/payments"
            element={
              <RoleProtectedRoute requiredPermission="manage_payments">
                <Payments />
              </RoleProtectedRoute>
            }
          />
          <Route path="/stripe-connect" element={<StripeConnectPage />} />

          {/* Access Denied Page */}
          <Route path="/access-denied" element={<AccessDenied />} />

          {/* Mobile App Routes */}
          <Route path="/mobile" element={<MobileAppLayout />}>
            <Route path="chats" element={<GetStreamChatList />} />
            <Route path="stream-chat/:channelId" element={<GetStreamChatView />} />
          </Route>

          {/* Debug Routes */}
          <Route path="/debug" element={<PublicDebug />} />
          <Route path="/system-debug" element={<SystemDebug />} />
          <Route path="/debug-chat" element={<DebugChat />} />
          <Route path="/public-debug-chat" element={<PublicDebugChat />} />
          <Route path="/create-test-users" element={<CreateTestUsers />} />
          <Route path="/debug-messages" element={<DebugMessages />} />
          <Route path="/route-explorer" element={<RouteExplorer />} />
          <Route path="/test-task-invoice-flow" element={<TestTaskInvoiceFlow />} />
          <Route path="/debug/role" element={<RoleDebug />} />
          <Route path="/debug/links" element={<DebugLinks />} />
          <Route path="/debug/task-types" element={<TaskTypeDebug />} />
          <Route path="/debug/chat" element={<ChatDebug />} />
          <Route path="/debug/pwa" element={
            <React.Suspense fallback={<div className="flex items-center justify-center h-screen">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>}>
              <PWADebug />
            </React.Suspense>
          } />

          {/* Emergency Routes */}
          <Route path="/emergency/task/:id" element={<EmergencyTaskActions />} />
          <Route path="/internal-task/:id" element={<InternalTask />} />
          <Route path="/test-component/:id" element={<TestInternalTaskActions />} />

          {/* Catch-all Route */}
          <Route path="*" element={<NotFound />} />
        </Routes>

          {/* Mobile navigation is now handled by MobileAppLayout */}
        </NotificationProvider>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

const App = () => {
  console.log('Debug: App component rendered');
  const [isPwaMode, setIsPwaMode] = useState(false);

  // Check if running as PWA
  useEffect(() => {
    setIsPwaMode(isPWA());
  }, []);

  return (
    <BrowserRouter>
      {isPwaMode ? (
        <QueryClientProvider client={queryClient}>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <AuthProvider>
              <NotificationProvider>
                <Suspense fallback={
                  <div className="flex items-center justify-center h-screen">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                }>
                  <PWARoutes />
                </Suspense>
              </NotificationProvider>
            </AuthProvider>
          </TooltipProvider>
        </QueryClientProvider>
      ) : (
        <AppWithProviders />
      )}
    </BrowserRouter>
  );
};

export default App;
